"""
Sentiment Analysis API System
----------------------------
Main entry point for the FastAPI application
"""

import logging
import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import Config
from app.core.database import MessageDatabase
from app.services.sentiment_service import SentimentAnalysisService
from app.services.async_sentiment_service import AsyncSentimentService
from app.services.huggingface_service import HuggingFaceService
from app.services.continuous_sentiment_processor import ContinuousSentimentProcessor
from app.services.daily_report_generator import DailyReportGenerator
from app.services.periodic_scheduler import PeriodicScheduler
from app.utils.website_validator import WebsiteValidator

# Set up logger
logger = logging.getLogger(__name__)

# Initialize configuration
config = Config()

# Get configuration
server_config = config.get_server_config()
model_config = config.get_model_config()
db_config = config.get_database_config()
processing_config = config.get_processing_config()
security_config = config.get_security_config()
daily_config = config.get_daily_sentiment_config()
CRISP_IDENTIFIER, CRISP_KEY, CRISP_WEBHOOK_SECRET, _, HUGGINGFACE_TOKEN = config.get_credentials()

# Initialize Hugging Face service and login
huggingface_service = HuggingFaceService.get_instance()
if HUGGINGFACE_TOKEN:
    if huggingface_service.login(HUGGINGFACE_TOKEN):
        logger.info("Successfully logged in to Hugging Face Hub")
    else:
        logger.warning("Failed to log in to Hugging Face Hub. Some features may not work correctly.")
else:
    logger.warning("No Hugging Face token provided. Some features may not work correctly.")

# Create the FastAPI application
app = FastAPI(
    title="Sentiment Analysis API",
    description="A webhook-based system for sentiment analysis using the Qwen/Qwen2.5-0.5B-Instruct model.",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Create the database and load data from disk if available
logger.info(f"Initializing database with save path: {db_config['save_path']}")
logger.info(f"Auto-save interval: {db_config['autosave_interval_sec']} seconds ({db_config['autosave_interval_sec']/60:.1f} minutes)")
logger.info(f"Conversation timeout: {db_config['conversation_timeout_hours']} hours")
logger.info(f"Cleanup interval: {db_config['cleanup_interval_hours']} hours")

db = MessageDatabase(
    save_path=db_config['save_path'],
    autosave_interval_sec=db_config['autosave_interval_sec'],
    conversation_timeout_hours=db_config['conversation_timeout_hours'],
    cleanup_interval_hours=db_config['cleanup_interval_hours']
)

logger.info("Database initialization complete")

# Create the sentiment service
sentiment_analyzer = SentimentAnalysisService(model_config, processing_config)
logger.info("Sentiment analysis service initialized")

# Create the async sentiment service
async_sentiment_analyzer = AsyncSentimentService(sentiment_analyzer)
logger.info("Async sentiment analysis service initialized")

# Initialize the website validator
website_validator = WebsiteValidator(config.config)
logger.info("Website validator initialized")

# Initialize daily sentiment services if enabled
continuous_processor = None
daily_generator = None
periodic_scheduler = None

if daily_config.get('enabled', True):
    logger.info("Initializing daily sentiment analysis services...")

    # Initialize continuous sentiment processor
    continuous_processor = ContinuousSentimentProcessor(daily_config, sentiment_analyzer, db)
    continuous_processor.update_processing_settings(processing_config)
    logger.info("Continuous sentiment processor initialized")

    # Initialize daily report generator
    daily_generator = DailyReportGenerator(daily_config, continuous_processor)
    logger.info("Daily report generator initialized")

    # Initialize and start periodic scheduler
    periodic_scheduler = PeriodicScheduler(daily_config, continuous_processor, daily_generator)
    periodic_scheduler.start()
    logger.info("Daily scheduler initialized and started")
else:
    logger.info("Daily sentiment analysis is disabled")

# Make services available to the application
import app as app_module
app_module.db = db
app_module.sentiment_analyzer = sentiment_analyzer
app_module.async_sentiment_analyzer = async_sentiment_analyzer
app_module.huggingface_service = huggingface_service
app_module.website_validator = website_validator
app_module.continuous_processor = continuous_processor
app_module.daily_generator = daily_generator
app_module.periodic_scheduler = periodic_scheduler

# Verify that the services are properly set
if app_module.db is None:
    raise RuntimeError("Failed to initialize database service")

if app_module.sentiment_analyzer is None:
    raise RuntimeError("Failed to initialize sentiment analyzer")

if app_module.async_sentiment_analyzer is None:
    raise RuntimeError("Failed to initialize async sentiment analyzer")

if app_module.website_validator is None:
    raise RuntimeError("Failed to initialize website validator")

logger.info("Global service instances successfully initialized")

# Import and include API routers
from app.api.routes import router as api_router
from app.api.webhooks import router as webhook_router
from app.api.async_routes import router as async_router
from app.api.airflow_integration import router as airflow_router

app.include_router(api_router)
app.include_router(webhook_router)
app.include_router(async_router)
app.include_router(airflow_router)

# Shutdown event handler
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Server shutting down...")

    # Shut down periodic scheduler
    if periodic_scheduler:
        periodic_scheduler.stop()
        logger.info("Periodic scheduler shut down.")

    # Shut down the async sentiment analyzer
    if async_sentiment_analyzer:
        async_sentiment_analyzer.shutdown()
        logger.info("Async sentiment analyzer shut down.")

    # Shut down the sentiment analyzer
    if sentiment_analyzer:
        sentiment_analyzer.shutdown()
        logger.info("Sentiment analyzer shut down.")

    # Shut down the database
    if db:
        db.shutdown()
        logger.info("Database shut down.")

    logger.info("Server shutdown complete.")

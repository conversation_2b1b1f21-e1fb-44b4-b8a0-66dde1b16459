#!/usr/bin/env python3
"""
Demo script showing complete flow: Webhook → Agent Extraction → Database → PostgreSQL
"""

import sys
import os
import json
import time
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.api.webhooks import _extract_agent_information
from app.models.schemas import DailySentimentRecord


def demo_webhook_processing():
    """Demo webhook processing with real-world examples"""
    print("🔄 DEMO: Webhook Processing with Agent Extraction")
    print("=" * 60)
    
    # Real-world webhook examples
    webhook_examples = [
        {
            "name": "Human Agent - Hannah Mai",
            "data": {
                "event": "message:received",
                "website_id": "f980a7d7-c586-4c05-b59f-068b40f0055d",
                "data": {
                    "session_id": "session_4b4b3ebf-de8e-4a10-90a7-4a74fb1cadaa",
                    "type": "text",
                    "content": "Thank you for your patience! The OTP requirement has been removed as per your request.",
                    "from": "operator",
                    "origin": "chat",
                    "timestamp": int(time.time()) - 3600,
                    "fingerprint": 174721378091742,
                    "user": {
                        "nickname": "<PERSON> <PERSON>",
                        "user_id": "7ae4d982-2b89-42c4-830c-960e3a9d1b9a"
                    }
                }
            }
        },
        {
            "name": "Bot Assistant",
            "data": {
                "event": "message:received", 
                "website_id": "f980a7d7-c586-4c05-b59f-068b40f0055d",
                "data": {
                    "session_id": "session_712dbf7a-d0c9-469d-bc3f-2a437c0c5eb4",
                    "type": "text",
                    "content": "Hi there. Thank you for reaching UpPromote support.",
                    "from": "operator",
                    "origin": "urn:crisp.im:bot:0",
                    "timestamp": int(time.time()) - 2400,
                    "fingerprint": 174720859416778,
                    "user": {
                        "nickname": "Krongkaew",
                        "user_id": "session_712dbf7a-d0c9-469d-bc3f-2a437c0c5eb4"
                    }
                }
            }
        },
        {
            "name": "Human Agent - Tommy Nguyen",
            "data": {
                "event": "message:received",
                "website_id": "f980a7d7-c586-4c05-b59f-068b40f0055d", 
                "data": {
                    "session_id": "session_85cba0e8-042b-4181-9b1a-860eb48c84ed",
                    "type": "text",
                    "content": "Hi there, this is Tommy from UpPromote Support. I hope you are having a great day!",
                    "from": "operator",
                    "origin": "chat",
                    "timestamp": int(time.time()) - 1800,
                    "fingerprint": 174720796045523,
                    "metadata": {
                        "agent_name": "Tommy Nguyen",
                        "agent_id": "tommy-support-001"
                    },
                    "user": {
                        "nickname": "Tommy Nguyen",
                        "user_id": "891e6c13-599a-458a-b6a0-99563c99639a"
                    }
                }
            }
        },
        {
            "name": "Customer Message",
            "data": {
                "event": "message:received",
                "website_id": "f980a7d7-c586-4c05-b59f-068b40f0055d",
                "data": {
                    "session_id": "session_3e987922-f32b-489f-b568-8e2c568e4d09",
                    "type": "text", 
                    "content": "Hello there I need some help",
                    "from": "user",
                    "origin": "chat",
                    "timestamp": int(time.time()) - 3000,
                    "fingerprint": 174721423901453,
                    "user": {
                        "nickname": "Ankur",
                        "user_id": "session_3e987922-f32b-489f-b568-8e2c568e4d09"
                    }
                }
            }
        }
    ]
    
    processed_data = []
    
    for example in webhook_examples:
        print(f"\n📨 Processing: {example['name']}")
        print("-" * 40)
        
        # Extract agent information
        enhanced_data = _extract_agent_information(example['data'])
        agent_info = enhanced_data['data']['agent_info']
        
        # Display extracted information
        print(f"   Role: {agent_info['role']}")
        print(f"   Name: {agent_info.get('name', 'N/A')}")
        print(f"   User ID: {agent_info.get('user_id', 'N/A')}")
        print(f"   Origin: {agent_info.get('origin', 'N/A')}")
        
        processed_data.append(enhanced_data)
        print("   ✅ Agent information extracted successfully")
    
    return processed_data


def demo_postgresql_record_creation():
    """Demo PostgreSQL record creation"""
    print("\n\n🗄️  DEMO: PostgreSQL Record Creation")
    print("=" * 60)
    
    # Create sample daily sentiment records
    base_time = int(time.time())
    
    records = [
        DailySentimentRecord(
            website_id="f980a7d7-c586-4c05-b59f-068b40f0055d",
            session_id="session_4b4b3ebf-de8e-4a10-90a7-4a74fb1cadaa",
            user_nickname="Customer A",
            user_id="customer-001",
            agent_name="Hannah Mai",
            agent_role="operator",
            conversation_start=base_time - 3600,
            conversation_end=base_time - 3000,
            is_completed=True,
            overall_emotion="positive",
            sentiment_shift="neutral_to_positive", 
            latest_sentiment="positive",
            message_count=8,
            analysis_timestamp=base_time
        ),
        DailySentimentRecord(
            website_id="f980a7d7-c586-4c05-b59f-068b40f0055d",
            session_id="session_712dbf7a-d0c9-469d-bc3f-2a437c0c5eb4",
            user_nickname="Krongkaew",
            user_id="customer-002",
            agent_name="Bot Assistant",
            agent_role="bot",
            conversation_start=base_time - 2400,
            conversation_end=base_time - 2100,
            is_completed=True,
            overall_emotion="neutral",
            sentiment_shift="stable",
            latest_sentiment="neutral",
            message_count=4,
            analysis_timestamp=base_time
        ),
        DailySentimentRecord(
            website_id="f980a7d7-c586-4c05-b59f-068b40f0055d",
            session_id="session_85cba0e8-042b-4181-9b1a-860eb48c84ed",
            user_nickname="Tommy Customer",
            user_id="customer-003",
            agent_name="Tommy Nguyen",
            agent_role="operator",
            conversation_start=base_time - 1800,
            conversation_end=None,  # Ongoing conversation
            is_completed=False,
            overall_emotion="neutral",
            sentiment_shift="stable",
            latest_sentiment="neutral",
            message_count=12,
            analysis_timestamp=base_time
        )
    ]
    
    print(f"📊 Created {len(records)} PostgreSQL records:")
    print()
    
    for i, record in enumerate(records, 1):
        print(f"Record {i}:")
        print(f"   🌐 Website: {record.website_id[:8]}...")
        print(f"   💬 Session: {record.session_id[:8]}...")
        print(f"   👤 User: {record.user_nickname}")
        print(f"   🎧 Agent: {record.agent_name} ({record.agent_role})")
        print(f"   ⏰ Duration: {(record.conversation_end or base_time) - record.conversation_start} seconds")
        print(f"   ✅ Completed: {record.is_completed}")
        print(f"   😊 Emotion: {record.overall_emotion}")
        print(f"   📈 Shift: {record.sentiment_shift}")
        print(f"   💬 Messages: {record.message_count}")
        print()
    
    return records


def demo_postgresql_query():
    """Demo PostgreSQL queries"""
    print("🔍 DEMO: PostgreSQL Query Examples")
    print("=" * 60)
    
    queries = [
        {
            "name": "Get all records for a specific date",
            "sql": """
SELECT * FROM daily_sentiment_reports 
WHERE report_date = '2025-05-26'
ORDER BY created_at DESC;
            """.strip()
        },
        {
            "name": "Get records by agent",
            "sql": """
SELECT agent_name, agent_role, COUNT(*) as conversations,
       AVG(message_count) as avg_messages
FROM daily_sentiment_reports 
WHERE report_date = '2025-05-26'
GROUP BY agent_name, agent_role
ORDER BY conversations DESC;
            """.strip()
        },
        {
            "name": "Get sentiment distribution",
            "sql": """
SELECT overall_emotion, COUNT(*) as count,
       ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM daily_sentiment_reports 
WHERE report_date = '2025-05-26'
GROUP BY overall_emotion
ORDER BY count DESC;
            """.strip()
        },
        {
            "name": "Get conversation completion stats",
            "sql": """
SELECT 
    is_completed,
    COUNT(*) as conversations,
    AVG(message_count) as avg_messages,
    AVG(conversation_end - conversation_start) as avg_duration_seconds
FROM daily_sentiment_reports 
WHERE report_date = '2025-05-26'
GROUP BY is_completed;
            """.strip()
        }
    ]
    
    for query in queries:
        print(f"\n📋 {query['name']}:")
        print("-" * 40)
        print(query['sql'])
        print()
    
    return queries


def demo_data_flow_summary():
    """Demo complete data flow summary"""
    print("📈 DEMO: Complete Data Flow Summary")
    print("=" * 60)
    
    flow_steps = [
        {
            "step": "1. Webhook Received",
            "description": "Customer/Agent message arrives via webhook",
            "data": "Raw webhook JSON with message content"
        },
        {
            "step": "2. Agent Extraction", 
            "description": "Extract agent information from webhook data",
            "data": "Enhanced webhook with agent_info field"
        },
        {
            "step": "3. Database Storage",
            "description": "Store message with agent info in local database",
            "data": "Message with agent_info in JSON structure"
        },
        {
            "step": "4. Sentiment Analysis",
            "description": "Periodic processing analyzes conversation sentiment",
            "data": "ConversationSentiment with agent list"
        },
        {
            "step": "5. Daily Report Generation",
            "description": "Generate daily records for each agent-conversation pair",
            "data": "DailySentimentRecord objects"
        },
        {
            "step": "6. PostgreSQL Upload",
            "description": "Upload records to PostgreSQL (append-only)",
            "data": "Structured records in PostgreSQL table"
        }
    ]
    
    for step in flow_steps:
        print(f"\n{step['step']}: {step['description']}")
        print(f"   📄 Data: {step['data']}")
    
    print(f"\n🎯 Result: Complete conversation tracking with agent attribution")
    print(f"   • Agent names and roles properly identified")
    print(f"   • Conversation timing and completion status")
    print(f"   • Sentiment analysis results")
    print(f"   • All data stored in PostgreSQL for reporting")


def main():
    """Run complete demo"""
    print("🚀 WEBHOOK TO POSTGRESQL COMPLETE DEMO")
    print("=" * 80)
    print("Demonstrating complete flow from webhook to PostgreSQL storage")
    print("=" * 80)
    
    try:
        # Demo 1: Webhook processing
        processed_data = demo_webhook_processing()
        
        # Demo 2: PostgreSQL record creation
        records = demo_postgresql_record_creation()
        
        # Demo 3: PostgreSQL queries
        queries = demo_postgresql_query()
        
        # Demo 4: Data flow summary
        demo_data_flow_summary()
        
        print("\n" + "=" * 80)
        print("✅ DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print(f"📊 Summary:")
        print(f"   • Processed {len(processed_data)} webhook messages")
        print(f"   • Created {len(records)} PostgreSQL records")
        print(f"   • Demonstrated {len(queries)} query examples")
        print(f"   • All agent information properly extracted and stored")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Hugging Face API Routes
---------------------
API routes for interacting with Hugging Face Hub
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

import app
from app.services.huggingface_service import HuggingFaceService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/huggingface",
    tags=["huggingface"],
    responses={404: {"description": "Not found"}},
)

class HuggingFaceStatusResponse(BaseModel):
    """Response model for Hugging Face status"""
    is_logged_in: bool
    message: str

class ModelInfoResponse(BaseModel):
    """Response model for model info"""
    id: str
    sha: Optional[str] = None
    last_modified: Optional[str] = None
    tags: Optional[List[str]] = None
    pipeline_tag: Optional[str] = None
    siblings: Optional[List[Dict[str, Any]]] = None
    private: Optional[bool] = None

@router.get("/status", response_model=HuggingFaceStatusResponse)
async def get_huggingface_status():
    """Get Hugging Face login status"""
    try:
        hf_service = app.huggingface_service
        is_logged_in = HuggingFaceService._is_logged_in
        
        if is_logged_in:
            return HuggingFaceStatusResponse(
                is_logged_in=True,
                message="Successfully logged in to Hugging Face Hub"
            )
        else:
            return HuggingFaceStatusResponse(
                is_logged_in=False,
                message="Not logged in to Hugging Face Hub"
            )
    except Exception as e:
        logger.error(f"Error getting Hugging Face status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting Hugging Face status: {str(e)}")

@router.post("/login", response_model=HuggingFaceStatusResponse)
async def login_to_huggingface(token: Optional[str] = None, force: bool = False):
    """
    Login to Hugging Face Hub
    
    Args:
        token: Hugging Face token (if None, will use token from config)
        force: Whether to force login even if already logged in
    """
    try:
        hf_service = app.huggingface_service
        success = hf_service.login(token, force)
        
        if success:
            return HuggingFaceStatusResponse(
                is_logged_in=True,
                message="Successfully logged in to Hugging Face Hub"
            )
        else:
            return HuggingFaceStatusResponse(
                is_logged_in=False,
                message="Failed to log in to Hugging Face Hub"
            )
    except Exception as e:
        logger.error(f"Error logging in to Hugging Face Hub: {e}")
        raise HTTPException(status_code=500, detail=f"Error logging in to Hugging Face Hub: {str(e)}")

@router.get("/model/{repo_id}", response_model=ModelInfoResponse)
async def get_model_info(repo_id: str):
    """
    Get information about a model
    
    Args:
        repo_id: Repository ID (e.g., "meta-llama/Llama-3.2-1B")
    """
    try:
        hf_service = app.huggingface_service
        
        # Check if logged in
        if not HuggingFaceService._is_logged_in:
            hf_service.login()
            
        # Get model info
        model_info = hf_service.get_model_info(repo_id)
        
        if model_info is None:
            raise HTTPException(status_code=404, detail=f"Model {repo_id} not found")
            
        # Convert model info to response model
        return ModelInfoResponse(
            id=model_info.id,
            sha=model_info.sha,
            last_modified=model_info.last_modified,
            tags=model_info.tags,
            pipeline_tag=model_info.pipeline_tag,
            siblings=[s.to_dict() for s in model_info.siblings],
            private=model_info.private
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model info for {repo_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting model info: {str(e)}")

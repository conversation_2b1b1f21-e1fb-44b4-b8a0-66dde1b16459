#!/bin/bash

# Make all scripts executable
# Usage: ./scripts/make-executable.sh

echo "Making all scripts executable..."

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Make all shell scripts executable
find "$SCRIPT_DIR" -name "*.sh" -type f -exec chmod +x {} \;

echo "✓ Made all .sh files in scripts/ executable"

# Make specific scripts executable
chmod +x "$SCRIPT_DIR/../run_ngrok.py" 2>/dev/null || true
chmod +x "$SCRIPT_DIR/../main.py" 2>/dev/null || true

echo "✓ Made Python scripts executable"

# List all executable files
echo ""
echo "Executable files in scripts/:"
find "$SCRIPT_DIR" -type f -executable -exec basename {} \;

echo ""
echo "All scripts are now executable!"

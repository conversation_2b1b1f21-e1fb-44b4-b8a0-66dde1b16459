apiVersion: v1
kind: ServiceAccount
metadata:
  name: airflow-worker
  namespace: airflow-tasks
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: airflow-worker-role
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log", "pods/exec"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: airflow-worker-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: airflow-worker-role
subjects:
- kind: ServiceAccount
  name: airflow-worker
  namespace: airflow-tasks
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: sentiment-analysis-sa
  namespace: sentiment-analysis
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: sentiment-analysis
  name: sentiment-analysis-role
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "services"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: sentiment-analysis-binding
  namespace: sentiment-analysis
subjects:
- kind: ServiceAccount
  name: sentiment-analysis-sa
  namespace: sentiment-analysis
roleRef:
  kind: Role
  name: sentiment-analysis-role
  apiGroup: rbac.authorization.k8s.io

# Webhook Agent Extraction Implementation Summary

## Tổng quan

Đã thành công cập nhật hệ thống webhook để trích xuất và lưu trữ thông tin agent từ webhook data, đ<PERSON><PERSON> bảo tất cả các trường dữ liệu cần thiết được thu thập cho PostgreSQL.

## C<PERSON>c thay đổi chính

### 1. Cậ<PERSON> nhật Webhook Handler (app/api/webhooks.py)

#### Thêm chức năng trích xuất agent:
```python
def _extract_agent_information(webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract and enhance webhook data with agent information"""
    
def _get_agent_info_from_webhook(webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract agent information from various webhook sources"""
```

#### <PERSON><PERSON><PERSON> phương thức trích xuất agent:
1. **Method 1**: Từ trường `user` (nếu chứa thông tin agent)
2. **Method 2**: Từ trường `origin` (cho bot messages)
3. **Method 3**: Từ trường `operator` (nếu tồn tại)
4. **Method 4**: Từ trường `metadata` (custom fields)

#### Xử lý các loại agent:
- **Human Agents**: Trích xuất tên và ID từ user info
- **Bot Agents**: Nhận diện từ origin pattern `urn:crisp.im:bot:`
- **Fallback**: Sử dụng "Support Agent" làm tên mặc định

### 2. Cập nhật Database Storage (app/core/database.py)

#### Lưu trữ thông tin agent:
```python
# Extract agent information if available
agent_info = message["data"].get("agent_info", {})

# Add agent information to message
mes = {
    # ... existing fields ...
    "agent_info": agent_info  # Add agent information
}
```

#### Cấu trúc agent_info:
```python
{
    "name": "Agent Name",
    "role": "operator|bot|user", 
    "user_id": "agent-id",
    "origin": "chat|bot"
}
```

### 3. Cập nhật Continuous Processor (app/services/continuous_sentiment_processor.py)

#### Trích xuất agent từ messages:
```python
# Extract agent information from messages
agents = []
agent_info_map = {}  # Map to track unique agents

for msg in messages_list:
    if msg.get('role') == 'operator':
        # Get agent info from the message
        agent_info = msg.get('agent_info', {})
        
        # Extract agent details
        agent_name = agent_info.get('name')
        agent_role = agent_info.get('role', 'operator')
        
        # Create unique agents list
        if agent_key not in agent_info_map:
            agents.append(AgentInfo(name=agent_name, role=agent_role))
```

## Cấu trúc dữ liệu

### Webhook Data Structure (Enhanced)
```json
{
  "event": "message:received",
  "website_id": "test-website",
  "data": {
    "session_id": "session-123",
    "type": "text",
    "content": "Hello, how can I help?",
    "from": "operator",
    "origin": "chat",
    "timestamp": 1748248270,
    "fingerprint": 123456789,
    "user": {
      "nickname": "Hannah Mai",
      "user_id": "agent-hannah-001"
    },
    "agent_info": {
      "name": "Hannah Mai",
      "role": "operator",
      "user_id": "agent-hannah-001",
      "origin": "chat"
    }
  }
}
```

### Database Message Structure
```json
{
  "type": "text",
  "origin": "chat",
  "text": "Hello, how can I help?",
  "timestamp": 1748248270,
  "fingerprint": 123456789,
  "role": "operator",
  "agent_info": {
    "name": "Hannah Mai",
    "role": "operator",
    "user_id": "agent-hannah-001",
    "origin": "chat"
  }
}
```

### PostgreSQL Record Structure
```json
{
  "website_id": "test-website",
  "session_id": "session-123",
  "user_nickname": "Customer Name",
  "user_id": "customer-123",
  "agent_name": "Hannah Mai",
  "agent_role": "operator",
  "conversation_start": 1748244670,
  "conversation_end": 1748248270,
  "is_completed": true,
  "overall_emotion": "positive",
  "sentiment_shift": "neutral_to_positive",
  "latest_sentiment": "positive",
  "message_count": 15,
  "analysis_timestamp": 1748248270
}
```

## Các loại Agent được hỗ trợ

### 1. Human Agents
- **Nguồn**: user.nickname, operator.nickname, metadata.agent_name
- **Ví dụ**: "Hannah Mai", "Tommy Nguyen", "Sarah Johnson"
- **Role**: "operator"

### 2. Bot Agents  
- **Nguồn**: origin pattern `urn:crisp.im:bot:`
- **Tên**: "Bot Assistant"
- **Role**: "bot"

### 3. User Messages
- **Role**: "user"
- **Agent info**: null (không có thông tin agent)

### 4. Fallback
- **Tên**: "Support Agent"
- **Role**: "operator"
- **Khi nào**: Không tìm thấy thông tin agent cụ thể

## Testing và Validation

### Test Scripts được tạo:
1. **`scripts/test_webhook_agent_extraction.py`**
   - Test trích xuất agent từ webhook
   - Test các scenario khác nhau
   - Test fallback mechanisms

2. **`scripts/test_full_integration.py`**
   - Test toàn bộ flow: Webhook → Database → PostgreSQL
   - Test data integrity
   - Test PostgreSQL record structure

### Test Results:
```
✓ Basic agent extraction test passed
✓ Bot agent extraction test passed  
✓ User message extraction test passed
✓ Metadata agent extraction test passed
✓ Fallback agent extraction test passed
✓ Database integration test passed
✓ Full integration test completed successfully!
```

## Lợi ích

### 1. Trích xuất thông tin agent đầy đủ
- Tên agent thực tế thay vì generic "Support Agent"
- Phân biệt được human agents và bots
- Lưu trữ agent ID để tracking

### 2. Tương thích với nhiều nguồn dữ liệu
- Hỗ trợ nhiều format webhook khác nhau
- Fallback mechanisms đảm bảo không bị lỗi
- Flexible extraction từ nhiều trường

### 3. Data integrity
- Append-only approach đảm bảo không mất dữ liệu
- Consistent data structure
- Proper error handling

### 4. PostgreSQL ready
- Tất cả trường dữ liệu cần thiết được thu thập
- Proper data types và validation
- Optimized for database storage

## Deployment Notes

### 1. Backward Compatibility
- Code mới tương thích với webhook data cũ
- Graceful fallback cho missing agent info
- No breaking changes

### 2. Performance
- Minimal overhead cho agent extraction
- Efficient data structure
- Optimized database queries

### 3. Monitoring
- Comprehensive logging cho debugging
- Error tracking và recovery
- Performance metrics

## Các bước tiếp theo

1. **Deploy lên staging environment**
2. **Test với webhook data thực tế**
3. **Monitor agent extraction accuracy**
4. **Fine-tune extraction logic nếu cần**
5. **Deploy lên production**
6. **Verify PostgreSQL data quality**

## Lưu ý quan trọng

1. **Debug logging**: Webhook data được log ở debug level để troubleshooting
2. **Error handling**: Extraction failures không ảnh hưởng đến webhook processing
3. **Data validation**: Tất cả fields được validate trước khi lưu PostgreSQL
4. **Agent deduplication**: Unique agents được track để tránh duplicate records

Hệ thống hiện tại đã sẵn sàng để trích xuất và lưu trữ đầy đủ thông tin agent từ webhook vào PostgreSQL với tất cả các trường dữ liệu yêu cầu.

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
export let errorRate = new Rate('errors');

// Test configuration
export let options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate must be below 10%
    errors: ['rate<0.1'],             // Custom error rate must be below 10%
  },
};

// Base URL from environment variable
const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';

// Test data
const testMessages = [
  {
    website_id: 'test-website-1',
    session_id: 'test-session-1',
    messages: [
      { role: 'user', content: 'Hello, I need help with my order' },
      { role: 'operator', content: 'Hi! I\'d be happy to help you with your order.' },
      { role: 'user', content: 'Thank you, that was very helpful!' }
    ]
  },
  {
    website_id: 'test-website-2',
    session_id: 'test-session-2',
    messages: [
      { role: 'user', content: 'I am very frustrated with this service' },
      { role: 'operator', content: 'I understand your frustration. Let me help you resolve this.' },
      { role: 'user', content: 'I appreciate your help, feeling much better now' }
    ]
  }
];

export default function () {
  // Test 1: Health check
  let healthResponse = http.get(`${BASE_URL}/health`);
  check(healthResponse, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 100ms': (r) => r.timings.duration < 100,
  }) || errorRate.add(1);

  sleep(1);

  // Test 2: API health check
  let apiHealthResponse = http.get(`${BASE_URL}/api/health`);
  check(apiHealthResponse, {
    'API health check status is 200': (r) => r.status === 200,
    'API health check response time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);

  sleep(1);

  // Test 3: Sentiment analysis request
  let testData = testMessages[Math.floor(Math.random() * testMessages.length)];
  
  let sentimentResponse = http.post(
    `${BASE_URL}/sentiment_widget/action`,
    JSON.stringify(testData),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  let sentimentCheck = check(sentimentResponse, {
    'sentiment analysis status is 200 or 202': (r) => r.status === 200 || r.status === 202,
    'sentiment analysis response time < 5000ms': (r) => r.timings.duration < 5000,
  });

  if (!sentimentCheck) {
    errorRate.add(1);
  } else if (sentimentResponse.status === 202) {
    // If processing is async, check status
    let responseBody = JSON.parse(sentimentResponse.body);
    if (responseBody.request_id) {
      sleep(2); // Wait a bit before checking status
      
      let statusResponse = http.get(`${BASE_URL}/sentiment_status/${responseBody.request_id}`);
      check(statusResponse, {
        'status check returns valid response': (r) => r.status === 200 || r.status === 202,
      }) || errorRate.add(1);
    }
  }

  sleep(2);

  // Test 4: Webhook endpoint (simulate webhook call)
  let webhookData = {
    website_id: testData.website_id,
    session_id: testData.session_id,
    event: 'message:received',
    data: {
      message: testData.messages[0]
    }
  };

  let webhookResponse = http.post(
    `${BASE_URL}/webhook`,
    JSON.stringify(webhookData),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  check(webhookResponse, {
    'webhook status is 200': (r) => r.status === 200,
    'webhook response time < 1000ms': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);

  sleep(1);
}

// Setup function (runs once at the beginning)
export function setup() {
  console.log(`Starting load test against ${BASE_URL}`);
  
  // Verify the service is available
  let response = http.get(`${BASE_URL}/health`);
  if (response.status !== 200) {
    throw new Error(`Service not available. Health check returned ${response.status}`);
  }
  
  console.log('Service is available, starting load test...');
  return { baseUrl: BASE_URL };
}

// Teardown function (runs once at the end)
export function teardown(data) {
  console.log('Load test completed');
  
  // Optional: Clean up test data
  // You can add cleanup logic here if needed
}

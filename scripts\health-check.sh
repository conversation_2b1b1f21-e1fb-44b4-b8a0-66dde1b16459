#!/bin/bash

# Health Check Script for Sentiment Analysis System
# Usage: ./health-check.sh [environment] [--detailed]

set -euo pipefail

# Configuration
ENVIRONMENT=${1:-staging}
DETAILED=${2:-}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Health check results
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((PASSED_CHECKS++))
    ((TOTAL_CHECKS++))
}

failure() {
    echo -e "${RED}[✗]${NC} $1"
    ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))
}

warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

# Determine URLs based on environment
case $ENVIRONMENT in
    "staging")
        BASE_URL="http://localhost:8080"
        GRAFANA_URL="http://localhost:3001"
        PROMETHEUS_URL="http://localhost:9091"
        COMPOSE_FILE="docker-compose.staging.yml"
        ;;
    "production")
        BASE_URL="http://localhost"
        GRAFANA_URL="http://localhost:3000"
        PROMETHEUS_URL="http://localhost:9090"
        # Determine which production environment is active
        if [ -f "$PROJECT_DIR/.current-production-env" ]; then
            CURRENT_ENV=$(cat "$PROJECT_DIR/.current-production-env")
            COMPOSE_FILE="docker-compose.production.$CURRENT_ENV.yml"
        else
            COMPOSE_FILE="docker-compose.production.blue.yml"
        fi
        ;;
    *)
        echo "Invalid environment. Use 'staging' or 'production'"
        exit 1
        ;;
esac

log "Starting health check for $ENVIRONMENT environment"
log "Using compose file: $COMPOSE_FILE"

# Check if Docker is running
check_docker() {
    log "Checking Docker service..."
    if systemctl is-active --quiet docker; then
        success "Docker service is running"
    else
        failure "Docker service is not running"
        return 1
    fi
}

# Check if Docker Compose file exists
check_compose_file() {
    log "Checking Docker Compose file..."
    if [ -f "$PROJECT_DIR/$COMPOSE_FILE" ]; then
        success "Docker Compose file exists: $COMPOSE_FILE"
    else
        failure "Docker Compose file not found: $COMPOSE_FILE"
        return 1
    fi
}

# Check container status
check_containers() {
    log "Checking container status..."
    cd "$PROJECT_DIR"
    
    local services=$(docker-compose -f "$COMPOSE_FILE" config --services)
    local all_healthy=true
    
    for service in $services; do
        if docker-compose -f "$COMPOSE_FILE" ps "$service" | grep -q "Up"; then
            if [ "$DETAILED" = "--detailed" ]; then
                success "Service $service is running"
            fi
        else
            failure "Service $service is not running"
            all_healthy=false
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        success "All containers are running"
    else
        failure "Some containers are not running"
        return 1
    fi
}

# Check application health endpoint
check_app_health() {
    log "Checking application health endpoint..."
    if curl -f -s "$BASE_URL/health" > /dev/null; then
        success "Application health endpoint is responding"
        
        if [ "$DETAILED" = "--detailed" ]; then
            local response=$(curl -s "$BASE_URL/health")
            echo "Response: $response"
        fi
    else
        failure "Application health endpoint is not responding"
        return 1
    fi
}

# Check API health endpoint
check_api_health() {
    log "Checking API health endpoint..."
    if curl -f -s "$BASE_URL/api/health" > /dev/null; then
        success "API health endpoint is responding"
    else
        failure "API health endpoint is not responding"
        return 1
    fi
}

# Check database connectivity
check_database() {
    log "Checking database connectivity..."
    cd "$PROJECT_DIR"
    
    if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
        success "Database is accessible"
        
        if [ "$DETAILED" = "--detailed" ]; then
            local db_info=$(docker-compose -f "$COMPOSE_FILE" exec -T postgres psql -U postgres -c "SELECT version();" 2>/dev/null | head -3 | tail -1)
            echo "Database version: $db_info"
        fi
    else
        failure "Database is not accessible"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log "Checking Redis connectivity..."
    cd "$PROJECT_DIR"
    
    if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping > /dev/null 2>&1; then
        success "Redis is accessible"
        
        if [ "$DETAILED" = "--detailed" ]; then
            local redis_info=$(docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli info server | grep redis_version)
            echo "Redis info: $redis_info"
        fi
    else
        failure "Redis is not accessible"
        return 1
    fi
}

# Check disk space
check_disk_space() {
    log "Checking disk space..."
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -lt 80 ]; then
        success "Disk usage is acceptable ($usage%)"
    elif [ "$usage" -lt 90 ]; then
        warning "Disk usage is high ($usage%)"
        ((TOTAL_CHECKS++))
    else
        failure "Disk usage is critical ($usage%)"
        return 1
    fi
}

# Check memory usage
check_memory() {
    log "Checking memory usage..."
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$usage" -lt 80 ]; then
        success "Memory usage is acceptable ($usage%)"
    elif [ "$usage" -lt 90 ]; then
        warning "Memory usage is high ($usage%)"
        ((TOTAL_CHECKS++))
    else
        failure "Memory usage is critical ($usage%)"
        return 1
    fi
}

# Check log files
check_logs() {
    log "Checking log files..."
    local log_dir="$PROJECT_DIR/logs"
    
    if [ -d "$log_dir" ]; then
        success "Log directory exists"
        
        if [ "$DETAILED" = "--detailed" ]; then
            local log_count=$(find "$log_dir" -name "*.log" | wc -l)
            echo "Number of log files: $log_count"
            
            # Check for recent errors
            local error_count=$(find "$log_dir" -name "*.log" -mtime -1 -exec grep -l "ERROR\|CRITICAL" {} \; 2>/dev/null | wc -l)
            if [ "$error_count" -gt 0 ]; then
                warning "Found $error_count log files with recent errors"
            fi
        fi
    else
        failure "Log directory does not exist"
        return 1
    fi
}

# Check SSL certificates (for production)
check_ssl() {
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Checking SSL certificates..."
        local ssl_dir="$PROJECT_DIR/nginx/ssl"
        
        if [ -d "$ssl_dir" ] && [ -f "$ssl_dir/cert.pem" ] && [ -f "$ssl_dir/key.pem" ]; then
            success "SSL certificates are present"
            
            if [ "$DETAILED" = "--detailed" ]; then
                local cert_expiry=$(openssl x509 -in "$ssl_dir/cert.pem" -noout -enddate 2>/dev/null | cut -d= -f2)
                echo "Certificate expires: $cert_expiry"
            fi
        else
            warning "SSL certificates not found (HTTP only)"
            ((TOTAL_CHECKS++))
        fi
    fi
}

# Check monitoring services
check_monitoring() {
    if [ "$DETAILED" = "--detailed" ]; then
        log "Checking monitoring services..."
        
        # Check Prometheus
        if curl -f -s "$PROMETHEUS_URL/api/v1/query?query=up" > /dev/null; then
            success "Prometheus is accessible"
        else
            warning "Prometheus is not accessible"
            ((TOTAL_CHECKS++))
        fi
        
        # Check Grafana
        if curl -f -s "$GRAFANA_URL/api/health" > /dev/null; then
            success "Grafana is accessible"
        else
            warning "Grafana is not accessible"
            ((TOTAL_CHECKS++))
        fi
    fi
}

# Check backup files
check_backups() {
    if [ "$DETAILED" = "--detailed" ]; then
        log "Checking backup files..."
        local backup_dir="$PROJECT_DIR/backups"
        
        if [ -d "$backup_dir" ]; then
            local recent_backups=$(find "$backup_dir" -name "backup_*" -mtime -1 | wc -l)
            if [ "$recent_backups" -gt 0 ]; then
                success "Recent backups found ($recent_backups files)"
            else
                warning "No recent backups found"
                ((TOTAL_CHECKS++))
            fi
        else
            warning "Backup directory does not exist"
            ((TOTAL_CHECKS++))
        fi
    fi
}

# Performance test
performance_test() {
    if [ "$DETAILED" = "--detailed" ]; then
        log "Running basic performance test..."
        
        local start_time=$(date +%s%N)
        if curl -f -s "$BASE_URL/health" > /dev/null; then
            local end_time=$(date +%s%N)
            local response_time=$(( (end_time - start_time) / 1000000 ))
            
            if [ "$response_time" -lt 500 ]; then
                success "Response time is good (${response_time}ms)"
            elif [ "$response_time" -lt 1000 ]; then
                warning "Response time is acceptable (${response_time}ms)"
                ((TOTAL_CHECKS++))
            else
                failure "Response time is slow (${response_time}ms)"
                return 1
            fi
        else
            failure "Performance test failed - endpoint not accessible"
            return 1
        fi
    fi
}

# Run all checks
main() {
    echo "========================================"
    echo "  Sentiment Analysis Health Check"
    echo "  Environment: $ENVIRONMENT"
    echo "  Time: $(date)"
    echo "========================================"
    echo
    
    # Basic checks
    check_docker || true
    check_compose_file || true
    check_containers || true
    check_app_health || true
    check_api_health || true
    check_database || true
    check_redis || true
    check_disk_space || true
    check_memory || true
    check_logs || true
    check_ssl || true
    
    # Detailed checks
    if [ "$DETAILED" = "--detailed" ]; then
        echo
        log "Running detailed checks..."
        check_monitoring || true
        check_backups || true
        performance_test || true
    fi
    
    # Summary
    echo
    echo "========================================"
    echo "  Health Check Summary"
    echo "========================================"
    echo "Total checks: $TOTAL_CHECKS"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    
    if [ "$FAILED_CHECKS" -eq 0 ]; then
        echo -e "${GREEN}Overall status: HEALTHY${NC}"
        exit 0
    else
        echo -e "${RED}Overall status: UNHEALTHY${NC}"
        exit 1
    fi
}

# Run main function
main

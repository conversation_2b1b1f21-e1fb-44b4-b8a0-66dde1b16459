apiVersion: v1
kind: ConfigMap
metadata:
  name: sentiment-analysis-config
  namespace: airflow-tasks
data:
  # Application configuration
  ENVIRONMENT: "kubernetes"
  LOG_LEVEL: "INFO"
  WORKERS: "2"
  
  # Database configuration
  POSTGRES_HOST: "postgres-service.sentiment-analysis.svc.cluster.local"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "sentiment_analysis"
  
  # Redis configuration
  REDIS_HOST: "redis-service.sentiment-analysis.svc.cluster.local"
  REDIS_PORT: "6379"
  
  # Monitoring configuration
  PROMETHEUS_ENABLED: "true"
  METRICS_PORT: "9090"
  
  # Airflow task configuration
  AIRFLOW_TASK_TIMEOUT: "3600"
  AIRFLOW_RETRY_DELAY: "300"
  AIRFLOW_MAX_RETRIES: "3"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: airflow-kubernetes-config
  namespace: airflow-tasks
data:
  # Kubernetes executor configuration
  worker_container_repository: "ghcr.io/your-org/sentiment-analysis"
  worker_container_tag: "latest"
  namespace: "airflow-tasks"
  worker_service_account_name: "airflow-worker"
  
  # Resource limits
  worker_cpu_request: "500m"
  worker_cpu_limit: "2000m"
  worker_memory_request: "1Gi"
  worker_memory_limit: "4Gi"
  
  # Pod configuration
  delete_worker_pods: "true"
  delete_worker_pods_on_failure: "false"
  worker_pods_creation_batch_size: "5"
  
  # Logging
  base_log_folder: "/opt/airflow/logs"
  logging_level: "INFO"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-config
  namespace: sentiment-analysis
data:
  postgresql.conf: |
    # PostgreSQL configuration for Kubernetes
    listen_addresses = '*'
    port = 5432
    max_connections = 100
    shared_buffers = 256MB
    effective_cache_size = 1GB
    maintenance_work_mem = 64MB
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    work_mem = 4MB
    min_wal_size = 1GB
    max_wal_size = 4GB
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'pg_log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
  
  pg_hba.conf: |
    # PostgreSQL Client Authentication Configuration File
    local   all             all                                     trust
    host    all             all             127.0.0.1/32            trust
    host    all             all             ::1/128                 trust
    host    all             all             0.0.0.0/0               md5

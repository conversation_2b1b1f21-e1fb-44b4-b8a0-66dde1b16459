#!/usr/bin/env python3
"""
Test script for PostgreSQL schema updates
"""

import sys
import os
import time
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.models.schemas import DailySentimentRecord, DailySentimentReport
from app.services.daily_report_generator import DailyReportGenerator
from app.services.postgresql_service import PostgreSQLService


def test_daily_sentiment_record_schema():
    """Test the updated DailySentimentRecord schema"""
    print("Testing DailySentimentRecord schema...")
    
    # Create a test record with all new fields
    record = DailySentimentRecord(
        website_id="test_website_123",
        session_id="session_456",
        user_nickname="TestUser",
        user_id="user_789",
        agent_name="<PERSON> Smith",
        agent_role="operator",
        conversation_start=int(time.time()) - 3600,  # 1 hour ago
        conversation_end=int(time.time()),  # now
        is_completed=True,
        overall_emotion="positive",
        sentiment_shift="neutral_to_positive",
        latest_sentiment="positive",
        message_count=15,
        analysis_timestamp=int(time.time())
    )
    
    print(f"✓ Created DailySentimentRecord: {record.session_id}")
    print(f"  - Conversation duration: {record.conversation_end - record.conversation_start} seconds")
    print(f"  - Message count: {record.message_count}")
    print(f"  - Completed: {record.is_completed}")
    
    return record


def test_daily_report_generator():
    """Test the updated DailyReportGenerator"""
    print("\nTesting DailyReportGenerator...")
    
    # Create test records
    records = []
    for i in range(3):
        record = DailySentimentRecord(
            website_id=f"website_{i}",
            session_id=f"session_{i}",
            user_nickname=f"User{i}",
            user_id=f"user_{i}",
            agent_name=f"Agent{i}",
            agent_role="operator",
            conversation_start=int(time.time()) - 3600 + i * 600,
            conversation_end=int(time.time()) - i * 300,
            is_completed=True,
            overall_emotion=["positive", "neutral", "negative"][i],
            sentiment_shift="stable",
            latest_sentiment=["positive", "neutral", "negative"][i],
            message_count=10 + i * 5,
            analysis_timestamp=int(time.time())
        )
        records.append(record)
    
    # Create a mock daily report
    report = DailySentimentReport(
        report_date=datetime.now().strftime('%Y-%m-%d'),
        generation_timestamp=int(time.time()),
        total_records=len(records),
        total_conversations=len(records),
        total_agents=len(records),
        records=records,
        processing_duration_seconds=1.5
    )
    
    print(f"✓ Created DailySentimentReport with {len(records)} records")
    
    # Test conversion to dict list
    config = {}
    continuous_processor = None  # Mock
    generator = DailyReportGenerator(config, continuous_processor)
    
    dict_list = generator.convert_records_to_dict_list(records)
    
    print(f"✓ Converted to dict list: {len(dict_list)} items")
    
    # Check if all new fields are present
    expected_fields = [
        'website_id', 'session_id', 'user_nickname', 'user_id',
        'agent_name', 'agent_role', 'conversation_start', 'conversation_end',
        'is_completed', 'overall_emotion', 'sentiment_shift', 'latest_sentiment',
        'message_count', 'analysis_timestamp'
    ]
    
    if dict_list:
        first_record = dict_list[0]
        missing_fields = [field for field in expected_fields if field not in first_record]
        
        if missing_fields:
            print(f"✗ Missing fields: {missing_fields}")
            return False
        else:
            print(f"✓ All expected fields present: {list(first_record.keys())}")
    
    return dict_list


def test_postgresql_schema():
    """Test PostgreSQL schema (without actual database connection)"""
    print("\nTesting PostgreSQL schema...")
    
    # Test configuration
    config = {
        'enabled': False,  # Don't actually connect
        'host': 'localhost',
        'port': 5432,
        'database': 'test_sentiment',
        'username': 'postgres',
        'password': 'password',
        'table_name': 'daily_sentiment_reports'
    }
    
    service = PostgreSQLService(config)
    
    print(f"✓ PostgreSQL service initialized (disabled mode)")
    print(f"  - Table name: {service.table_name}")
    print(f"  - Host: {service.host}:{service.port}")
    
    # Test status
    status = service.get_connection_status()
    print(f"✓ Connection status: {status}")
    
    return True


def main():
    """Run all tests"""
    print("=" * 60)
    print("PostgreSQL Schema Update Tests")
    print("=" * 60)
    
    try:
        # Test 1: Schema validation
        record = test_daily_sentiment_record_schema()
        
        # Test 2: Report generator
        dict_list = test_daily_report_generator()
        
        # Test 3: PostgreSQL service
        test_postgresql_schema()
        
        print("\n" + "=" * 60)
        print("✓ All tests passed successfully!")
        print("=" * 60)
        
        # Show sample data structure
        if dict_list:
            print("\nSample record structure:")
            print("-" * 40)
            for key, value in dict_list[0].items():
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

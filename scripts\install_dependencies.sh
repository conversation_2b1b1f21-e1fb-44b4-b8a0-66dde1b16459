#!/bin/bash
# Shell script to install dependencies for the Sentiment Analysis API System using Conda

# Check if Conda is installed
if ! command -v conda &>/dev/null; then
    echo "Conda is not installed. Please install Miniconda or Anaconda."
    echo "Visit https://docs.conda.io/en/latest/miniconda.html for installation instructions."
    exit 1
fi

# Display Conda version
echo "Found Conda: $(conda --version)"

# Create Conda environment
echo "Creating Conda environment..."
conda env create -f environment.yml
echo "Conda environment created."

# Activate Conda environment
echo "To activate the Conda environment, run:"
echo "conda activate sentiment-api"

# Create .env file from example if it doesn't exist
if [ ! -f ".env" ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo ".env file created. Please edit it with your configuration."
else
    echo ".env file already exists."
fi

echo "Setup complete. After activating the environment, you can run the application with:"
echo "uvicorn app.main:app --host 0.0.0.0 --port 8000"

#!/bin/bash

# Setup Airflow Integration for Sentiment Analysis
# Usage: ./setup-airflow-integration.sh [sentiment-server|airflow-server]

set -euo pipefail

# Configuration
SETUP_TYPE=${1:-}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

show_usage() {
    echo "Usage: $0 [sentiment-server|airflow-server]"
    echo ""
    echo "Setup types:"
    echo "  sentiment-server  - Setup Airflow integration on Sentiment Analysis server"
    echo "  airflow-server    - Setup DAGs and configuration on Airflow server"
    echo ""
    echo "Examples:"
    echo "  $0 sentiment-server"
    echo "  $0 airflow-server"
}

# Validate input
if [ -z "$SETUP_TYPE" ]; then
    show_usage
    exit 1
fi

if [[ "$SETUP_TYPE" != "sentiment-server" && "$SETUP_TYPE" != "airflow-server" ]]; then
    error "Invalid setup type. Use 'sentiment-server' or 'airflow-server'"
fi

log "Starting Airflow integration setup for: $SETUP_TYPE"

# Setup for Sentiment Analysis Server
setup_sentiment_server() {
    log "Setting up Airflow integration on Sentiment Analysis server..."
    
    # Check if required files exist
    if [ ! -f "$PROJECT_DIR/app/api/airflow_integration.py" ]; then
        error "Airflow integration API file not found"
    fi
    
    if [ ! -f "$PROJECT_DIR/app/utils/task_executors.py" ]; then
        error "Task executors file not found"
    fi
    
    # Install required Python packages
    log "Installing required Python packages..."
    pip install psutil || error "Failed to install psutil"
    
    # Create environment configuration
    log "Setting up environment configuration..."
    
    ENV_FILE="$PROJECT_DIR/.env"
    if [ ! -f "$ENV_FILE" ]; then
        cp "$PROJECT_DIR/.env.example" "$ENV_FILE"
        log "Created .env file from template"
    fi
    
    # Add Airflow-specific environment variables
    if ! grep -q "AIRFLOW_API_TOKEN" "$ENV_FILE"; then
        echo "" >> "$ENV_FILE"
        echo "# Airflow Integration" >> "$ENV_FILE"
        echo "AIRFLOW_API_TOKEN=airflow-secret-token-change-this" >> "$ENV_FILE"
        echo "AIRFLOW_ENABLED=true" >> "$ENV_FILE"
        log "Added Airflow environment variables to .env"
    fi
    
    # Create logs directory if it doesn't exist
    mkdir -p "$PROJECT_DIR/logs"
    
    # Test API endpoints
    log "Testing Airflow integration endpoints..."
    
    # Start server in background for testing
    cd "$PROJECT_DIR"
    python run.py &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 10
    
    # Test health endpoint
    if curl -f -H "Authorization: Bearer airflow-secret-token-change-this" \
            http://localhost:8000/airflow/health > /dev/null 2>&1; then
        success "Airflow health endpoint is working"
    else
        warning "Airflow health endpoint test failed (server may need manual restart)"
    fi
    
    # Stop test server
    kill $SERVER_PID 2>/dev/null || true
    
    success "Sentiment Analysis server setup completed"
    
    echo ""
    echo "Next steps:"
    echo "1. Update AIRFLOW_API_TOKEN in .env with a secure token"
    echo "2. Restart the sentiment analysis server"
    echo "3. Test endpoints: http://localhost:8000/docs#/airflow-integration"
}

# Setup for Airflow Server
setup_airflow_server() {
    log "Setting up DAGs and configuration on Airflow server..."
    
    # Check if AIRFLOW_HOME is set
    if [ -z "${AIRFLOW_HOME:-}" ]; then
        error "AIRFLOW_HOME environment variable is not set"
    fi
    
    if [ ! -d "$AIRFLOW_HOME" ]; then
        error "Airflow home directory does not exist: $AIRFLOW_HOME"
    fi
    
    log "Using Airflow home: $AIRFLOW_HOME"
    
    # Create DAGs directory if it doesn't exist
    DAGS_DIR="$AIRFLOW_HOME/dags"
    mkdir -p "$DAGS_DIR"
    
    # Copy DAG files
    log "Copying DAG files..."
    
    if [ -f "$PROJECT_DIR/airflow/dags/sentiment_analysis_dag.py" ]; then
        cp "$PROJECT_DIR/airflow/dags/sentiment_analysis_dag.py" "$DAGS_DIR/"
        success "Copied sentiment_analysis_dag.py"
    else
        error "DAG file not found: sentiment_analysis_dag.py"
    fi
    
    if [ -f "$PROJECT_DIR/airflow/dags/sentiment_monitoring_dag.py" ]; then
        cp "$PROJECT_DIR/airflow/dags/sentiment_monitoring_dag.py" "$DAGS_DIR/"
        success "Copied sentiment_monitoring_dag.py"
    else
        error "DAG file not found: sentiment_monitoring_dag.py"
    fi
    
    # Create config directory
    CONFIG_DIR="$AIRFLOW_HOME/config"
    mkdir -p "$CONFIG_DIR"
    
    # Copy configuration files
    log "Copying configuration files..."
    
    if [ -f "$PROJECT_DIR/airflow/config/airflow_connections.py" ]; then
        cp "$PROJECT_DIR/airflow/config/airflow_connections.py" "$CONFIG_DIR/"
        success "Copied airflow_connections.py"
    fi
    
    if [ -f "$PROJECT_DIR/airflow/config/airflow_variables.py" ]; then
        cp "$PROJECT_DIR/airflow/config/airflow_variables.py" "$CONFIG_DIR/"
        success "Copied airflow_variables.py"
    fi
    
    # Install required Airflow providers
    log "Installing required Airflow providers..."
    
    pip install apache-airflow-providers-http || warning "Failed to install HTTP provider"
    pip install apache-airflow-providers-email || warning "Failed to install Email provider"
    
    # Setup connections
    log "Setting up Airflow connections..."
    
    read -p "Enter Sentiment Analysis server URL (e.g., http://your-server.com:8000): " SENTIMENT_URL
    read -p "Enter API token: " API_TOKEN
    read -p "Enter alert email address: " ALERT_EMAIL
    
    # Update connection script with user input
    sed -i "s|your-sentiment-server.com|${SENTIMENT_URL#http://}|g" "$CONFIG_DIR/airflow_connections.py"
    sed -i "s|<EMAIL>|$ALERT_EMAIL|g" "$CONFIG_DIR/airflow_connections.py"
    
    # Update variables script with user input
    sed -i "s|http://your-sentiment-server.com:8000|$SENTIMENT_URL|g" "$CONFIG_DIR/airflow_variables.py"
    sed -i "s|airflow-secret-token-change-this|$API_TOKEN|g" "$CONFIG_DIR/airflow_variables.py"
    sed -i "s|<EMAIL>|$ALERT_EMAIL|g" "$CONFIG_DIR/airflow_variables.py"
    
    # Run setup scripts
    log "Running connection setup..."
    cd "$CONFIG_DIR"
    python airflow_connections.py || warning "Connection setup failed - run manually later"
    
    log "Running variables setup..."
    python airflow_variables.py || warning "Variables setup failed - run manually later"
    
    # Create startup script
    log "Creating startup script..."
    
    cat > "$AIRFLOW_HOME/start-sentiment-integration.sh" << 'EOF'
#!/bin/bash

# Startup script for Sentiment Analysis Airflow integration

echo "Starting Airflow services for Sentiment Analysis integration..."

# Start Airflow webserver
airflow webserver --port 8080 --daemon

# Start Airflow scheduler
airflow scheduler --daemon

echo "Airflow services started"
echo "Web UI: http://localhost:8080"
echo "Username: admin"
echo "Password: admin (change this!)"

# Show DAG status
sleep 5
airflow dags list | grep sentiment
EOF
    
    chmod +x "$AIRFLOW_HOME/start-sentiment-integration.sh"
    
    success "Airflow server setup completed"
    
    echo ""
    echo "Next steps:"
    echo "1. Start Airflow services: $AIRFLOW_HOME/start-sentiment-integration.sh"
    echo "2. Access Airflow UI: http://localhost:8080"
    echo "3. Enable the sentiment analysis DAGs"
    echo "4. Test the integration"
}

# Test integration
test_integration() {
    log "Testing Airflow integration..."
    
    read -p "Enter Sentiment Analysis server URL: " SENTIMENT_URL
    read -p "Enter API token: " API_TOKEN
    
    # Test health endpoint
    log "Testing health endpoint..."
    if curl -f -H "Authorization: Bearer $API_TOKEN" \
            "$SENTIMENT_URL/airflow/health" > /dev/null 2>&1; then
        success "Health endpoint test passed"
    else
        error "Health endpoint test failed"
    fi
    
    # Test task creation
    log "Testing task creation..."
    TASK_RESPONSE=$(curl -s -X POST \
        -H "Authorization: Bearer $API_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "task_type": "sentiment_analysis",
            "parameters": {
                "website_id": "test-site",
                "session_id": "test-session",
                "messages": [
                    {"role": "user", "content": "Test message"},
                    {"role": "operator", "content": "Test response"}
                ]
            }
        }' \
        "$SENTIMENT_URL/airflow/tasks")
    
    if echo "$TASK_RESPONSE" | grep -q "task_id"; then
        success "Task creation test passed"
        TASK_ID=$(echo "$TASK_RESPONSE" | grep -o '"task_id":"[^"]*"' | cut -d'"' -f4)
        log "Created test task: $TASK_ID"
    else
        error "Task creation test failed"
    fi
    
    success "Integration tests completed"
}

# Main execution
case $SETUP_TYPE in
    "sentiment-server")
        setup_sentiment_server
        ;;
    "airflow-server")
        setup_airflow_server
        ;;
    "test")
        test_integration
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

echo ""
echo "========================================"
echo "  Airflow Integration Setup Complete"
echo "========================================"
echo "Setup type: $SETUP_TYPE"
echo "Time: $(date)"
echo ""
echo "Documentation: doc/AIRFLOW_INTEGRATION_GUIDE.md"
echo "Support: Check the troubleshooting section in the guide"
echo "========================================"

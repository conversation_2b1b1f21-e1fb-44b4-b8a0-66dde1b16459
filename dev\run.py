"""
Sentiment Analysis API System
----------------------------
<PERSON><PERSON><PERSON> to run the FastAPI application with Uvicorn (without Ngrok integration)

Note: To use Ngrok, run the run_ngrok.py script separately:
      python run_ngrok.py --port 5000 --keep-alive
"""

import os
import logging
import uvicorn
import sys
import time
from app.core.config import Config

# Set up logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/app.log')
    ]
)
logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)
os.makedirs('data', exist_ok=True)

# Initialize configuration
config = Config()
server_config = config.get_server_config()

if __name__ == "__main__":
    # Print banner
    print("""
    ╔════════════════════════════════════════════════════════════╗
    ║                                                            ║
    ║   Sentiment Analysis API System                            ║
    ║   ----------------------------                             ║
    ║   A webhook-based system for sentiment analysis using      ║
    ║   the Llama model with llama.cpp integration.              ║
    ║                                                            ║
    ╚════════════════════════════════════════════════════════════╝
    """)

    # Wait a moment for the banner to be visible
    time.sleep(1)

    # Note about Ngrok
    if server_config.get('use_ngrok', True):
        print("\nNOTE: This script only starts the API server without Ngrok integration.")
        print("To use Ngrok, run the run_ngrok.py script in a separate terminal:")
        print(f"    python run_ngrok.py --port {server_config.get('port', 5000)} --keep-alive\n")
        logger.info("Ngrok integration is enabled in config, but not used in this script.")
        logger.info("To use Ngrok, run the run_ngrok.py script separately.")

    try:
        # Configure Uvicorn
        uvicorn_config = {
            "app": "app.main:app",
            "host": server_config.get('host', '0.0.0.0'),
            "port": server_config.get('port', 5000),
            "reload": server_config.get('debug', False),
            "workers": server_config.get('workers', 1),
            "log_level": "info",
        }

        # Run the server
        logger.info(f"Starting Uvicorn server on {uvicorn_config['host']}:{uvicorn_config['port']}...")
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        logger.info("Server shutting down...")
        logger.info("Server shutdown complete.")
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")

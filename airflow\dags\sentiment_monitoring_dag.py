"""
Sentiment Analysis Monitoring DAG
Continuous monitoring and alerting for sentiment analysis system
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.email_operator import EmailOperator
from airflow.hooks.http_hook import HttpHook
from airflow.models import Variable
from airflow.sensors.http_sensor import HttpSensor
import json
import logging

# Configuration
SENTIMENT_API_BASE_URL = Variable.get("SENTIMENT_API_BASE_URL", "http://your-sentiment-server:8000")
AIRFLOW_API_TOKEN = Variable.get("AIRFLOW_API_TOKEN", "airflow-secret-token")
ALERT_EMAIL = Variable.get("ALERT_EMAIL", "<EMAIL>")

# Default arguments
default_args = {
    'owner': 'ops-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=2),
    'execution_timeout': timedelta(minutes=10)
}

# Create monitoring DAG
monitoring_dag = DAG(
    'sentiment_monitoring',
    default_args=default_args,
    description='Continuous monitoring of sentiment analysis system',
    schedule_interval=timedelta(minutes=5),  # Run every 5 minutes
    catchup=False,
    max_active_runs=1,
    tags=['sentiment-analysis', 'monitoring', 'ops']
)

def check_system_metrics(**context):
    """Check system metrics and detect anomalies"""
    http_hook = HttpHook(method='GET', http_conn_id='sentiment_api')
    
    try:
        # Get health status
        health_response = http_hook.run(
            endpoint='/airflow/health',
            headers={
                'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
                'Content-Type': 'application/json'
            }
        )
        
        health_data = json.loads(health_response.content)
        
        # Get detailed metrics
        metrics_response = http_hook.run(
            endpoint='/airflow/metrics',
            headers={
                'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
                'Content-Type': 'application/json'
            }
        )
        
        metrics_data = json.loads(metrics_response.content)
        
        # Check for issues
        issues = []
        
        # Check system health
        if health_data['status'] != 'healthy':
            issues.append(f"System status is {health_data['status']}")
        
        # Check service health
        for service, status in health_data['services'].items():
            if status != 'healthy':
                issues.append(f"Service {service} is {status}")
        
        # Check system metrics
        system_metrics = metrics_data['system_metrics']
        
        if system_metrics['cpu_usage'] > 80:
            issues.append(f"High CPU usage: {system_metrics['cpu_usage']}%")
        
        if system_metrics['memory_usage'] > 85:
            issues.append(f"High memory usage: {system_metrics['memory_usage']}%")
        
        if system_metrics['disk_usage'] > 90:
            issues.append(f"High disk usage: {system_metrics['disk_usage']}%")
        
        # Check task metrics
        task_metrics = metrics_data['task_metrics']
        
        if task_metrics['failed_tasks'] > 5:
            issues.append(f"High number of failed tasks: {task_metrics['failed_tasks']}")
        
        if task_metrics['pending_tasks'] > 20:
            issues.append(f"High number of pending tasks: {task_metrics['pending_tasks']}")
        
        # Store results
        monitoring_result = {
            'timestamp': datetime.now().isoformat(),
            'health_status': health_data['status'],
            'issues': issues,
            'metrics': metrics_data,
            'alert_level': 'critical' if len(issues) > 3 else 'warning' if issues else 'normal'
        }
        
        # Push to XCom for downstream tasks
        context['task_instance'].xcom_push(key='monitoring_result', value=monitoring_result)
        
        if issues:
            logging.warning(f"Issues detected: {issues}")
        else:
            logging.info("System monitoring: All checks passed")
        
        return monitoring_result
        
    except Exception as e:
        logging.error(f"Monitoring check failed: {e}")
        # Push error result
        error_result = {
            'timestamp': datetime.now().isoformat(),
            'health_status': 'unknown',
            'issues': [f"Monitoring failed: {str(e)}"],
            'alert_level': 'critical'
        }
        context['task_instance'].xcom_push(key='monitoring_result', value=error_result)
        raise

def check_task_performance(**context):
    """Check task performance and identify bottlenecks"""
    http_hook = HttpHook(method='GET', http_conn_id='sentiment_api')
    
    try:
        # Get task list
        response = http_hook.run(
            endpoint='/airflow/tasks?limit=50',
            headers={
                'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
                'Content-Type': 'application/json'
            }
        )
        
        tasks = json.loads(response.content)
        
        # Analyze task performance
        performance_issues = []
        
        # Check for long-running tasks
        long_running_tasks = []
        failed_tasks = []
        
        for task in tasks:
            if task['status'] == 'running' and task['started_at']:
                started_at = datetime.fromisoformat(task['started_at'].replace('Z', '+00:00'))
                duration = (datetime.now() - started_at.replace(tzinfo=None)).total_seconds()
                
                if duration > 3600:  # 1 hour
                    long_running_tasks.append({
                        'task_id': task['task_id'],
                        'duration': duration
                    })
            
            elif task['status'] == 'failed':
                failed_tasks.append({
                    'task_id': task['task_id'],
                    'error': task.get('error', 'Unknown error')
                })
        
        if long_running_tasks:
            performance_issues.append(f"Long-running tasks detected: {len(long_running_tasks)}")
        
        if failed_tasks:
            performance_issues.append(f"Failed tasks detected: {len(failed_tasks)}")
        
        performance_result = {
            'timestamp': datetime.now().isoformat(),
            'long_running_tasks': long_running_tasks,
            'failed_tasks': failed_tasks,
            'performance_issues': performance_issues,
            'total_tasks_checked': len(tasks)
        }
        
        context['task_instance'].xcom_push(key='performance_result', value=performance_result)
        
        return performance_result
        
    except Exception as e:
        logging.error(f"Performance check failed: {e}")
        raise

def generate_alert(**context):
    """Generate alerts based on monitoring results"""
    monitoring_result = context['task_instance'].xcom_pull(key='monitoring_result')
    performance_result = context['task_instance'].xcom_pull(key='performance_result')
    
    if not monitoring_result:
        logging.warning("No monitoring result found")
        return "no_data"
    
    alert_level = monitoring_result.get('alert_level', 'normal')
    issues = monitoring_result.get('issues', [])
    
    if performance_result:
        issues.extend(performance_result.get('performance_issues', []))
    
    if alert_level == 'normal' and not issues:
        logging.info("No alerts needed - system is healthy")
        return "no_alert"
    
    # Prepare alert message
    alert_message = f"""
Sentiment Analysis System Alert - {alert_level.upper()}

Timestamp: {monitoring_result['timestamp']}
System Status: {monitoring_result['health_status']}

Issues Detected:
{chr(10).join(f"- {issue}" for issue in issues)}

System Metrics:
- CPU Usage: {monitoring_result['metrics']['system_metrics']['cpu_usage']}%
- Memory Usage: {monitoring_result['metrics']['system_metrics']['memory_usage']}%
- Disk Usage: {monitoring_result['metrics']['system_metrics']['disk_usage']}%

Task Metrics:
- Active Tasks: {monitoring_result['metrics']['task_metrics']['active_tasks']}
- Pending Tasks: {monitoring_result['metrics']['task_metrics']['pending_tasks']}
- Failed Tasks: {monitoring_result['metrics']['task_metrics']['failed_tasks']}

Please investigate and take appropriate action.
"""
    
    # Store alert for email task
    context['task_instance'].xcom_push(key='alert_message', value=alert_message)
    context['task_instance'].xcom_push(key='alert_level', value=alert_level)
    
    logging.warning(f"Alert generated: {alert_level}")
    return alert_message

def should_send_email(**context):
    """Determine if email should be sent"""
    alert_level = context['task_instance'].xcom_pull(key='alert_level')
    
    # Only send email for warning or critical alerts
    if alert_level in ['warning', 'critical']:
        return True
    
    return False

# Task definitions
system_metrics_check = PythonOperator(
    task_id='check_system_metrics',
    python_callable=check_system_metrics,
    dag=monitoring_dag
)

performance_check = PythonOperator(
    task_id='check_task_performance',
    python_callable=check_task_performance,
    dag=monitoring_dag
)

alert_generation = PythonOperator(
    task_id='generate_alert',
    python_callable=generate_alert,
    dag=monitoring_dag
)

# Email alert (conditional)
email_alert = EmailOperator(
    task_id='send_email_alert',
    to=[ALERT_EMAIL],
    subject='Sentiment Analysis System Alert - {{ ti.xcom_pull(key="alert_level") }}',
    html_content='<pre>{{ ti.xcom_pull(key="alert_message") }}</pre>',
    dag=monitoring_dag
)

# Task dependencies
[system_metrics_check, performance_check] >> alert_generation >> email_alert

# Create a separate DAG for weekly reports
weekly_report_dag = DAG(
    'sentiment_weekly_report',
    default_args=default_args,
    description='Weekly sentiment analysis performance report',
    schedule_interval='0 9 * * 1',  # Run every Monday at 9 AM
    catchup=False,
    max_active_runs=1,
    tags=['sentiment-analysis', 'reporting', 'weekly']
)

def generate_weekly_report(**context):
    """Generate weekly performance report"""
    http_hook = HttpHook(method='GET', http_conn_id='sentiment_api')
    
    try:
        # Get metrics for the past week
        metrics_response = http_hook.run(
            endpoint='/airflow/metrics',
            headers={
                'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
                'Content-Type': 'application/json'
            }
        )
        
        metrics_data = json.loads(metrics_response.content)
        
        # Get task history
        tasks_response = http_hook.run(
            endpoint='/airflow/tasks?limit=1000',
            headers={
                'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
                'Content-Type': 'application/json'
            }
        )
        
        tasks_data = json.loads(tasks_response.content)
        
        # Analyze weekly performance
        execution_date = context['execution_date']
        week_start = execution_date - timedelta(days=7)
        
        weekly_tasks = [
            task for task in tasks_data 
            if task.get('started_at') and 
            datetime.fromisoformat(task['started_at'].replace('Z', '+00:00')).replace(tzinfo=None) >= week_start
        ]
        
        # Calculate statistics
        total_tasks = len(weekly_tasks)
        completed_tasks = len([t for t in weekly_tasks if t['status'] == 'completed'])
        failed_tasks = len([t for t in weekly_tasks if t['status'] == 'failed'])
        
        success_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        # Generate report
        report = f"""
Weekly Sentiment Analysis Report
Week ending: {execution_date.strftime('%Y-%m-%d')}

Task Statistics:
- Total Tasks: {total_tasks}
- Completed Tasks: {completed_tasks}
- Failed Tasks: {failed_tasks}
- Success Rate: {success_rate:.1f}%

Current System Status:
- Active Tasks: {metrics_data['task_metrics']['active_tasks']}
- Pending Tasks: {metrics_data['task_metrics']['pending_tasks']}

Performance Metrics:
- Average Task Duration: {metrics_data['performance_metrics']['avg_task_duration']:.1f}s
- Total Processing Time: {metrics_data['performance_metrics']['total_processing_time']:.1f}s

System Health:
- CPU Usage: {metrics_data['system_metrics']['cpu_usage']}%
- Memory Usage: {metrics_data['system_metrics']['memory_usage']}%
- Disk Usage: {metrics_data['system_metrics']['disk_usage']}%

This report was generated automatically by Apache Airflow.
"""
        
        context['task_instance'].xcom_push(key='weekly_report', value=report)
        
        logging.info("Weekly report generated successfully")
        return report
        
    except Exception as e:
        logging.error(f"Weekly report generation failed: {e}")
        raise

weekly_report_task = PythonOperator(
    task_id='generate_weekly_report',
    python_callable=generate_weekly_report,
    dag=weekly_report_dag
)

weekly_email = EmailOperator(
    task_id='send_weekly_report',
    to=[ALERT_EMAIL],
    subject='Weekly Sentiment Analysis Report - {{ ds }}',
    html_content='<pre>{{ ti.xcom_pull(key="weekly_report") }}</pre>',
    dag=weekly_report_dag
)

weekly_report_task >> weekly_email

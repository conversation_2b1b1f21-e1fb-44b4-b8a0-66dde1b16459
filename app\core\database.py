import os
import json
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class MessageDatabase:
    """Real-time database for storing and managing messages with conversation timeout"""

    def __init__(self, save_path: str = "data/messages.json", autosave_interval_sec: int = 60, conversation_timeout_hours: int = 24, cleanup_interval_hours: int = 6):
        """
        Initialize the database

        Args:
            save_path: Path to save the database file
            autosave_interval_sec: Interval in seconds to auto-save the database
            conversation_timeout_hours: Timeout in hours for conversations
            cleanup_interval_hours: Interval in hours to clean up old conversations
        """
        self.save_path = save_path
        self.autosave_interval = autosave_interval_sec
        self.conversation_timeout = conversation_timeout_hours * 3600  # Convert hours to seconds
        self.cleanup_interval = cleanup_interval_hours * 3600  # Convert hours to seconds
        self._lock = threading.Lock()
        self._data = {}
        self._conversation_last_activity = {}  # Track last activity time for each conversation
        self._running = True  # Flag to control background threads

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.save_path), exist_ok=True)

        # Load existing data if available
        if os.path.exists(self.save_path):
            try:
                logger.info(f"Loading data from {self.save_path}...")
                with open(self.save_path, "r", encoding="utf-8") as f:
                    self._data = json.load(f)

                # Count total conversations and messages
                website_count = len(self._data)
                conversation_count = 0
                message_count = 0
                for website_id in self._data:
                    conversation_count += len(self._data[website_id])
                    for session_id in self._data[website_id]:
                        message_count += len(self._data[website_id][session_id].get("data", []))

                logger.info(f"Successfully loaded data from disk: {website_count} websites, {conversation_count} conversations, {message_count} messages")

                # Initialize conversation activity timestamps from loaded data
                self._initialize_conversation_timestamps()
            except Exception as e:
                logger.error(f"Failed to load existing data: {e}")
                logger.error(f"Error details: {str(e)}")
        else:
            logger.info(f"No existing data file found at {self.save_path}. Starting with empty database.")

        # Start autosave thread
        self._autosave_thread = threading.Thread(target=self._autosave_loop, daemon=True)
        self._autosave_thread.start()

        # Start cleanup thread
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.start()

    def _initialize_conversation_timestamps(self):
        """Initialize conversation timestamps from loaded data"""
        for website_id in self._data:
            for session_id in self._data[website_id]:
                # Find the most recent message timestamp
                latest_timestamp = 0
                for message in self._data[website_id][session_id].get("data", []):
                    msg_timestamp = message.get("timestamp", 0)
                    if msg_timestamp > latest_timestamp:
                        latest_timestamp = msg_timestamp

                if latest_timestamp > 0:
                    self._conversation_last_activity[(website_id, session_id)] = latest_timestamp
                    logger.debug(f"Initialized conversation timestamp for {website_id}/{session_id}: {latest_timestamp}")

    def add_message(self, message: Dict[str, Any]):
        """
        Add a message to the database with conversation timeout handling

        Args:
            message: Message data to add
        """
        with self._lock:
            try:
                website_id = message["website_id"]
                session_id = message["data"]["session_id"]
                current_timestamp = message["data"]["timestamp"]

                # Extract agent information if available
                agent_info = message["data"].get("agent_info", {})

                # Process message based on type
                mes = {}
                if message["data"]["type"] == "text":
                    logger.debug("Processing text message")
                    mes = {
                        "type": message["data"]["type"],
                        "origin": message["data"]["origin"],
                        "text": message["data"]["content"],
                        "timestamp": current_timestamp,
                        "fingerprint": message["data"]["fingerprint"],
                        "role": message["data"]["from"],
                        "agent_info": agent_info  # Add agent information
                    }
                elif message["data"]["type"] == "image":
                    logger.debug("Processing image message")
                    mes = {
                        "type": message["data"]["type"],
                        "origin": message["data"]["origin"],
                        "url": message["data"]["url"],
                        "timestamp": current_timestamp,
                        "fingerprint": message["data"]["fingerprint"],
                        "role": message["data"]["from"],
                        "agent_info": agent_info  # Add agent information
                    }
                else:
                    logger.warning(f"Unknown message type: {message['data']['type']}")
                    return

                # Create website entry if it doesn't exist
                if website_id not in self._data:
                    logger.info(f"Creating new website record: {website_id}")
                    self._data[website_id] = {}

                # Check if we need to create a new conversation due to timeout
                conversation_key = (website_id, session_id)
                last_activity = self._conversation_last_activity.get(conversation_key, 0)
                time_since_last_message = current_timestamp - last_activity if last_activity > 0 else 0

                # Create a new conversation if timeout exceeded or session doesn't exist
                new_conversation = False
                if session_id not in self._data[website_id]:
                    logger.info(f"Creating new session record: {session_id} for website {website_id}")
                    new_conversation = True
                elif time_since_last_message > self.conversation_timeout:
                    logger.info(f"Conversation timeout exceeded for {website_id}/{session_id}. "
                                f"Time since last message: {time_since_last_message/3600:.2f} hours. "
                                f"Creating new conversation.")
                    # Archive old conversation data if needed
                    # For now, we'll just replace it with a new conversation
                    new_conversation = True

                if new_conversation:
                    # Create a new conversation
                    self._data[website_id][session_id] = {
                        "user": message["data"]["user"],
                        "data": [mes],
                        "conversation_start": current_timestamp
                    }
                else:
                    # Add message to existing conversation
                    logger.debug(f"Adding message to existing conversation for {website_id}/{session_id}")
                    self._data[website_id][session_id]["data"].append(mes)

                # Update the last activity timestamp
                self._conversation_last_activity[conversation_key] = current_timestamp

                # Update conversation tracker if available
                self._update_conversation_tracker(website_id, session_id)

            except KeyError as e:
                logger.error(f"Missing key in message: {e}")
            except Exception as e:
                logger.error(f"Error adding message: {e}")

    def _update_conversation_tracker(self, website_id: str, session_id: str):
        """Update conversation tracker with latest information"""
        try:
            # Get conversation tracker from app module if available
            import app as app_module
            if hasattr(app_module, 'continuous_processor') and app_module.continuous_processor:
                conversation_tracker = app_module.continuous_processor.conversation_tracker

                # Get conversation data
                conversation_data = self._data.get(website_id, {}).get(session_id, {})
                messages = conversation_data.get('data', [])

                if messages:
                    # Get last message timestamp
                    last_message_timestamp = max(msg.get('timestamp', 0) for msg in messages)
                    message_count = len(messages)

                    # Determine if conversation is completed (no activity for 24 hours)
                    current_time = int(time.time())
                    is_completed = (current_time - last_message_timestamp) > (24 * 3600)

                    # Update tracker
                    conversation_tracker.update_conversation(
                        website_id=website_id,
                        session_id=session_id,
                        last_message_timestamp=last_message_timestamp,
                        message_count=message_count,
                        is_completed=is_completed
                    )
        except Exception as e:
            # Don't let tracker errors affect database operations
            logger.debug(f"Error updating conversation tracker: {e}")

    def get_all_messages(self) -> List[Dict[str, Any]]:
        """Get all messages in the database"""
        with self._lock:
            return list(self._data)

    def get_messages(self, website_id, session_id) -> List[Dict[str, Any]]:
        """
        Get messages for a specific website and session

        Args:
            website_id: Website ID
            session_id: Session ID

        Returns:
            List of messages in the format expected by the model
        """
        with self._lock:
            try:
                if website_id not in self._data:
                    logger.warning(f"Website ID {website_id} not found in database")
                    return []

                if session_id not in self._data[website_id]:
                    logger.warning(f"Session ID {session_id} not found for website {website_id}")
                    return []

                # Convert messages to the format expected by the model
                temp_messages = []
                for message in self._data[website_id][session_id]["data"]:
                    if message["type"] == "text":
                        temp_messages.append({
                            "role": message["role"],
                            "content": message["text"]
                        })
                    # Support for image messages can be added here if needed
                    # elif message["type"] == "image":
                    #     temp_messages.append({
                    #         "role": message["role"],
                    #         "content": [
                    #             {"type": message["type"], "url": message["url"]}
                    #         ]
                    #     })

                logger.debug(f"Retrieved {len(temp_messages)} messages for session {session_id}")
                return temp_messages
            except Exception as e:
                logger.error(f"Error retrieving messages: {e}")
                return []

    def get_conversation_data(self, website_id: str, session_id: str) -> Dict[str, Any]:
        """
        Get raw conversation data for a specific website and session

        Args:
            website_id: Website ID
            session_id: Session ID

        Returns:
            Dictionary with conversation data including user info, messages, and metadata
        """
        with self._lock:
            try:
                if website_id not in self._data:
                    logger.warning(f"Website ID {website_id} not found in database")
                    return {}

                if session_id not in self._data[website_id]:
                    logger.warning(f"Session ID {session_id} not found for website {website_id}")
                    return {}

                # Return the raw conversation data
                conversation_data = self._data[website_id][session_id].copy()
                logger.debug(f"Retrieved conversation data for {website_id}/{session_id}")
                return conversation_data
            except Exception as e:
                logger.error(f"Error retrieving conversation data: {e}")
                return {}

    def save_to_disk(self):
        """Save the database to disk"""
        with self._lock:
            try:
                if len(self._data) > 0:
                    # Count total conversations and messages for logging
                    website_count = len(self._data)
                    conversation_count = 0
                    message_count = 0
                    for website_id in self._data:
                        conversation_count += len(self._data[website_id])
                        for session_id in self._data[website_id]:
                            message_count += len(self._data[website_id][session_id].get("data", []))

                    logger.info(f"Saving database: {website_count} websites, {conversation_count} conversations, {message_count} messages")

                    # Create directory if it doesn't exist
                    os.makedirs(os.path.dirname(self.save_path), exist_ok=True)

                    # Save the data
                    save_start_time = time.time()
                    with open(self.save_path, "w", encoding="utf-8") as f:
                        json.dump(self._data, f, ensure_ascii=False, indent=2)
                    save_time = time.time() - save_start_time

                    # Get file size for logging
                    file_size_bytes = os.path.getsize(self.save_path)
                    file_size_kb = file_size_bytes / 1024
                    file_size_mb = file_size_kb / 1024

                    if file_size_mb >= 1:
                        size_str = f"{file_size_mb:.2f} MB"
                    else:
                        size_str = f"{file_size_kb:.2f} KB"

                    logger.info(f"Database saved to {self.save_path} ({size_str}) in {save_time:.2f} seconds")

                    # Reload the website whitelist if configured
                    try:
                        from app import get_website_validator
                        website_validator = get_website_validator()
                        if website_validator.reload_after_save:
                            if website_validator.reload_whitelist():
                                logger.info("Reloaded website whitelist after saving messages")
                            else:
                                logger.debug("No changes to website whitelist detected")
                    except Exception as e:
                        logger.warning(f"Failed to reload website whitelist: {e}")

                    return True
                else:
                    logger.info("No data to save")
                    return False
            except Exception as e:
                logger.error(f"Failed to save database: {e}")
                logger.error(f"Error details: {str(e)}")
                return False

    def _autosave_loop(self):
        """Background thread that periodically saves the database"""
        logger.info(f"Starting autosave thread with interval of {self.autosave_interval} seconds ({self.autosave_interval/60:.1f} minutes)")

        # Wait a short time before the first save to allow initial data loading
        initial_wait = min(60, self.autosave_interval / 2)  # Wait at most 60 seconds
        logger.info(f"Initial autosave will occur in {initial_wait:.1f} seconds")
        time.sleep(initial_wait)

        save_count = 0
        while self._running:
            try:
                # Perform the auto-save
                save_start_time = time.time()
                logger.info(f"Auto-save #{save_count+1} starting...")
                success = self.save_to_disk()
                save_time = time.time() - save_start_time

                if success:
                    save_count += 1
                    logger.info(f"Auto-save #{save_count} completed in {save_time:.2f} seconds")
                else:
                    logger.warning(f"Auto-save skipped (no data or error occurred)")

                # Sleep until the next save interval
                next_save_time = time.strftime('%H:%M:%S', time.localtime(time.time() + self.autosave_interval))
                logger.info(f"Next auto-save scheduled at {next_save_time} (in {self.autosave_interval/60:.1f} minutes)")
                time.sleep(self.autosave_interval)
            except Exception as e:
                logger.error(f"Error in autosave loop: {e}")
                logger.error(f"Error details: {str(e)}")
                # Sleep for a shorter interval before retrying after an error
                time.sleep(60)  # Wait 1 minute before retrying after an error

    def _cleanup_loop(self):
        """Background thread that periodically cleans up old conversations"""
        logger.info(f"Starting cleanup thread with interval of {self.cleanup_interval} seconds ({self.cleanup_interval/3600:.1f} hours)")

        # Wait before the first cleanup to allow initial data loading
        initial_wait = min(300, self.cleanup_interval / 4)  # Wait at most 5 minutes
        logger.info(f"Initial cleanup will occur in {initial_wait:.1f} seconds")
        time.sleep(initial_wait)

        cleanup_count = 0
        while self._running:
            try:
                # Perform the cleanup
                cleanup_start_time = time.time()
                logger.info(f"Cleanup #{cleanup_count+1} starting...")
                removed_count = self.cleanup_old_conversations()
                cleanup_time = time.time() - cleanup_start_time

                cleanup_count += 1
                if removed_count > 0:
                    logger.info(f"Cleanup #{cleanup_count} completed in {cleanup_time:.2f} seconds. Removed {removed_count} old conversations.")
                else:
                    logger.info(f"Cleanup #{cleanup_count} completed in {cleanup_time:.2f} seconds. No old conversations to remove.")

                # Sleep until the next cleanup interval
                next_cleanup_time = time.strftime('%H:%M:%S', time.localtime(time.time() + self.cleanup_interval))
                logger.info(f"Next cleanup scheduled at {next_cleanup_time} (in {self.cleanup_interval/3600:.1f} hours)")
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                logger.error(f"Error details: {str(e)}")
                # Sleep for a shorter interval before retrying after an error
                time.sleep(300)  # Wait 5 minutes before retrying after an error

    def cleanup_old_conversations(self):
        """
        Clean up old conversations based on their last activity timestamp

        Returns:
            int: Number of conversations removed
        """
        with self._lock:
            try:
                current_time = time.time()
                conversations_to_remove = []

                # Identify old conversations
                for (website_id, session_id), last_activity in self._conversation_last_activity.items():
                    time_since_last_activity = current_time - last_activity
                    if time_since_last_activity > self.conversation_timeout:
                        # This conversation has exceeded the timeout
                        conversations_to_remove.append((website_id, session_id, time_since_last_activity))

                if not conversations_to_remove:
                    return 0

                # Log the conversations to be removed
                logger.info(f"Found {len(conversations_to_remove)} old conversations to remove")
                for website_id, session_id, time_since_last in conversations_to_remove:
                    hours_since_last = time_since_last / 3600
                    logger.info(f"Removing conversation {website_id}/{session_id} - Last activity: {hours_since_last:.2f} hours ago")

                    # Remove the conversation from the data
                    if website_id in self._data and session_id in self._data[website_id]:
                        del self._data[website_id][session_id]

                        # If this was the last conversation for this website, remove the website entry
                        if not self._data[website_id]:
                            del self._data[website_id]

                    # Remove from the activity tracking
                    del self._conversation_last_activity[(website_id, session_id)]

                # Save the database after cleanup
                self.save_to_disk()

                return len(conversations_to_remove)
            except Exception as e:
                logger.error(f"Error cleaning up old conversations: {e}")
                logger.error(f"Error details: {str(e)}")
                return 0

    def shutdown(self):
        """Properly shut down the database, saving data and stopping threads"""
        logger.info("Shutting down database...")
        self._running = False

        # Save data one last time
        logger.info("Performing final save before shutdown")
        self.save_to_disk()

        logger.info("Database shutdown complete")

"""
Continuous Sentiment Processor Service
-------------------------------------
Service for processing sentiment analysis continuously throughout the day
"""

import json
import os
import threading
import time
import uuid
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from app.models.schemas import (
    ConversationSentiment, SentimentAnalysisResult,
    UserInfo, AgentInfo, MessageData
)
from app.services.conversation_tracker import ConversationTracker

logger = logging.getLogger(__name__)


class ContinuousSentimentProcessor:
    """
    Service for processing sentiment analysis continuously throughout the day.
    This service:
    - Processes conversations at specified intervals
    - Tracks which conversations need analysis
    - Stores processed results in memory for daily upload
    - Manages the continuous sentiment analysis workflow
    """

    def __init__(self, config: Dict[str, Any], sentiment_service, database):
        """
        Initialize the continuous sentiment processor.

        Args:
            config: Periodic sentiment configuration
            sentiment_service: SentimentAnalysisService instance
            database: MessageDatabase instance
        """
        self.config = config
        self.sentiment_service = sentiment_service
        self.database = database

        # Initialize conversation tracker
        tracking_path = config.get('storage', {}).get('conversation_tracking_path',
                                                     'data/conversation_tracking.json')
        self.conversation_tracker = ConversationTracker(tracking_path)

        # Processing settings (get from main processing config)
        # These settings are now in the main processing section to avoid duplication
        self.min_messages = 2  # Will be overridden by main config
        self.max_age_hours = 168  # Will be overridden by main config
        self.batch_size = 50  # Will be overridden by main config
        self.conversation_timeout = 30  # Will be overridden by main config

        # Threading
        self._lock = threading.Lock()
        self._processing = False
        self._current_processing_id = None

        # In-memory storage for processed conversations (reset daily)
        self._daily_processed_conversations: Dict[str, ConversationSentiment] = {}
        self._last_reset_date = datetime.now().strftime('%Y-%m-%d')

        # Processing statistics
        self._processing_stats = {
            'total_processed_today': 0,
            'total_analyzed_today': 0,
            'total_skipped_today': 0,
            'last_processing_time': None
        }

        logger.info("ContinuousSentimentProcessor initialized")

    def update_processing_settings(self, processing_config: Dict[str, Any]):
        """
        Update processing settings from main processing configuration.

        Args:
            processing_config: Main processing configuration
        """
        self.min_messages = processing_config.get('min_messages_for_analysis', self.min_messages)
        self.max_age_hours = processing_config.get('max_conversation_age_hours', self.max_age_hours)
        self.batch_size = processing_config.get('sentiment_batch_size', self.batch_size)
        self.conversation_timeout = processing_config.get('conversation_timeout_sec', self.conversation_timeout)

        logger.debug(f"Updated processing settings: min_messages={self.min_messages}, "
                    f"max_age_hours={self.max_age_hours}, batch_size={self.batch_size}, "
                    f"timeout={self.conversation_timeout}")

    def _reset_daily_data_if_needed(self):
        """Reset daily data if it's a new day"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        if current_date != self._last_reset_date:
            with self._lock:
                self._daily_processed_conversations.clear()
                self._processing_stats = {
                    'total_processed_today': 0,
                    'total_analyzed_today': 0,
                    'total_skipped_today': 0,
                    'last_processing_time': None
                }
                self._last_reset_date = current_date
                logger.info(f"Reset daily data for new day: {current_date}")

    def _extract_conversation_data(self, website_id: str, session_id: str) -> Optional[ConversationSentiment]:
        """
        Extract conversation data from database and prepare for sentiment analysis.

        Args:
            website_id: Website ID
            session_id: Session ID

        Returns:
            ConversationSentiment object or None if extraction fails
        """
        try:
            # Get conversation data from database
            messages_data = self.database.get_conversation_data(website_id, session_id)
            if not messages_data:
                return None

            # Extract conversation metadata
            conversation_start = messages_data.get('conversation_start', 0)
            user_info = messages_data.get('user', {})
            messages_list = messages_data.get('data', [])

            if len(messages_list) < self.min_messages:
                return None

            # Convert user info
            user = UserInfo(
                nickname=user_info.get('nickname', 'Unknown'),
                user_id=user_info.get('user_id', session_id)
            )

            # Extract agent information from messages
            agents = []
            agent_names = set()
            for msg in messages_list:
                if msg.get('role') == 'operator':
                    origin = msg.get('origin', '')
                    if origin and origin not in agent_names:
                        agent_names.add(origin)
                        agents.append(AgentInfo(name=origin, role='operator'))

            # If no specific agents found, add a generic one
            if not agents:
                agents.append(AgentInfo(name='Support Agent', role='operator'))

            # Convert messages
            messages = []
            for msg in messages_list:
                try:
                    message = MessageData(
                        type=msg.get('type', 'text'),
                        origin=msg.get('origin', 'chat'),
                        text=msg.get('text', ''),
                        timestamp=msg.get('timestamp', 0),
                        fingerprint=msg.get('fingerprint', 0),
                        role=msg.get('role', 'user')
                    )
                    messages.append(message)
                except Exception as e:
                    logger.warning(f"Failed to convert message: {e}")
                    continue

            # Determine if conversation is completed
            current_time = int(time.time())
            last_message_time = max(msg.timestamp for msg in messages) if messages else 0
            is_completed = (current_time - last_message_time) > (24 * 3600)  # 24 hours

            # Create conversation sentiment object (without analysis yet)
            conversation_sentiment = ConversationSentiment(
                session_id=session_id,
                website_id=website_id,
                user=user,
                agents=agents,
                conversation_start=conversation_start,
                conversation_end=last_message_time if is_completed else None,
                messages=messages,
                sentiment_analysis=SentimentAnalysisResult(
                    overall_emotion="pending",
                    sentiment_shift="pending",
                    latest_sentiment="pending"
                ),
                analysis_timestamp=int(time.time()),
                message_count=len(messages),
                is_completed=is_completed
            )

            return conversation_sentiment

        except Exception as e:
            logger.error(f"Error extracting conversation data for {website_id}_{session_id}: {e}")
            return None

    def _perform_sentiment_analysis(self, conversation: ConversationSentiment) -> bool:
        """
        Perform sentiment analysis on a conversation.

        Args:
            conversation: ConversationSentiment object to analyze

        Returns:
            True if analysis was successful, False otherwise
        """
        try:
            # Prepare requests for all three sentiment types
            requests = []
            for item_id in ['overall_emotion', 'sentiment_shift', 'latest_sentiment']:
                request_data = {
                    'item_id': item_id,
                    'session_id': conversation.session_id,
                    'website_id': conversation.website_id,
                    'priority': 1  # High priority for continuous processing
                }

                # Add request to sentiment service
                request_id = self.sentiment_service.add_request(request_data)
                requests.append((item_id, request_id))

            # Wait for results with timeout
            results = {}
            start_time = time.time()

            while len(results) < 3 and (time.time() - start_time) < self.conversation_timeout:
                for item_id, request_id in requests:
                    if item_id not in results:
                        result = self.sentiment_service.get_result(request_id)
                        if result:
                            results[item_id] = result.get('data', {}).get('value', 'unknown')

                if len(results) < 3:
                    time.sleep(0.5)  # Wait before checking again

            # Update conversation with results
            if len(results) == 3:
                conversation.sentiment_analysis = SentimentAnalysisResult(
                    overall_emotion=results.get('overall_emotion', 'unknown'),
                    sentiment_shift=results.get('sentiment_shift', 'unknown'),
                    latest_sentiment=results.get('latest_sentiment', 'unknown')
                )
                return True
            else:
                logger.warning(f"Incomplete sentiment analysis for {conversation.session_id}: {results}")
                return False

        except Exception as e:
            logger.error(f"Error performing sentiment analysis for {conversation.session_id}: {e}")
            return False

    def process_continuous_sentiment(self, interval_minutes: int) -> Dict[str, Any]:
        """
        Process sentiment analysis for conversations continuously.

        Args:
            interval_minutes: Processing interval in minutes

        Returns:
            Dictionary with processing results
        """
        with self._lock:
            if self._processing:
                return {"status": "already_running", "message": "Processing is already running"}

            self._processing = True
            processing_id = str(uuid.uuid4())
            self._current_processing_id = processing_id

        try:
            start_time = time.time()
            logger.info(f"Starting continuous sentiment processing (interval: {interval_minutes}min, ID: {processing_id})")

            # Reset daily data if needed
            self._reset_daily_data_if_needed()

            # Get conversations that need analysis
            conversations_for_analysis = self.conversation_tracker.get_conversations_for_periodic_analysis(
                max_age_hours=self.max_age_hours,
                min_messages=self.min_messages
            )

            logger.info(f"Found {len(conversations_for_analysis)} conversations for analysis")

            # Process conversations in batches
            analyzed_count = 0
            skipped_count = 0

            for i in range(0, len(conversations_for_analysis), self.batch_size):
                batch = conversations_for_analysis[i:i + self.batch_size]

                for tracking_info in batch:
                    try:
                        conversation_key = f"{tracking_info.website_id}_{tracking_info.session_id}"

                        # Skip if already analyzed and not forcing reprocess
                        if (tracking_info.last_sentiment_analysis_timestamp and
                            tracking_info.last_sentiment_analysis_timestamp >= tracking_info.last_message_timestamp):

                            # Check if we already have this conversation in daily processed
                            if conversation_key not in self._daily_processed_conversations:
                                # Extract conversation data for daily storage
                                conversation = self._extract_conversation_data(
                                    tracking_info.website_id,
                                    tracking_info.session_id
                                )
                                if conversation:
                                    # Use existing sentiment analysis
                                    self._daily_processed_conversations[conversation_key] = conversation

                            skipped_count += 1
                            continue

                        # Extract conversation data
                        conversation = self._extract_conversation_data(
                            tracking_info.website_id,
                            tracking_info.session_id
                        )

                        if not conversation:
                            skipped_count += 1
                            continue

                        # Perform sentiment analysis
                        if self._perform_sentiment_analysis(conversation):
                            # Store in daily processed conversations
                            self._daily_processed_conversations[conversation_key] = conversation
                            analyzed_count += 1

                            # Update conversation tracker
                            self.conversation_tracker.update_sentiment_analysis(
                                tracking_info.website_id,
                                tracking_info.session_id,
                                conversation.analysis_timestamp
                            )
                        else:
                            skipped_count += 1

                    except Exception as e:
                        logger.error(f"Error processing conversation {tracking_info.session_id}: {e}")
                        skipped_count += 1

            # Update statistics
            processing_duration = time.time() - start_time
            with self._lock:
                self._processing_stats['total_processed_today'] += len(conversations_for_analysis)
                self._processing_stats['total_analyzed_today'] += analyzed_count
                self._processing_stats['total_skipped_today'] += skipped_count
                self._processing_stats['last_processing_time'] = int(time.time())

            result = {
                "status": "completed",
                "processing_id": processing_id,
                "processing_timestamp": int(start_time),
                "processing_interval_minutes": interval_minutes,
                "conversations_processed": len(conversations_for_analysis),
                "conversations_analyzed": analyzed_count,
                "conversations_skipped": skipped_count,
                "processing_duration_seconds": processing_duration,
                "total_daily_conversations": len(self._daily_processed_conversations)
            }

            logger.info(f"Continuous processing completed: {analyzed_count} analyzed, {skipped_count} skipped, "
                       f"{processing_duration:.2f}s duration, {len(self._daily_processed_conversations)} total daily")

            return result

        finally:
            with self._lock:
                self._processing = False
                self._current_processing_id = None

    def get_daily_conversations(self) -> List[ConversationSentiment]:
        """
        Get all conversations processed today for daily upload.

        Returns:
            List of ConversationSentiment objects processed today
        """
        self._reset_daily_data_if_needed()
        with self._lock:
            return list(self._daily_processed_conversations.values())

    def get_processing_status(self) -> Dict[str, Any]:
        """
        Get current processing status.

        Returns:
            Dictionary with processing status information
        """
        with self._lock:
            return {
                "is_running": self._processing,
                "current_processing_id": self._current_processing_id,
                "daily_stats": self._processing_stats.copy(),
                "total_daily_conversations": len(self._daily_processed_conversations),
                "last_reset_date": self._last_reset_date
            }

    def clear_daily_data(self):
        """Clear daily processed data (called after successful upload)"""
        with self._lock:
            self._daily_processed_conversations.clear()
            logger.info("Cleared daily processed conversations after successful upload")

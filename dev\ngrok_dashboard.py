"""
Ngrok Dashboard
--------------
Interactive dashboard for managing ngrok tunnels
"""

import os
import sys
import time
import logging
import json
from pyngrok import ngrok, conf
from app.core.config import Config

# Set up logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/ngrok_dashboard.log')
    ]
)
logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

class NgrokDashboard:
    """Interactive dashboard for managing ngrok tunnels"""

    def __init__(self):
        """Initialize the dashboard"""
        # Load configuration
        try:
            config = Config()
            _, _, _, self.auth_token = config.get_credentials()
            self.server_config = config.get_server_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            self.auth_token = None
            self.server_config = {"port": 5000, "host": "0.0.0.0"}

        # Set the auth token if provided
        if self.auth_token:
            logger.info("Setting ngrok authentication token")
            ngrok.set_auth_token(self.auth_token)
        else:
            print("⚠️ No ngrok auth token found in config. Using anonymous session (limited to 2 hours).")

    def clear_screen(self):
        """Clear the terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def print_banner(self):
        """Print the dashboard banner"""
        self.clear_screen()
        print("""
        ╔════════════════════════════════════════════════════════════╗
        ║                                                            ║
        ║   Ngrok Dashboard                                          ║
        ║   --------------                                           ║
        ║   Interactive dashboard for managing ngrok tunnels         ║
        ║                                                            ║
        ╚════════════════════════════════════════════════════════════╝
        """)

    def print_menu(self):
        """Print the dashboard menu"""
        print("\nOptions:")
        print("1. Start a new tunnel")
        print("2. List active tunnels")
        print("3. Stop a tunnel")
        print("4. Stop all tunnels")
        print("5. View ngrok configuration")
        print("6. Exit")
        print("\nEnter your choice (1-6): ", end="")

    def start_tunnel(self):
        """Start a new ngrok tunnel"""
        self.clear_screen()
        print("=== Start a New Tunnel ===\n")

        # Get the port
        try:
            port = input(f"Enter the port to tunnel to (default: {self.server_config['port']}): ")
            port = int(port) if port else self.server_config['port']
        except ValueError:
            print("⚠️ Invalid port number. Using default port.")
            port = self.server_config['port']

        # Get the tunnel type
        tunnel_type = input("Enter the tunnel type (http/tcp, default: http): ").lower()
        tunnel_type = tunnel_type if tunnel_type in ['http', 'tcp'] else 'http'

        # Start the tunnel
        try:
            if tunnel_type == 'http':
                public_url = ngrok.connect(port, "http").public_url
            else:
                public_url = ngrok.connect(port, "tcp").public_url

            print("\n✅ Tunnel started successfully!")
            print(f"🌐 Public URL: {public_url}")

            if tunnel_type == 'http':
                print(f"🔗 Webhook URL: {public_url}/webhook")
                print(f"🔗 Sentiment Widget Action URL: {public_url}/sentiment_widget/action")
                print(f"🔗 Sentiment Status URL: {public_url}/sentiment_status/{{request_id}}")

            input("\nPress Enter to continue...")
        except Exception as e:
            logger.error(f"Error starting tunnel: {e}")
            print(f"\n❌ Error starting tunnel: {e}")
            input("\nPress Enter to continue...")

    def list_tunnels(self):
        """List all active ngrok tunnels"""
        self.clear_screen()
        print("=== Active Tunnels ===\n")

        tunnels = ngrok.get_tunnels()
        if not tunnels:
            print("No active tunnels found.")
        else:
            for i, tunnel in enumerate(tunnels, 1):
                print(f"{i}. {tunnel.public_url} -> {tunnel.config['addr']}")

        input("\nPress Enter to continue...")

    def stop_tunnel(self):
        """Stop a specific ngrok tunnel"""
        self.clear_screen()
        print("=== Stop a Tunnel ===\n")

        tunnels = ngrok.get_tunnels()
        if not tunnels:
            print("No active tunnels found.")
            input("\nPress Enter to continue...")
            return

        # List the tunnels
        for i, tunnel in enumerate(tunnels, 1):
            print(f"{i}. {tunnel.public_url} -> {tunnel.config['addr']}")

        # Get the tunnel to stop
        try:
            choice = input("\nEnter the number of the tunnel to stop (or 'c' to cancel): ")
            if choice.lower() == 'c':
                return

            choice = int(choice)
            if choice < 1 or choice > len(tunnels):
                print("⚠️ Invalid choice.")
                input("\nPress Enter to continue...")
                return

            # Stop the tunnel
            tunnel = tunnels[choice - 1]
            ngrok.disconnect(tunnel.public_url)
            print(f"\n✅ Tunnel {tunnel.public_url} stopped successfully!")
            input("\nPress Enter to continue...")
        except ValueError:
            print("⚠️ Invalid choice.")
            input("\nPress Enter to continue...")
        except Exception as e:
            logger.error(f"Error stopping tunnel: {e}")
            print(f"\n❌ Error stopping tunnel: {e}")
            input("\nPress Enter to continue...")

    def stop_all_tunnels(self):
        """Stop all active ngrok tunnels"""
        self.clear_screen()
        print("=== Stop All Tunnels ===\n")

        # First try to get tunnels through the API
        tunnels = ngrok.get_tunnels()
        if not tunnels:
            print("No active tunnels found through the API.")
        else:
            print(f"Found {len(tunnels)} active tunnels through the API.")

        # Confirm
        confirm = input(f"Are you sure you want to stop all ngrok processes and tunnels? (y/n): ")
        if confirm.lower() != 'y':
            print("\nOperation cancelled.")
            input("\nPress Enter to continue...")
            return

        # Use the NgrokService to kill all processes
        try:
            print("\nAttempting to kill all ngrok processes...")
            from app.services.ngrok_service import NgrokService
            killed = NgrokService.kill_all_ngrok_processes()

            if killed:
                print("✅ Successfully terminated ngrok processes!")
            else:
                print("ℹ️ No ngrok processes found to terminate.")

            # Also try to disconnect any tunnels through the API
            if tunnels:
                print("\nDisconnecting tunnels through the API...")
                for tunnel in tunnels:
                    try:
                        ngrok.disconnect(tunnel.public_url)
                        print(f"✅ Disconnected tunnel: {tunnel.public_url}")
                    except Exception as e:
                        print(f"⚠️ Error disconnecting tunnel {tunnel.public_url}: {e}")

            print(f"\n✅ All tunnels and processes stopped successfully!")
            input("\nPress Enter to continue...")
        except Exception as e:
            logger.error(f"Error stopping tunnels: {e}")
            print(f"\n❌ Error stopping tunnels: {e}")
            input("\nPress Enter to continue...")

    def view_config(self):
        """View the ngrok configuration"""
        self.clear_screen()
        print("=== Ngrok Configuration ===\n")

        # Get the configuration
        try:
            config = conf.get_default()
            config_dict = {
                "auth_token": self.auth_token or "Not set",
                "region": config.region,
                "console_ui": config.console_ui,
                "log_level": config.log_level,
                "log_format": config.log_format,
                "log_timestamp": config.log_timestamp,
                "update_check": config.update_check,
                "web_addr": config.web_addr
            }

            # Print the configuration
            for key, value in config_dict.items():
                print(f"{key}: {value}")

            input("\nPress Enter to continue...")
        except Exception as e:
            logger.error(f"Error getting configuration: {e}")
            print(f"\n❌ Error getting configuration: {e}")
            input("\nPress Enter to continue...")

    def run(self):
        """Run the dashboard"""
        while True:
            self.print_banner()
            self.print_menu()

            choice = input()

            if choice == '1':
                self.start_tunnel()
            elif choice == '2':
                self.list_tunnels()
            elif choice == '3':
                self.stop_tunnel()
            elif choice == '4':
                self.stop_all_tunnels()
            elif choice == '5':
                self.view_config()
            elif choice == '6':
                # Exit
                self.clear_screen()
                print("Exiting Ngrok Dashboard...")
                # Stop all tunnels
                try:
                    tunnels = ngrok.get_tunnels()
                    if tunnels:
                        print(f"Stopping {len(tunnels)} active tunnels...")
                        for tunnel in tunnels:
                            ngrok.disconnect(tunnel.public_url)
                        print("All tunnels stopped successfully!")
                except Exception as e:
                    logger.error(f"Error stopping tunnels: {e}")
                    print(f"Error stopping tunnels: {e}")
                break
            else:
                print("⚠️ Invalid choice. Please try again.")
                time.sleep(1)

if __name__ == "__main__":
    dashboard = NgrokDashboard()
    dashboard.run()

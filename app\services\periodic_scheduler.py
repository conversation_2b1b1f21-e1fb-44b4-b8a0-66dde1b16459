"""
Periodic Scheduler Service
-------------------------
Service for scheduling and managing periodic sentiment analysis tasks
"""

import threading
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import schedule

from app.services.continuous_sentiment_processor import ContinuousSentimentProcessor
from app.services.daily_report_generator import DailyReportGenerator
from app.services.postgresql_service import PostgreSQLService

logger = logging.getLogger(__name__)


class PeriodicScheduler:
    """
    Service for scheduling and managing continuous sentiment analysis tasks.
    This service:
    - Schedules continuous sentiment processing at configured intervals
    - Schedules daily report generation and MongoDB upload
    - Manages the execution of scheduled tasks
    - Provides status and control over scheduled operations
    """

    def __init__(self, config: Dict[str, Any],
                 continuous_processor: ContinuousSentimentProcessor,
                 daily_generator: DailyReportGenerator):
        """
        Initialize the periodic scheduler.

        Args:
            config: Periodic sentiment configuration
            continuous_processor: ContinuousSentimentProcessor instance
            daily_generator: DailyReportGenerator instance
        """
        self.config = config
        self.continuous_processor = continuous_processor
        self.daily_generator = daily_generator

        # Initialize PostgreSQL service
        postgresql_config = config.get('postgresql', {})
        self.postgresql_service = PostgreSQLService(postgresql_config)

        # Scheduler settings
        self.enabled = config.get('enabled', True)
        self.periodic_intervals = config.get('processing_intervals', [30, 60, 180, 360])  # Continuous processing

        # PostgreSQL upload settings
        self.upload_time = postgresql_config.get('upload_time', '09:00')  # 9:00 AM upload

        # Threading
        self._lock = threading.Lock()
        self._running = False
        self._scheduler_thread = None

        # Task tracking
        self._last_periodic_runs = {}  # interval -> timestamp
        self._last_upload_run = None
        self._task_stats = {
            'continuous_runs': 0,
            'continuous_failures': 0,
            'upload_runs': 0,
            'upload_failures': 0
        }

        logger.info(f"PeriodicScheduler initialized with intervals: {self.periodic_intervals}")

    def start(self):
        """Start the periodic scheduler"""
        with self._lock:
            if self._running:
                logger.warning("Scheduler is already running")
                return

            if not self.enabled:
                logger.info("Periodic scheduling is disabled")
                return

            self._running = True

            # Clear any existing scheduled jobs
            schedule.clear()

            # Schedule continuous sentiment processing
            for interval in self.periodic_intervals:
                schedule.every(interval).minutes.do(self._run_continuous_processing, interval)
                logger.info(f"Scheduled continuous processing every {interval} minutes")

            # Schedule PostgreSQL upload (daily at 9:00 AM)
            if self.postgresql_service.enabled:
                schedule.every().day.at(self.upload_time).do(self._run_postgresql_upload)
                logger.info(f"Scheduled PostgreSQL upload at {self.upload_time}")

            # Start scheduler thread
            self._scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self._scheduler_thread.start()

            logger.info("Periodic scheduler started")

    def stop(self):
        """Stop the periodic scheduler"""
        with self._lock:
            if not self._running:
                logger.warning("Scheduler is not running")
                return

            self._running = False
            schedule.clear()

            # Close MongoDB connection
            if self.mongodb_service:
                self.mongodb_service.close()

            logger.info("Periodic scheduler stopped")

    def _scheduler_loop(self):
        """Main scheduler loop"""
        logger.info("Scheduler loop started")

        while self._running:
            try:
                schedule.run_pending()
                time.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                time.sleep(60)  # Wait longer on error

        logger.info("Scheduler loop stopped")

    def _run_continuous_processing(self, interval_minutes: int):
        """Run continuous sentiment processing for a specific interval"""
        try:
            logger.info(f"Starting scheduled continuous processing (interval: {interval_minutes}min)")

            # Check if processor is already running
            status = self.continuous_processor.get_processing_status()
            if status.get('is_running', False):
                logger.warning(f"Skipping continuous processing ({interval_minutes}min) - already running")
                return

            # Run continuous processing
            result = self.continuous_processor.process_continuous_sentiment(interval_minutes)

            # Update tracking
            with self._lock:
                self._last_periodic_runs[interval_minutes] = int(time.time())
                self._task_stats['continuous_runs'] += 1

            logger.info(f"Scheduled continuous processing completed (interval: {interval_minutes}min): "
                       f"{result.get('conversations_analyzed', 0)} analyzed, "
                       f"{result.get('conversations_skipped', 0)} skipped")

        except Exception as e:
            logger.error(f"Error in scheduled continuous processing ({interval_minutes}min): {e}")
            with self._lock:
                self._task_stats['continuous_failures'] += 1

    def _run_postgresql_upload(self):
        """Generate daily report and upload to PostgreSQL"""
        try:
            logger.info("Starting scheduled daily report generation and PostgreSQL upload")

            if not self.postgresql_service.enabled:
                logger.warning("PostgreSQL upload skipped - service disabled")
                return

            # Generate daily report from continuous processing data
            report = self.daily_generator.generate_daily_report()

            # Convert records to dictionary format for PostgreSQL
            records_dict = self.daily_generator.convert_records_to_dict_list(report.records)

            # Upload to PostgreSQL
            success = self.postgresql_service.upload_daily_sentiment_data(records_dict, report.report_date)

            if success:
                logger.info(f"Successfully uploaded daily report {report.report_date} to PostgreSQL ({len(records_dict)} records)")

                # Save backup file
                backup_path = f"data/backup_reports/backup_report_{report.report_date}.json"
                self.daily_generator.save_report_to_file(report, backup_path)

                # Clear daily data after successful upload
                self.continuous_processor.clear_daily_data()

                # Update tracking
                with self._lock:
                    self._last_upload_run = int(time.time())
                    self._task_stats['upload_runs'] += 1
            else:
                logger.error(f"Failed to upload daily report {report.report_date} to PostgreSQL")
                with self._lock:
                    self._task_stats['upload_failures'] += 1

        except Exception as e:
            logger.error(f"Error in scheduled PostgreSQL upload: {e}")
            with self._lock:
                self._task_stats['upload_failures'] += 1

    def trigger_daily_upload(self):
        """
        Manually trigger daily report generation and PostgreSQL upload.

        Returns:
            Dictionary with upload result
        """
        try:
            # Generate daily report
            report = self.daily_generator.generate_daily_report()

            # Upload to PostgreSQL
            if self.postgresql_service.enabled:
                # Convert records to dictionary format
                records_dict = self.daily_generator.convert_records_to_dict_list(report.records)

                success = self.postgresql_service.upload_daily_sentiment_data(records_dict, report.report_date)

                if success:
                    # Save backup file
                    backup_path = f"data/backup_reports/backup_report_{report.report_date}.json"
                    self.daily_generator.save_report_to_file(report, backup_path)

                    # Clear daily data after successful upload
                    self.continuous_processor.clear_daily_data()

                    with self._lock:
                        self._last_upload_run = int(time.time())
                        self._task_stats['upload_runs'] += 1

                    return {
                        "status": "success",
                        "report_date": report.report_date,
                        "total_records": report.total_records,
                        "total_conversations": report.total_conversations,
                        "uploaded_to_postgresql": True
                    }
                else:
                    with self._lock:
                        self._task_stats['upload_failures'] += 1
                    return {
                        "status": "upload_failed",
                        "report_date": report.report_date,
                        "total_records": report.total_records,
                        "total_conversations": report.total_conversations,
                        "uploaded_to_postgresql": False
                    }
            else:
                return {
                    "status": "success",
                    "report_date": report.report_date,
                    "total_records": report.total_records,
                    "total_conversations": report.total_conversations,
                    "uploaded_to_postgresql": False,
                    "message": "PostgreSQL disabled"
                }

        except Exception as e:
            logger.error(f"Error in manual daily upload: {e}")
            with self._lock:
                self._task_stats['upload_failures'] += 1
            raise

    def get_scheduler_status(self) -> Dict[str, Any]:
        """
        Get scheduler status and statistics.

        Returns:
            Dictionary with scheduler status information
        """
        with self._lock:
            # Calculate next scheduled runs
            next_runs = {}
            current_time = int(time.time())

            # Calculate next periodic runs
            for interval in self.periodic_intervals:
                last_run = self._last_periodic_runs.get(interval, 0)
                next_run = last_run + (interval * 60)  # Convert minutes to seconds
                next_runs[f"continuous_{interval}min"] = next_run if next_run > current_time else current_time

            # Calculate next MongoDB upload
            now = datetime.now()
            upload_hour, upload_minute = map(int, self.upload_time.split(':'))
            next_upload = now.replace(hour=upload_hour, minute=upload_minute, second=0, microsecond=0)

            if next_upload <= now:
                next_upload += timedelta(days=1)

            next_runs["postgresql_upload"] = int(next_upload.timestamp())

            # Get continuous processor status
            processor_status = self.continuous_processor.get_processing_status()

            return {
                "enabled": self.enabled,
                "running": self._running,
                "continuous_intervals": self.periodic_intervals,
                "upload_time": self.upload_time,
                "last_periodic_runs": self._last_periodic_runs.copy(),
                "last_upload_run": self._last_upload_run,
                "next_scheduled_runs": next_runs,
                "task_statistics": self._task_stats.copy(),
                "pending_jobs": len(schedule.jobs),
                "postgresql_enabled": self.postgresql_service.enabled,
                "continuous_processor_status": processor_status
            }

    def get_next_scheduled_times(self) -> Dict[str, str]:
        """
        Get human-readable next scheduled times.

        Returns:
            Dictionary with next scheduled times
        """
        status = self.get_scheduler_status()
        next_runs = status.get("next_scheduled_runs", {})

        readable_times = {}
        for task, timestamp in next_runs.items():
            readable_times[task] = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

        return readable_times

    def update_configuration(self, new_config: Dict[str, Any]):
        """
        Update scheduler configuration and restart if necessary.

        Args:
            new_config: New configuration dictionary
        """
        with self._lock:
            old_enabled = self.enabled
            old_intervals = self.periodic_intervals.copy()
            old_nightly_time = self.nightly_time

            # Update configuration
            self.config.update(new_config)
            self.enabled = new_config.get('enabled', self.enabled)
            self.periodic_intervals = new_config.get('periodic_statistics', self.periodic_intervals)

            nightly_config = new_config.get('nightly_report', {})
            self.nightly_time = nightly_config.get('start_nightly_time', self.nightly_time)

            # Check if restart is needed
            restart_needed = (
                old_enabled != self.enabled or
                old_intervals != self.periodic_intervals or
                old_nightly_time != self.nightly_time
            )

            if restart_needed and self._running:
                logger.info("Configuration changed, restarting scheduler")
                self._running = False  # Stop current loop

                # Wait a moment for the loop to stop
                time.sleep(1)

                # Restart with new configuration
                self.start()

            logger.info(f"Scheduler configuration updated: enabled={self.enabled}, "
                       f"intervals={self.periodic_intervals}, nightly_time={self.nightly_time}")

    def cleanup_old_data(self, max_age_days: int = 30):
        """
        Clean up old tracking and processing data.

        Args:
            max_age_days: Maximum age in days to keep data
        """
        try:
            # Clean up conversation tracker data
            self.nightly_generator.conversation_tracker.cleanup_old_conversations(max_age_days)

            logger.info(f"Cleaned up data older than {max_age_days} days")

        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")

    def is_running(self) -> bool:
        """Check if scheduler is running"""
        with self._lock:
            return self._running

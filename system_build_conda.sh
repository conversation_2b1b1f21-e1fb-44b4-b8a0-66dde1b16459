#!/bin/bash

# ======== <PERSON><PERSON><PERSON>nh cần chỉnh =========
project_path="uppromote-system-sentiment-plugin-server"
conda_env_name="fastapi_env"
domain="ai-tools.secomapp.com"
email="<EMAIL>"
branch="main-deploy"

# ======== Cài các công cụ cơ bản =========
apt update && apt upgrade -y
apt install -y nginx certbot python3-certbot-nginx git curl wget ufw

# ======== Cài Miniconda nếu chưa có =========
if ! command -v conda &> /dev/null; then
    echo "Installing Miniconda..."
    wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh
    bash miniconda.sh -b -p /opt/miniconda
    echo 'export PATH="/opt/miniconda/bin:$PATH"' >> ~/.bashrc
    export PATH="/opt/miniconda/bin:$PATH"
    source ~/.bashrc
fi

# ======== Thiết lập firewall =========
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw --force enable

# ======== Clone code nếu chưa có =========
if [ ! -d "$project_path" ]; then
  git clone https://your-repo-url.git "$project_path"
fi

cd "$project_path" || exit 1
git fetch origin
git reset --hard origin/$branch

# ======== Tạo Conda Env cho MLOps =========
eval "$(conda shell.bash hook)"
conda create -y -n $conda_env_name python=3.10
conda activate $conda_env_name
pip install --upgrade pip
pip install -r requirements.txt
conda deactivate

# ======== Tạo systemd service để phục vụ FastAPI =========
tee /etc/systemd/system/fastapi.service > /dev/null << EOF
[Unit]
Description=FastAPI with ML Model
After=network.target

[Service]
Type=simple
ExecStart=/opt/miniconda/envs/$conda_env_name/bin/uvicorn main:app --host 127.0.0.1 --port 8000
WorkingDirectory=$project_path
Restart=always

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable fastapi
systemctl restart fastapi

# ======== Cấu hình NGINX + HTTPS =========
tee /etc/nginx/sites-available/$domain > /dev/null << EOF
server {
    listen 80;
    server_name $domain;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

ln -sf /etc/nginx/sites-available/$domain /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx
certbot --nginx -d $domain --non-interactive --agree-tos -m $email

# ======== Tạo script deploy.sh =========
cat > $project_path/deploy.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
git pull origin main

eval "$(conda shell.bash hook)"
conda activate fastapi_env
pip install -r requirements.txt
conda deactivate

systemctl restart fastapi
echo "✅ Deploy complete."
EOF
chmod +x $project_path/deploy.sh

# ======== Tạo cronjob train model hàng tuần (tuỳ chọn) =========
(crontab -l 2>/dev/null; echo "0 3 * * 1 cd $project_path && /opt/miniconda/envs/$conda_env_name/bin/python train.py >> train.log 2>&1") | crontab -

echo "✅ Đã thiết lập MLOps server thành công: https://$domain"

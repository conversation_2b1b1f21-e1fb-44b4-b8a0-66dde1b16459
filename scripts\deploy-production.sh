#!/bin/bash

# Production Blue-Green Deployment Script
# Usage: ./deploy-production.sh <docker-image-tag>

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE_BLUE="$PROJECT_DIR/docker-compose.production.blue.yml"
COMPOSE_FILE_GREEN="$PROJECT_DIR/docker-compose.production.green.yml"
ENV_FILE="$PROJECT_DIR/.env.production"
BACKUP_DIR="$PROJECT_DIR/backups/production"
LOG_FILE="$PROJECT_DIR/logs/deploy-production.log"
NGINX_CONFIG_DIR="$PROJECT_DIR/nginx"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if image tag is provided
if [ $# -eq 0 ]; then
    error "Usage: $0 <docker-image-tag>"
fi

IMAGE_TAG="$1"

log "Starting production blue-green deployment with image: $IMAGE_TAG"

# Create necessary directories
mkdir -p "$BACKUP_DIR" "$(dirname "$LOG_FILE")"

# Load environment variables
if [ -f "$ENV_FILE" ]; then
    set -a
    source "$ENV_FILE"
    set +a
    log "Loaded environment variables from $ENV_FILE"
fi

# Determine current and target environments
CURRENT_ENV_FILE="$PROJECT_DIR/.current-production-env"
if [ -f "$CURRENT_ENV_FILE" ]; then
    CURRENT_ENV=$(cat "$CURRENT_ENV_FILE")
else
    CURRENT_ENV="blue"
fi

if [ "$CURRENT_ENV" = "blue" ]; then
    TARGET_ENV="green"
    CURRENT_COMPOSE_FILE="$COMPOSE_FILE_BLUE"
    TARGET_COMPOSE_FILE="$COMPOSE_FILE_GREEN"
    CURRENT_PORT="8001"
    TARGET_PORT="8002"
else
    TARGET_ENV="blue"
    CURRENT_COMPOSE_FILE="$COMPOSE_FILE_GREEN"
    TARGET_COMPOSE_FILE="$COMPOSE_FILE_BLUE"
    CURRENT_PORT="8002"
    TARGET_PORT="8001"
fi

log "Current environment: $CURRENT_ENV (port $CURRENT_PORT)"
log "Target environment: $TARGET_ENV (port $TARGET_PORT)"

# Pre-deployment checks
log "Running pre-deployment checks..."

# Check Docker
if ! command -v docker &> /dev/null; then
    error "Docker is not installed or not in PATH"
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    error "Docker Compose is not installed or not in PATH"
fi

# Check if current environment is running
if docker-compose -f "$CURRENT_COMPOSE_FILE" ps | grep -q "Up"; then
    log "Current environment ($CURRENT_ENV) is running"
    CURRENT_RUNNING=true
else
    log "Current environment ($CURRENT_ENV) is not running"
    CURRENT_RUNNING=false
fi

# Create comprehensive backup
log "Creating comprehensive backup..."
BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_PATH="$BACKUP_DIR/production_backup_$BACKUP_TIMESTAMP"

mkdir -p "$BACKUP_PATH"

# Backup database
if [ "$CURRENT_RUNNING" = true ]; then
    log "Backing up PostgreSQL database..."
    docker-compose -f "$CURRENT_COMPOSE_FILE" exec -T postgres pg_dump -U postgres sentiment_analysis > "$BACKUP_PATH/database_backup.sql" || error "Database backup failed"
    
    # Backup application data
    log "Backing up application data..."
    cp -r "$PROJECT_DIR/data" "$BACKUP_PATH/" 2>/dev/null || warning "Data backup failed"
fi

# Backup configuration
cp "$PROJECT_DIR/config/"* "$BACKUP_PATH/" 2>/dev/null || warning "Config backup failed"
cp "$PROJECT_DIR/postgresql_config.yaml" "$BACKUP_PATH/" 2>/dev/null || warning "PostgreSQL config backup failed"

# Backup current environment info
echo "$CURRENT_ENV" > "$BACKUP_PATH/current_env.txt"
echo "$(date)" > "$BACKUP_PATH/backup_timestamp.txt"

success "Backup completed: $BACKUP_PATH"

# Pull new image
log "Pulling new Docker image: $IMAGE_TAG"
docker pull "$IMAGE_TAG" || error "Failed to pull Docker image"

# Update target compose file with new image
log "Updating target compose file with new image tag..."
sed -i.bak "s|image: .*sentiment-analysis.*|image: $IMAGE_TAG|g" "$TARGET_COMPOSE_FILE"

# Deploy to target environment
log "Deploying to target environment ($TARGET_ENV)..."

# Stop target environment if running
docker-compose -f "$TARGET_COMPOSE_FILE" down || warning "Failed to stop target environment"

# Start target environment
docker-compose -f "$TARGET_COMPOSE_FILE" up -d || error "Failed to start target environment"

# Wait for target environment to be ready
log "Waiting for target environment to be ready..."
sleep 60

# Comprehensive health checks
log "Running comprehensive health checks on target environment..."

# Check main application
for i in {1..20}; do
    if curl -f "http://localhost:$TARGET_PORT/health" &>/dev/null; then
        success "Target application health check passed"
        break
    fi
    if [ $i -eq 20 ]; then
        error "Target application health check failed after 20 attempts"
    fi
    log "Health check attempt $i/20 failed, retrying in 15 seconds..."
    sleep 15
done

# Check database connectivity
if docker-compose -f "$TARGET_COMPOSE_FILE" exec -T postgres pg_isready -U postgres &>/dev/null; then
    success "Target database health check passed"
else
    error "Target database health check failed"
fi

# Run comprehensive smoke tests
log "Running comprehensive smoke tests on target environment..."

# Test API endpoints
curl -f "http://localhost:$TARGET_PORT/api/health" || error "API health endpoint failed"

# Test sentiment analysis functionality (if available)
# Add more comprehensive tests here

# Load testing (optional)
if command -v ab &> /dev/null; then
    log "Running basic load test..."
    ab -n 100 -c 10 "http://localhost:$TARGET_PORT/health" || warning "Load test failed"
fi

success "All tests passed on target environment"

# Switch traffic to target environment
log "Switching traffic to target environment..."

# Update nginx configuration
if [ -f "$NGINX_CONFIG_DIR/nginx.conf" ]; then
    # Create backup of current nginx config
    cp "$NGINX_CONFIG_DIR/nginx.conf" "$BACKUP_PATH/nginx.conf.bak"
    
    # Update upstream configuration
    sed -i "s/server sentiment-api:$CURRENT_PORT/server sentiment-api:$TARGET_PORT/g" "$NGINX_CONFIG_DIR/nginx.conf"
    
    # Reload nginx
    docker-compose -f "$TARGET_COMPOSE_FILE" exec nginx nginx -s reload || error "Failed to reload nginx"
    
    log "Nginx configuration updated and reloaded"
fi

# Wait for traffic switch to complete
log "Waiting for traffic switch to complete..."
sleep 30

# Verify production traffic
log "Verifying production traffic on new environment..."
for i in {1..10}; do
    if curl -f "http://localhost/health" &>/dev/null; then
        success "Production traffic verification passed"
        break
    fi
    if [ $i -eq 10 ]; then
        error "Production traffic verification failed"
    fi
    log "Traffic verification attempt $i/10 failed, retrying in 10 seconds..."
    sleep 10
done

# Monitor for a few minutes
log "Monitoring new environment for 3 minutes..."
for i in {1..18}; do
    if ! curl -f "http://localhost/health" &>/dev/null; then
        error "Production environment failed during monitoring at minute $((i/6))"
    fi
    sleep 10
done

success "Production environment stable after monitoring period"

# Stop old environment
if [ "$CURRENT_RUNNING" = true ]; then
    log "Stopping old environment ($CURRENT_ENV)..."
    docker-compose -f "$CURRENT_COMPOSE_FILE" down || warning "Failed to stop old environment"
fi

# Update current environment marker
echo "$TARGET_ENV" > "$CURRENT_ENV_FILE"
echo "$IMAGE_TAG" > "$PROJECT_DIR/.current-production-version"
echo "$(date)" > "$PROJECT_DIR/.last-production-deployment"

# Cleanup old images
log "Cleaning up old Docker images..."
docker image prune -f || warning "Failed to cleanup old images"

success "Production blue-green deployment completed successfully!"
log "Deployed version: $IMAGE_TAG"
log "Active environment: $TARGET_ENV"
log "Deployment time: $(date)"

# Send notification
if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚀 Production deployment successful! Version: $IMAGE_TAG, Environment: $TARGET_ENV\"}" \
        "$SLACK_WEBHOOK_URL" || warning "Failed to send Slack notification"
fi

# Create rollback script
ROLLBACK_SCRIPT="$PROJECT_DIR/rollback-production.sh"
cat > "$ROLLBACK_SCRIPT" << EOF
#!/bin/bash
# Auto-generated rollback script for deployment $IMAGE_TAG
echo "Rolling back from $TARGET_ENV to $CURRENT_ENV..."
echo "$CURRENT_ENV" > "$CURRENT_ENV_FILE"
# Add specific rollback commands here
echo "Rollback completed"
EOF
chmod +x "$ROLLBACK_SCRIPT"

log "Rollback script created: $ROLLBACK_SCRIPT"
log "Production deployment script completed"

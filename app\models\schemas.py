"""
Sentiment Analysis API System
----------------------------
Data models and schemas for the FastAPI application
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime


class SentimentRequest(BaseModel):
    """Schema for sentiment analysis request"""
    item_id: str
    session_id: str
    website_id: str


class SentimentResponse(BaseModel):
    """Schema for sentiment analysis response"""
    data: Dict[str, Any]


class ProcessingStatus(BaseModel):
    """Schema for processing status response"""
    status: str
    request_id: str
    message: Optional[str] = None


class QueueStatus(BaseModel):
    """Schema for queue status response"""
    status: str
    queue: Dict[str, Any]


class ErrorResponse(BaseModel):
    """Schema for error response"""
    status: str = "error"
    message: str


# Periodic Sentiment Analysis Schemas

class UserInfo(BaseModel):
    """Schema for user information"""
    nickname: str
    user_id: str


class AgentInfo(BaseModel):
    """Schema for agent information"""
    name: Optional[str] = None
    role: str = "operator"


class MessageData(BaseModel):
    """Schema for individual message"""
    type: str
    origin: str
    text: str
    timestamp: int
    fingerprint: int
    role: str


class SentimentAnalysisResult(BaseModel):
    """Schema for sentiment analysis result"""
    overall_emotion: str
    sentiment_shift: str
    latest_sentiment: str


class ConversationSentiment(BaseModel):
    """Schema for conversation sentiment analysis"""
    session_id: str
    website_id: str
    user: UserInfo
    agents: List[AgentInfo]
    conversation_start: int
    conversation_end: Optional[int] = None
    messages: List[MessageData]
    sentiment_analysis: SentimentAnalysisResult
    analysis_timestamp: int
    message_count: int
    is_completed: bool = False


class PeriodicSentimentResult(BaseModel):
    """Schema for periodic sentiment processing result"""
    processing_id: str
    processing_timestamp: int
    processing_interval_minutes: int
    conversations_processed: int
    conversations_analyzed: int
    conversations_skipped: int
    processing_duration_seconds: float
    results: List[ConversationSentiment]


class NightlyReport(BaseModel):
    """Schema for nightly sentiment report"""
    report_id: str
    report_date: str  # YYYY-MM-DD format
    generation_timestamp: int
    start_nightly_time: str
    end_nightly_time: str

    # Summary statistics
    total_conversations: int
    completed_conversations: int
    ongoing_conversations: int
    total_messages: int

    # Sentiment distribution
    sentiment_distribution: Dict[str, int]  # emotion -> count

    # Conversations data
    conversations: List[ConversationSentiment]

    # Processing metadata
    processing_duration_seconds: float
    data_collection_period: str


class ConversationTrackingInfo(BaseModel):
    """Schema for tracking conversation state"""
    session_id: str
    website_id: str
    last_message_timestamp: int
    last_sentiment_analysis_timestamp: Optional[int] = None
    message_count: int
    is_completed: bool = False
    completion_timestamp: Optional[int] = None


class PeriodicProcessingRequest(BaseModel):
    """Schema for manual periodic processing request"""
    interval_minutes: Optional[int] = None
    force_reprocess: bool = False


class PeriodicProcessingStatus(BaseModel):
    """Schema for periodic processing status"""
    is_running: bool
    current_processing_id: Optional[str] = None
    last_processing_timestamp: Optional[int] = None
    next_scheduled_processing: Optional[int] = None
    active_intervals: List[int]


# Simplified Daily Sentiment Report Schemas

class DailySentimentRecord(BaseModel):
    """Schema for individual daily sentiment record (simplified)"""
    website_id: str
    session_id: str
    user_nickname: str
    user_id: str
    agent_name: str
    agent_role: str = "operator"
    overall_emotion: str
    sentiment_shift: str
    latest_sentiment: str


class DailySentimentReport(BaseModel):
    """Schema for daily sentiment report (simplified)"""
    report_date: str  # YYYY-MM-DD format
    generation_timestamp: int
    total_records: int
    total_conversations: int
    total_agents: int
    records: List[DailySentimentRecord]
    processing_duration_seconds: float

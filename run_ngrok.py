"""
Ngrok Tunnel Manager
-------------------
Utility script to start and manage ngrok tunnels independently
"""

import os
import sys
import time
import logging
import argparse
from app.core.config import Config
from app.services.ngrok_service import NgrokService

# Set up logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/ngrok.log')
    ]
)
logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

def kill_all_ngrok_processes():
    """
    Kill all existing ngrok processes

    Returns:
        bool: True if processes were killed, False otherwise
    """
    print("Checking for existing ngrok processes...")
    killed = NgrokService.kill_all_ngrok_processes()
    if killed:
        print("✅ Existing ngrok processes terminated successfully.")
    else:
        print("ℹ️ No existing ngrok processes found.")
    return killed

def start_ngrok_tunnel(port=5000, auth_token=None, keep_alive=False, force_restart=True):
    """
    Start an ngrok tunnel to the specified port

    Args:
        port: Port to tunnel to
        auth_token: Ngrok authentication token
        keep_alive: Whether to keep the tunnel alive until interrupted
        force_restart: Whether to force restart ngrok if there are existing sessions

    Returns:
        public_url: Public URL of the tunnel
    """
    # Create the ngrok service
    ngrok_service = NgrokService(auth_token)

    try:
        # If force_restart is True, kill all existing ngrok processes first
        if force_restart:
            kill_all_ngrok_processes()

        print(f"Starting ngrok tunnel to port {port}...")

        # Start the tunnel
        public_url = ngrok_service.start_tunnel(None, port, force_restart=False)

        # Print the URLs
        print("\n" + "=" * 60)
        print(f"✅ Ngrok tunnel established successfully!")
        print("=" * 60)
        print(f"🌐 Public URL: {public_url}")
        print(f"🔗 Webhook URL: {public_url}/webhook")
        print(f"🔗 Sentiment Widget Action URL: {public_url}/sentiment_widget/action")
        print(f"🔗 Sentiment Status URL: {public_url}/sentiment_status/{{request_id}}")
        print("=" * 60 + "\n")

        # Keep the tunnel alive if requested
        if keep_alive:
            print("Tunnel is active. Press Ctrl+C to stop...")
            try:
                # Keep the script running until interrupted
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\nShutting down ngrok tunnel...")
            finally:
                # Stop the tunnel
                ngrok_service.stop_tunnel()
                print("Ngrok tunnel stopped.")

        return public_url
    except Exception as e:
        logger.error(f"Error starting ngrok tunnel: {e}")
        print(f"❌ Error starting ngrok tunnel: {e}")

        # If the error is about limited sessions, try to kill all processes and retry
        if "limited to 1 simultaneous ngrok agent session" in str(e):
            print("\n⚠️ Detected 'limited to 1 simultaneous ngrok agent session' error.")
            print("Attempting to kill all existing ngrok processes and retry...")

            if kill_all_ngrok_processes() and not force_restart:
                print("Retrying tunnel creation...")
                return start_ngrok_tunnel(port, auth_token, keep_alive, force_restart=False)

        # Try to stop the tunnel if it was started
        if ngrok_service.get_public_url():
            ngrok_service.stop_tunnel()
        return None

def main():
    """Main entry point for the script"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Start an ngrok tunnel')
    parser.add_argument('--port', type=int, default=5000, help='Port to tunnel to (default: 5000)')
    parser.add_argument('--token', type=str, help='Ngrok authentication token')
    parser.add_argument('--keep-alive', action='store_true', help='Keep the tunnel alive until interrupted')
    parser.add_argument('--kill', action='store_true', help='Kill all existing ngrok processes and exit')
    parser.add_argument('--no-restart', action='store_true', help='Do not restart existing ngrok processes')
    args = parser.parse_args()

    # Print banner
    print("""
    ╔════════════════════════════════════════════════════════════╗
    ║                                                            ║
    ║   Ngrok Tunnel Manager                                     ║
    ║   -------------------                                      ║
    ║   Utility to start and manage ngrok tunnels                ║
    ║                                                            ║
    ╚════════════════════════════════════════════════════════════╝
    """)

    # If --kill is specified, just kill all ngrok processes and exit
    if args.kill:
        print("Killing all ngrok processes...")
        killed = kill_all_ngrok_processes()
        if killed:
            print("✅ All ngrok processes terminated successfully.")
        else:
            print("ℹ️ No ngrok processes found to terminate.")
        return

    # Get the auth token from the config if not provided
    if not args.token:
        try:
            config = Config()
            _, _, _, auth_token, _ = config.get_credentials()
            if not auth_token:
                print("⚠️ No ngrok auth token found in config. Using anonymous session (limited to 2 hours).")
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            auth_token = None
    else:
        auth_token = args.token

    # Start the tunnel
    start_ngrok_tunnel(
        port=args.port,
        auth_token=auth_token,
        keep_alive=args.keep_alive,
        force_restart=not args.no_restart
    )

if __name__ == "__main__":
    main()

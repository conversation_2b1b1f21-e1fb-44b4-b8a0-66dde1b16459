import yaml
import os
import torch
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

logger = logging.getLogger(__name__)

class Config:
    """Configuration loader for the sentiment analysis system"""

    def __init__(self, config_path="config/config.yaml", postgresql_config_path="postgresql_config.yaml"):
        """Load configuration from YAML file"""
        self.config_path = config_path
        self.postgresql_config_path = postgresql_config_path
        self.config = self._load_config()
        self.postgresql_config = self._load_postgresql_config()

    def _load_config(self):
        """Load the configuration from the YAML file"""
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"Config file not found: {self.config_path}")
                raise FileNotFoundError(f"Config file not found: {self.config_path}")

            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            logger.info(f"Loaded configuration from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            raise

    def _load_postgresql_config(self):
        """Load the PostgreSQL configuration from the separate YAML file"""
        try:
            if not os.path.exists(self.postgresql_config_path):
                logger.warning(f"PostgreSQL config file not found: {self.postgresql_config_path}")
                logger.warning("PostgreSQL functionality will be disabled")
                return {"postgresql": {"enabled": False}}

            with open(self.postgresql_config_path, 'r') as f:
                postgresql_config = yaml.safe_load(f)

            logger.info(f"Loaded PostgreSQL configuration from {self.postgresql_config_path}")
            return postgresql_config
        except Exception as e:
            logger.error(f"Error loading PostgreSQL config: {e}")
            logger.warning("PostgreSQL functionality will be disabled")
            return {"postgresql": {"enabled": False}}

    def get_credentials(self):
        """Get API credentials"""
        creds = self.config.get("credentials", {})
        return (
            creds.get("CRISP_IDENTIFIER"),
            creds.get("CRISP_KEY"),
            creds.get("CRISP_WEBHOOK_SECRET"),
            creds.get("NGROK_AUTH_TOKEN"),
            creds.get("HUGGINGFACE_TOKEN")
        )

    def get_server_config(self):
        """Get server configuration"""
        server_config = self.config.get("server", {})
        return {
            "host": server_config.get("host", "0.0.0.0"),
            "port": server_config.get("port", 5000),
            "debug": server_config.get("debug", False),
            "threaded": server_config.get("threaded", True),
            "use_ngrok": server_config.get("use_ngrok", False)
        }

    def get_security_config(self):
        """Get security configuration"""
        return self.config.get("security", {})

    def get_storage_config(self):
        """Get storage configuration with absolute paths"""
        storage_config = self.config.get("storage", {})

        # Convert relative paths to absolute paths
        resolved_storage = {}
        for key, path in storage_config.items():
            if path and not os.path.isabs(path):
                resolved_storage[key] = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                    path
                )
                # Create directory if it doesn't exist
                if key.endswith('_path') and not key.endswith('_file_path'):
                    # For directory paths
                    if path.endswith('/'):
                        os.makedirs(resolved_storage[key], exist_ok=True)
                    else:
                        # For file paths, create parent directory
                        os.makedirs(os.path.dirname(resolved_storage[key]), exist_ok=True)
                else:
                    # For file paths, create parent directory
                    os.makedirs(os.path.dirname(resolved_storage[key]), exist_ok=True)
            else:
                resolved_storage[key] = path

        return resolved_storage

    def get_database_config(self):
        """Get database configuration"""
        db_config = self.config.get("database", {})
        processing_config = self.config.get("processing", {})
        storage_config = self.get_storage_config()

        # Get save path from storage configuration
        save_path = storage_config.get("messages_path", "data/messages.json")
        if not os.path.isabs(save_path):
            save_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), save_path)

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Get autosave interval from processing config (convert minutes to seconds)
        autosave_interval_min = processing_config.get("autosave_interval_min", 5)
        autosave_interval_sec = autosave_interval_min * 60

        return {
            "save_path": save_path,
            "autosave_interval_sec": autosave_interval_sec,
            "conversation_timeout_hours": db_config.get("conversation_timeout_hours", 24),
            "cleanup_interval_hours": db_config.get("cleanup_interval_hours", 48)
        }

    def get_model_config(self):
        """Get model configuration"""
        model_config = self.config.get("model", {})

        # Handle device configuration
        device = model_config.get("device", "auto")
        if device == "auto":
            device = "cuda" if torch.cuda.is_available() else "cpu"

        # Handle dtype configuration
        dtype_str = model_config.get("torch_dtype", "bfloat16")
        if dtype_str == "float16":
            torch_dtype = torch.float16
        elif dtype_str == "bfloat16":
            torch_dtype = torch.bfloat16
        else:
            torch_dtype = torch.float32

        return {
            "name": model_config.get("name", "Qwen/Qwen2.5-0.5B-Instruct"),
            "max_new_tokens": model_config.get("max_new_tokens", 100),
            "device": device,
            "torch_dtype": torch_dtype,
            "allowed_gpus": model_config.get("allowed_gpus", [])  # Get the list of allowed GPU indices
        }

    def get_processing_config(self):
        """Get processing configuration including batch and immediate modes"""
        processing_config = self.config.get("processing", {})

        # Get processing mode
        mode = processing_config.get("mode", "batch")
        if mode not in ["batch", "immediate"]:
            logger.warning(f"Invalid processing mode '{mode}', defaulting to 'batch'")
            mode = "batch"

        # Get CPU multi-threading settings
        cpu_threads = processing_config.get("cpu_threads", 4)
        cpu_cores_per_request = processing_config.get("cpu_cores_per_request", 2)
        use_threadpool = processing_config.get("use_threadpool", True)

        # Get batch processing settings
        batch_interval_sec = processing_config.get("batch_interval_sec", 1.0)
        cleanup_interval_min = processing_config.get("cleanup_interval_min", 5)
        response_timeout_min = processing_config.get("response_timeout_min", 30)

        return {
            "mode": mode,
            "cpu_threads": cpu_threads,
            "cpu_cores_per_request": cpu_cores_per_request,
            "use_threadpool": use_threadpool,
            "batch_interval_sec": batch_interval_sec,
            "cleanup_interval_min": cleanup_interval_min,
            "response_timeout_min": response_timeout_min
        }

    def get_batch_config(self):
        """Get batch processing configuration (legacy method for backward compatibility)"""
        processing_config = self.get_processing_config()
        return {
            "interval_sec": processing_config["batch_interval_sec"],
            "cleanup_interval_min": processing_config["cleanup_interval_min"],
            "response_timeout_min": processing_config["response_timeout_min"]
        }

    def get_prompts(self):
        """Get sentiment analysis prompts"""
        return self.config.get("prompts", {})

    def get_daily_sentiment_config(self):
        """Get daily sentiment analysis configuration"""
        daily_config = self.config.get("daily_sentiment", {})

        # Add centralized storage paths to the daily config
        storage_config = self.get_storage_config()
        daily_config["storage"] = {
            "conversation_tracking_path": storage_config.get("conversation_tracking_path"),
            "backup_reports_path": storage_config.get("backup_reports_path")
        }

        # Merge PostgreSQL configuration from separate file
        postgresql_config = self.postgresql_config.get("postgresql", {})
        daily_config["postgresql"] = postgresql_config

        return daily_config

    def get_postgresql_config(self):
        """Get PostgreSQL configuration from separate config file"""
        return self.postgresql_config.get("postgresql", {})

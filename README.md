# Sentiment Analysis API System

A FastAPI-based system for sentiment analysis using the Qwen/Qwen2.5-0.5B-Instruct model from Huggingface.

## Features

- Webhook endpoint for receiving messages
- Sentiment analysis with batch processing for optimal performance
- **Periodic sentiment analysis and nightly reports** - Automatically processes conversations and generates consolidated reports
- Conversation timeout management (24-hour timeout)
- Ngrok integration for public access
- YAML-based configuration

## Project Structure

```
sentiment-analysis-api/
├── app/                  # Main application package
│   ├── api/              # API endpoints
│   ├── core/             # Core functionality
│   ├── main.py           # FastAPI application entry point
│   ├── models/           # Data models and schemas
│   ├── services/         # Services
│   └── utils/            # Utilities
├── config/               # Configuration files
│   └── config.yaml       # Main configuration
├── data/                 # Data storage
├── doc/                  # Documentation
├── logs/                 # Log files
├── scripts/              # Shell scripts
├── tests/                # Test files
├── .env.example          # Environment variables example
├── .gitignore            # Git ignore file
├── postgresql_config.yaml # PostgreSQL configuration
├── README.md             # This file
├── requirements.txt      # Python dependencies
└── run.py                # Script to run with Uvicorn
```

## System Architecture

1. **Webhook Message Reception**
   - Messages are received through the `/webhook` endpoint
   - Messages are stored in a temporary database
   - Conversations are managed with a 24-hour timeout

2. **Sentiment Analysis**
   - Sentiment analysis requests are received through the `/sentiment_widget/action` endpoint
   - Requests are added to a queue for batch processing
   - Batches are processed every second for optimal performance

3. **Result Delivery**
   - Results are stored in a thread-safe dictionary
   - Results can be retrieved through the `/sentiment_status/<request_id>` endpoint

## Configuration

The system uses multiple configuration files:

### Main Configuration (`config/config.yaml`)
- API credentials
- Server settings
- Database settings
- Model configuration
- Batch processing settings
- Sentiment analysis prompts
- Daily sentiment analysis settings

### PostgreSQL Configuration (`postgresql_config.yaml`)
- PostgreSQL database connection settings
- Upload scheduling configuration
- Retry and error handling settings
- Backup configuration

This separation allows for better security and easier management of database credentials.

## Installation

### Using Conda (Recommended)

1. Clone the repository
2. Run the setup script:
   ```bash
   # On Windows
   .\scripts\install_dependencies.ps1

   # On Linux/macOS
   bash scripts/install_dependencies.sh
   ```
3. Activate the Conda environment:
   ```bash
   conda activate sentiment-api
   ```
4. Configure the application by editing the `.env` file (created from `.env.example`)

### Manual Installation

1. Clone the repository
2. Create a Conda environment:
   ```bash
   conda env create -f environment.yml
   conda activate sentiment-api
   ```
3. Configure the system by editing `config/config.yaml` or using environment variables

## Running the System

### Using the Run Scripts

```bash
# On Linux/macOS
bash scripts/run_app.sh

# On Windows
.\scripts\run_app.ps1
```

If you need ngrok for local development (to expose your local server to the internet), add the `--ngrok` flag:

```bash
# On Linux/macOS
bash scripts/run_app.sh --ngrok

# On Windows
.\scripts\run_app.ps1 --ngrok
```

### Using Uvicorn Directly

Alternatively, you can run the application directly with Uvicorn:

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

When using the run scripts or running directly, the application will:
1. Load the configuration from `config/config.yaml` and environment variables
2. Start the Uvicorn server with FastAPI
3. Start the batch processor thread
4. Start the cleanup thread
5. If using ngrok, start a tunnel for public access

## Deploying to Cloud Providers

This application can be deployed to various cloud providers without Docker. Here are some general guidelines:

### General Deployment Steps

1. Clone the repository on your cloud server or upload the code
2. Install Miniconda or Anaconda on the server
3. Create the Conda environment using the provided `environment.yml` file
4. Configure the application using environment variables or the config file
5. Run the application using Uvicorn

### Example: Deploying to a VPS (DigitalOcean, AWS EC2, etc.)

```bash
# Install Miniconda
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh

# Clone the repository
git clone <your-repository-url>
cd <repository-directory>

# Create and activate the Conda environment
conda env create -f environment.yml
conda activate sentiment-api

# Configure the application
cp .env.example .env
# Edit the .env file with your settings

# Run the application with a process manager like Supervisor or systemd
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Using a Process Manager (Recommended for Production)

For production deployments, it's recommended to use a process manager like Supervisor or systemd to ensure the application stays running.

#### Example Supervisor Configuration

```ini
[program:sentiment-api]
command=/path/to/conda/envs/sentiment-api/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
directory=/path/to/application
user=username
autostart=true
autorestart=true
stdout_logfile=/path/to/logs/sentiment-api.log
stderr_logfile=/path/to/logs/sentiment-api-error.log
```

## API Endpoints

- `/webhook` - Receive messages from Crisp Chat
- `/sentiment_widget/action` - Request sentiment analysis with a short timeout (5 seconds)
- `/sentiment_status/<request_id>` - Check the status of a sentiment analysis request with configurable timeout
  - Query parameter: `timeout` (default: 3.0, range: 0.5-10.0 seconds)
- `/sentiment_wait/<request_id>` - Wait for sentiment analysis result with a longer timeout for GPU processing
  - Query parameter: `timeout` (default: 15.0, range: 1.0-30.0 seconds)
- `/sentiment_widget/callback` - Callback endpoint for Crisp Chat
- `/sentiment_widget/setting` - Settings endpoint for Crisp Chat

## Processing Modes

The system supports two processing modes that can be configured in `config.yaml` and can automatically adapt to the available hardware:

### 1. Batch Processing Mode (Default)

In batch processing mode, requests are queued and processed in batches. This is more efficient for GPU processing but may introduce some delay.

```yaml
processing:
  mode: "batch"
  batch_interval_sec: 1.0
  # Other batch settings...
```

### 2. Immediate Processing Mode

In immediate processing mode, each request is processed as soon as it arrives, using CPU multi-threading. This provides faster response times but may be less efficient for GPU processing.

```yaml
processing:
  mode: "immediate"
  cpu_threads: 4  # Number of worker threads (or null for auto-detection)
  use_threadpool: true  # Use a thread pool for processing
  # Other settings...
```

### Automatic Hardware Optimization

The system can automatically optimize the processing mode based on the available hardware:

```yaml
processing:
  # Initial mode (will be auto-adjusted if auto_adjust_mode is true)
  mode: "batch"

  # Enable automatic hardware optimization
  auto_adjust_mode: true

  # Auto-detect number of CPU threads
  cpu_threads: null  # null means use all available cores minus 1
```

With `auto_adjust_mode` enabled:

1. **On CPU**: The system will automatically switch to immediate processing mode and use all available CPU cores (minus one to keep the system responsive)

2. **On GPU**: The system will automatically switch to batch processing mode for more efficient GPU utilization

This ensures optimal performance regardless of the hardware environment.

### GPU Selection

For systems with multiple GPUs, you can specify which GPUs to use:

```yaml
model:
  name: "Qwen/Qwen2.5-0.5B-Instruct"
  device: "auto"  # Will use GPU if available
  torch_dtype: "bfloat16"
  allowed_gpus: [0, 2]  # Only use the first and third GPU
```

Options for `allowed_gpus`:

- `[0]`: Use only the first GPU
- `[0, 1, 2]`: Use the first three GPUs
- `[]`: Use all available GPUs (default if not specified)
- Not specified or `null`: Use all available GPUs

The system will automatically distribute the model across the specified GPUs if multiple are allowed.

## Auto-Save Feature

The system includes an automatic data saving feature that periodically saves conversation data to disk:

```yaml
processing:
  # Auto-save settings
  autosave_enabled: true  # Enable automatic saving of data
  autosave_interval_min: 5  # Auto-save every 5 minutes
```

This ensures that data is regularly persisted to disk, minimizing data loss in case of unexpected shutdowns. The system also performs a final save operation during graceful shutdown.

## Message Logging

The system provides detailed logging of the messages being processed for sentiment analysis:

### Standard Logging

By default, the system logs:
- Number of messages in each conversation
- First and last message previews
- Conversation statistics (message counts by role, average length, etc.)
- Prompt being used for analysis

### Detailed Message Logging

For debugging purposes, you can enable detailed message logging to see the full content of all messages:

```yaml
processing:
  # Logging settings
  detailed_message_logging: true  # Log full conversation messages
```

This will log the complete content of each message in the conversation, which can be helpful for debugging but may generate large log files.

## Timeout-Based Processing

The system implements a timeout-based approach for handling sentiment analysis requests:

1. **Short Timeout (5 seconds)** - The `/sentiment_widget/action` endpoint waits for up to 5 seconds for a result before returning a processing status. This is suitable for webhook callbacks that need quick responses.

2. **Medium Timeout (3 seconds)** - The `/sentiment_status/<request_id>` endpoint waits for up to 3 seconds (configurable) for a result. This is suitable for polling the status of a request.

3. **Long Timeout (15 seconds)** - The `/sentiment_wait/<request_id>` endpoint waits for up to 15 seconds (configurable) for a result. This is specifically designed for cases where you want to wait for GPU processing to complete.

When using immediate processing mode with CPU multi-threading, responses are typically available much faster, often within the short timeout period.

## Periodic Sentiment Analysis & Nightly Reports

The system includes an advanced periodic sentiment analysis feature that automatically processes conversations and generates consolidated nightly reports.

### Key Features

- **Automatic periodic processing**: Analyzes conversations at configurable intervals (30min, 1h, 3h, 6h, 12h, 24h)
- **Smart reprocessing**: Only reanalyzes conversations with new messages since last analysis
- **Nightly consolidated reports**: Generates comprehensive reports for conversations within specified time windows
- **Complete conversation data**: Includes full conversation content, user info, agent info, and all three sentiment analysis types
- **File-based output**: All results are saved to JSON files for direct access

### Configuration

Configure periodic processing in `config/config.yaml`:

```yaml
periodic_sentiment:
  enabled: true
  periodic_statistics: [30, 60, 180, 360, 720, 1440]  # Minutes
  nightly_report:
    start_nightly_time: "18:00"  # 6:00 PM
    end_nightly_time: "06:00"    # 6:00 AM
    timezone: "Asia/Ho_Chi_Minh"
  storage:
    periodic_results_path: "data/periodic_sentiment_results.json"
    nightly_reports_path: "data/nightly_reports/"
    conversation_tracking_path: "data/conversation_tracking.json"
```

### Output Files

1. **Periodic Results**: `data/periodic_sentiment_results.json` - Contains all periodic processing results
2. **Nightly Reports**: `data/nightly_reports/nightly_report_YYYY-MM-DD.json` - Daily consolidated reports
3. **Conversation Tracking**: `data/conversation_tracking.json` - Tracks conversation states and analysis history

### Usage

The system runs automatically once started. To access results:

```bash
# View latest nightly report
cat data/nightly_reports/nightly_report_$(date +%Y-%m-%d).json | jq '.'

# View sentiment distribution
cat data/nightly_reports/nightly_report_$(date +%Y-%m-%d).json | jq '.sentiment_distribution'

# View periodic processing results
cat data/periodic_sentiment_results.json | jq '.[-1]'  # Latest result
```

For detailed usage instructions, see [PERIODIC_SENTIMENT_GUIDE.md](doc/PERIODIC_SENTIMENT_GUIDE.md).

## CI/CD Pipeline

The system includes a comprehensive CI/CD pipeline with:

### Continuous Integration
- **Code Quality**: Automated formatting, linting, and type checking
- **Security Scanning**: Vulnerability detection in code and dependencies
- **Testing**: Unit tests, integration tests, and performance tests
- **Multi-environment**: Support for staging and production deployments

### Continuous Deployment
- **Staging**: Automatic deployment on main branch push
- **Production**: Blue-green deployment triggered by version tags
- **Monitoring**: Integrated Prometheus and Grafana monitoring
- **Rollback**: Automated rollback capabilities

### Quick Start CI/CD

1. **Setup Server**:
   ```bash
   # Setup staging server
   ./scripts/setup-server.sh staging

   # Setup production server
   ./scripts/setup-server.sh production
   ```

2. **Deploy to Staging**:
   ```bash
   git push origin main
   # Automatic deployment via GitHub Actions
   ```

3. **Deploy to Production**:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   # Blue-green deployment via GitHub Actions
   ```

4. **Monitor Deployment**:
   - Staging: http://staging.your-domain.com:8080
   - Production: https://your-domain.com
   - Grafana: https://your-domain.com:3000
   - Prometheus: https://your-domain.com:9090

### Documentation

- [CI/CD Guide](doc/CICD_GUIDE.md) - Comprehensive CI/CD setup and usage
- [Deployment Checklist](doc/DEPLOYMENT_CHECKLIST.md) - Pre and post-deployment verification
- [PostgreSQL Configuration](doc/POSTGRESQL_CONFIG_GUIDE.md) - Database configuration guide
- [Architecture Overview](doc/ARCHITECTURE.md) - System architecture documentation
- [Airflow Integration](doc/AIRFLOW_INTEGRATION_GUIDE.md) - Apache Airflow workflow management

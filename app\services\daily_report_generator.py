"""
Daily Report Generator Service
-----------------------------
Service for generating simplified daily sentiment reports
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.models.schemas import DailySentimentReport, DailySentimentRecord

logger = logging.getLogger(__name__)


class DailyReportGenerator:
    """
    Service for generating simplified daily sentiment analysis reports.
    This service:
    - Collects conversations processed throughout the day
    - Generates simplified reports with only essential data
    - Creates records for each agent-conversation pair
    """

    def __init__(self, config: Dict[str, Any], continuous_processor):
        """
        Initialize the daily report generator.

        Args:
            config: Daily sentiment configuration
            continuous_processor: ContinuousSentimentProcessor instance
        """
        self.config = config
        self.continuous_processor = continuous_processor

        # Threading
        self._lock = threading.Lock()
        self._generating = False

        logger.info("DailyReportGenerator initialized")

    def generate_daily_report(self, target_date: datetime = None) -> DailySentimentReport:
        """
        Generate a simplified daily sentiment report for the specified date.

        Args:
            target_date: Target date for the report (defaults to today)

        Returns:
            DailySentimentReport object
        """
        with self._lock:
            if self._generating:
                raise RuntimeError("Daily report generation is already running")

            self._generating = True

        try:
            start_time = time.time()

            if target_date is None:
                target_date = datetime.now()

            report_date = target_date.strftime('%Y-%m-%d')

            logger.info(f"Starting daily report generation for {report_date}")

            # Get conversations processed throughout the day
            conversations = self.continuous_processor.get_daily_conversations()

            logger.info(f"Found {len(conversations)} conversations for daily report")

            # Convert conversations to simplified records
            records = self._convert_conversations_to_records(conversations)

            # Calculate statistics
            total_records = len(records)
            total_conversations = len(conversations)
            total_agents = len(set(record.agent_name for record in records))

            # Create report
            processing_duration = time.time() - start_time
            report = DailySentimentReport(
                report_date=report_date,
                generation_timestamp=int(start_time),
                total_records=total_records,
                total_conversations=total_conversations,
                total_agents=total_agents,
                records=records,
                processing_duration_seconds=processing_duration
            )

            logger.info(f"Daily report generated: {total_conversations} conversations, "
                       f"{total_records} records, {total_agents} agents, {processing_duration:.2f}s duration")

            return report

        finally:
            with self._lock:
                self._generating = False

    def _convert_conversations_to_records(self, conversations) -> List[DailySentimentRecord]:
        """
        Convert conversations to simplified sentiment records.
        Creates one record per agent-conversation pair.

        Args:
            conversations: List of ConversationSentiment objects

        Returns:
            List of DailySentimentRecord objects
        """
        records = []

        for conversation in conversations:
            try:
                # Extract basic conversation info
                website_id = conversation.website_id
                session_id = conversation.session_id

                # Extract user info
                user_nickname = conversation.user.nickname if conversation.user else "Unknown"
                user_id = conversation.user.user_id if conversation.user else session_id

                # Extract conversation timing
                conversation_start = conversation.conversation_start
                conversation_end = conversation.conversation_end
                is_completed = conversation.is_completed

                # Extract message count and analysis timestamp
                message_count = conversation.message_count
                analysis_timestamp = conversation.analysis_timestamp

                # Extract sentiment analysis results
                sentiment = conversation.sentiment_analysis
                overall_emotion = sentiment.overall_emotion if sentiment else "unknown"
                sentiment_shift = sentiment.sentiment_shift if sentiment else "unknown"
                latest_sentiment = sentiment.latest_sentiment if sentiment else "unknown"

                # Create record for each agent
                if conversation.agents:
                    for agent in conversation.agents:
                        record = DailySentimentRecord(
                            website_id=website_id,
                            session_id=session_id,
                            user_nickname=user_nickname,
                            user_id=user_id,
                            agent_name=agent.name,
                            agent_role=agent.role,
                            conversation_start=conversation_start,
                            conversation_end=conversation_end,
                            is_completed=is_completed,
                            overall_emotion=overall_emotion,
                            sentiment_shift=sentiment_shift,
                            latest_sentiment=latest_sentiment,
                            message_count=message_count,
                            analysis_timestamp=analysis_timestamp
                        )
                        records.append(record)
                else:
                    # If no agents found, create record with generic agent
                    record = DailySentimentRecord(
                        website_id=website_id,
                        session_id=session_id,
                        user_nickname=user_nickname,
                        user_id=user_id,
                        agent_name="Support Agent",
                        agent_role="operator",
                        conversation_start=conversation_start,
                        conversation_end=conversation_end,
                        is_completed=is_completed,
                        overall_emotion=overall_emotion,
                        sentiment_shift=sentiment_shift,
                        latest_sentiment=latest_sentiment,
                        message_count=message_count,
                        analysis_timestamp=analysis_timestamp
                    )
                    records.append(record)

            except Exception as e:
                logger.error(f"Error converting conversation {conversation.session_id} to record: {e}")
                continue

        logger.info(f"Converted {len(conversations)} conversations to {len(records)} records")
        return records

    def get_generation_status(self) -> Dict[str, Any]:
        """
        Get current generation status.

        Returns:
            Dictionary with generation status information
        """
        with self._lock:
            return {
                "is_generating": self._generating
            }

    def convert_records_to_dict_list(self, records: List[DailySentimentRecord]) -> List[Dict[str, Any]]:
        """
        Convert DailySentimentRecord objects to dictionary list for database upload.

        Args:
            records: List of DailySentimentRecord objects

        Returns:
            List of dictionaries suitable for database upload
        """
        dict_list = []

        for record in records:
            dict_record = {
                'website_id': record.website_id,
                'session_id': record.session_id,
                'user_nickname': record.user_nickname,
                'user_id': record.user_id,
                'agent_name': record.agent_name,
                'agent_role': record.agent_role,
                'conversation_start': record.conversation_start,
                'conversation_end': record.conversation_end,
                'is_completed': record.is_completed,
                'overall_emotion': record.overall_emotion,
                'sentiment_shift': record.sentiment_shift,
                'latest_sentiment': record.latest_sentiment,
                'message_count': record.message_count,
                'analysis_timestamp': record.analysis_timestamp
            }
            dict_list.append(dict_record)

        return dict_list

    def save_report_to_file(self, report: DailySentimentReport, file_path: str) -> bool:
        """
        Save daily report to JSON file for backup.

        Args:
            report: DailySentimentReport object
            file_path: Path to save the report

        Returns:
            True if successful, False otherwise
        """
        try:
            import json
            import os

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Convert report to dictionary
            report_dict = {
                'report_date': report.report_date,
                'generation_timestamp': report.generation_timestamp,
                'total_records': report.total_records,
                'total_conversations': report.total_conversations,
                'total_agents': report.total_agents,
                'processing_duration_seconds': report.processing_duration_seconds,
                'records': self.convert_records_to_dict_list(report.records)
            }

            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_dict, f, indent=2, ensure_ascii=False)

            logger.info(f"Daily report saved to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving daily report to {file_path}: {e}")
            return False

    def load_report_from_file(self, file_path: str) -> Optional[DailySentimentReport]:
        """
        Load daily report from JSON file.

        Args:
            file_path: Path to the report file

        Returns:
            DailySentimentReport object or None if loading fails
        """
        try:
            import json

            with open(file_path, 'r', encoding='utf-8') as f:
                report_dict = json.load(f)

            # Convert records back to DailySentimentRecord objects
            records = []
            for record_dict in report_dict.get('records', []):
                record = DailySentimentRecord(**record_dict)
                records.append(record)

            # Create DailySentimentReport object
            report = DailySentimentReport(
                report_date=report_dict['report_date'],
                generation_timestamp=report_dict['generation_timestamp'],
                total_records=report_dict['total_records'],
                total_conversations=report_dict['total_conversations'],
                total_agents=report_dict['total_agents'],
                records=records,
                processing_duration_seconds=report_dict['processing_duration_seconds']
            )

            logger.info(f"Daily report loaded from {file_path}")
            return report

        except Exception as e:
            logger.error(f"Error loading daily report from {file_path}: {e}")
            return None

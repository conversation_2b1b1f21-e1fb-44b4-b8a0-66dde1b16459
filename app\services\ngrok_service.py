from pyngrok import ngrok, conf, process
import logging
import time
import os
import subprocess
import signal
import psutil

logger = logging.getLogger(__name__)

class NgrokService:
    """Service for managing ngrok tunnels"""

    def __init__(self, auth_token=None):
        """
        Initialize the ngrok service

        Args:
            auth_token: Ngrok authentication token
        """
        self.auth_token = auth_token
        self.public_url = None
        self.ngrok_process = None

    def kill_existing_ngrok_processes(self):
        """
        Kill any existing ngrok processes to avoid the 'limited to 1 simultaneous ngrok agent session' error

        Returns:
            bool: True if processes were killed, False otherwise
        """
        killed = False
        try:
            # Try to find and kill ngrok processes
            for proc in psutil.process_iter(['pid', 'name']):
                if 'ngrok' in proc.info['name'].lower():
                    logger.info(f"Found existing ngrok process (PID: {proc.info['pid']}). Terminating...")
                    try:
                        os.kill(proc.info['pid'], signal.SIGTERM)
                        killed = True
                    except Exception as e:
                        logger.error(f"Failed to kill ngrok process: {e}")

            # Also try to disconnect any tunnels from pyngrok
            try:
                tunnels = ngrok.get_tunnels()
                for tunnel in tunnels:
                    logger.info(f"Disconnecting existing tunnel: {tunnel.public_url}")
                    ngrok.disconnect(tunnel.public_url)
                    killed = True
            except Exception as e:
                logger.warning(f"Error checking existing tunnels: {e}")

            # If we killed any processes, wait a moment for them to fully terminate
            if killed:
                logger.info("Waiting for ngrok processes to terminate...")
                time.sleep(2)

            return killed
        except Exception as e:
            logger.error(f"Error killing existing ngrok processes: {e}")
            return False

    def start_tunnel(self, app=None, port=8000, force_restart=True):
        """
        Start an ngrok tunnel

        Args:
            app: Application instance (optional, can be None for FastAPI)
            port: Port to tunnel to
            force_restart: Whether to force restart ngrok if there are existing sessions

        Returns:
            public_url: Public URL of the tunnel
        """
        try:
            # Kill existing ngrok processes if force_restart is True
            if force_restart:
                self.kill_existing_ngrok_processes()

            # Set the auth token if provided
            if self.auth_token:
                logger.info("Setting ngrok authentication token")
                ngrok.set_auth_token(self.auth_token)

            # Open a ngrok tunnel to the HTTP server
            logger.info(f"Starting ngrok tunnel to port {port}...")
            tunnel = ngrok.connect(port)
            self.public_url = tunnel.public_url
            logger.info(f"ngrok tunnel established: '{self.public_url}' -> 'http://127.0.0.1:{port}'")

            # Store the ngrok process
            try:
                self.ngrok_process = process.get_process()
            except Exception as e:
                logger.warning(f"Could not get ngrok process: {e}")

            # Update any base URLs to use the ngrok tunnel (for Flask apps)
            if app and hasattr(app, 'config'):
                app.config["BASE_URL"] = self.public_url

            return self.public_url
        except Exception as e:
            if "limited to 1 simultaneous ngrok agent session" in str(e):
                logger.warning("Detected 'limited to 1 simultaneous ngrok agent session' error. Trying to kill existing sessions...")
                if self.kill_existing_ngrok_processes():
                    logger.info("Retrying tunnel creation after killing existing sessions...")
                    # Try again, but don't force restart to avoid infinite recursion
                    return self.start_tunnel(app, port, force_restart=False)

            logger.error(f"Error starting ngrok tunnel: {e}")
            raise

    def stop_tunnel(self):
        """Stop the ngrok tunnel"""
        try:
            # Disconnect the tunnel if we have a URL
            if self.public_url:
                logger.info(f"Stopping ngrok tunnel: {self.public_url}")
                ngrok.disconnect(self.public_url)
                self.public_url = None

            # Try to kill the ngrok process if we have a reference to it
            if self.ngrok_process:
                try:
                    logger.info("Stopping ngrok process...")
                    process.terminate_process(self.ngrok_process)
                    self.ngrok_process = None
                except Exception as e:
                    logger.error(f"Error stopping ngrok process: {e}")
        except Exception as e:
            logger.error(f"Error stopping ngrok tunnel: {e}")

    def get_public_url(self):
        """Get the public URL of the tunnel"""
        return self.public_url

    def get_all_tunnels(self):
        """Get all active ngrok tunnels"""
        try:
            return ngrok.get_tunnels()
        except Exception as e:
            logger.error(f"Error getting tunnels: {e}")
            return []

    @staticmethod
    def kill_all_ngrok_processes():
        """
        Static method to kill all ngrok processes

        Returns:
            bool: True if processes were killed, False otherwise
        """
        killed = False
        try:
            # Try to find and kill ngrok processes using psutil
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'ngrok' in proc.info['name'].lower():
                        logger.info(f"Found ngrok process (PID: {proc.info['pid']}). Terminating...")
                        os.kill(proc.info['pid'], signal.SIGTERM)
                        killed = True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            # Also try to use pyngrok's process termination
            try:
                ngrok_process = process.get_process()
                if ngrok_process:
                    logger.info(f"Terminating ngrok process via pyngrok...")
                    process.terminate_process(ngrok_process)
                    killed = True
            except Exception as e:
                logger.warning(f"Error terminating ngrok process via pyngrok: {e}")

            # If we killed any processes, wait a moment for them to fully terminate
            if killed:
                logger.info("Waiting for ngrok processes to terminate...")
                time.sleep(2)

            return killed
        except Exception as e:
            logger.error(f"Error killing ngrok processes: {e}")
            return False

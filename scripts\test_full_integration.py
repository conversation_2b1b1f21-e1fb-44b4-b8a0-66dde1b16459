#!/usr/bin/env python3
"""
Test script for full integration: Webhook -> Database -> Agent Extraction -> PostgreSQL
"""

import sys
import os
import json
import time
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.api.webhooks import _extract_agent_information
from app.core.database import MessageDatabase
from app.services.daily_report_generator import DailyReportGenerator
from app.services.postgresql_service import PostgreSQLService
from app.models.schemas import DailySentimentRecord


def create_test_webhook_data():
    """Create test webhook data with various agent scenarios"""
    base_time = int(time.time())
    
    test_data = [
        # Human agent message
        {
            "event": "message:received",
            "website_id": "test-website-001",
            "data": {
                "session_id": "session-001",
                "type": "text",
                "content": "Hello! How can I help you today?",
                "from": "operator",
                "origin": "chat",
                "timestamp": base_time - 3600,
                "fingerprint": 123456001,
                "user": {
                    "nickname": "<PERSON>",
                    "user_id": "agent-hannah-001"
                }
            }
        },
        # Bot message
        {
            "event": "message:received",
            "website_id": "test-website-001",
            "data": {
                "session_id": "session-002",
                "type": "text",
                "content": "Hi there. Thank you for reaching UpPromote support.",
                "from": "operator",
                "origin": "urn:crisp.im:bot:0",
                "timestamp": base_time - 3000,
                "fingerprint": 123456002,
                "user": {
                    "nickname": "Customer",
                    "user_id": "customer-001"
                }
            }
        },
        # Agent with metadata
        {
            "event": "message:received",
            "website_id": "test-website-001",
            "data": {
                "session_id": "session-003",
                "type": "text",
                "content": "Let me check that for you.",
                "from": "operator",
                "origin": "chat",
                "timestamp": base_time - 2400,
                "fingerprint": 123456003,
                "metadata": {
                    "agent_name": "Tommy Nguyen",
                    "agent_id": "agent-tommy-001"
                },
                "user": {
                    "nickname": "Customer",
                    "user_id": "customer-002"
                }
            }
        },
        # User message
        {
            "event": "message:received",
            "website_id": "test-website-001",
            "data": {
                "session_id": "session-001",
                "type": "text",
                "content": "I need help with my order",
                "from": "user",
                "origin": "chat",
                "timestamp": base_time - 3500,
                "fingerprint": 123456004,
                "user": {
                    "nickname": "John Customer",
                    "user_id": "customer-003"
                }
            }
        }
    ]
    
    return test_data


def test_webhook_processing():
    """Test webhook processing with agent extraction"""
    print("Testing webhook processing with agent extraction...")
    
    test_data = create_test_webhook_data()
    enhanced_data = []
    
    for webhook_data in test_data:
        # Process webhook data
        enhanced = _extract_agent_information(webhook_data)
        enhanced_data.append(enhanced)
        
        # Verify agent info was added
        assert "agent_info" in enhanced["data"]
        agent_info = enhanced["data"]["agent_info"]
        
        print(f"✓ Processed message from {agent_info['role']}: {agent_info.get('name', 'N/A')}")
    
    print(f"✓ Processed {len(enhanced_data)} webhook messages")
    return enhanced_data


def test_database_storage(enhanced_data):
    """Test database storage with agent information"""
    print("\nTesting database storage...")
    
    # Create temporary database
    db = MessageDatabase(save_path="data/test_messages.json", autosave_interval_sec=3600)
    
    # Store enhanced messages
    for enhanced_message in enhanced_data:
        db.add_message(enhanced_message)
    
    # Verify storage
    for enhanced_message in enhanced_data:
        website_id = enhanced_message["website_id"]
        session_id = enhanced_message["data"]["session_id"]
        
        # Get conversation data
        conversation_data = db.get_conversation_data(website_id, session_id)
        
        if conversation_data:
            messages = conversation_data.get("data", [])
            
            # Check if agent info is preserved
            for msg in messages:
                if msg.get("role") == "operator":
                    assert "agent_info" in msg, f"Missing agent_info in message: {msg}"
                    agent_info = msg["agent_info"]
                    print(f"✓ Stored agent info: {agent_info.get('name', 'N/A')} ({agent_info.get('role')})")
    
    print("✓ Database storage test passed")
    return db


def test_daily_report_generation(db):
    """Test daily report generation with agent information"""
    print("\nTesting daily report generation...")
    
    # Create mock continuous processor
    class MockContinuousProcessor:
        def get_daily_conversations(self):
            # Mock conversation data with agent information
            from app.models.schemas import ConversationSentiment, UserInfo, AgentInfo, MessageData, SentimentAnalysisResult
            
            conversations = []
            base_time = int(time.time())
            
            # Create test conversation
            conversation = ConversationSentiment(
                session_id="session-001",
                website_id="test-website-001",
                user=UserInfo(nickname="John Customer", user_id="customer-003"),
                agents=[
                    AgentInfo(name="Hannah Mai", role="operator"),
                    AgentInfo(name="Bot Assistant", role="bot")
                ],
                conversation_start=base_time - 3600,
                conversation_end=base_time - 3000,
                messages=[
                    MessageData(
                        type="text",
                        origin="chat",
                        text="I need help with my order",
                        timestamp=base_time - 3500,
                        fingerprint=123456004,
                        role="user"
                    ),
                    MessageData(
                        type="text",
                        origin="chat",
                        text="Hello! How can I help you today?",
                        timestamp=base_time - 3400,
                        fingerprint=123456001,
                        role="operator"
                    )
                ],
                sentiment_analysis=SentimentAnalysisResult(
                    overall_emotion="neutral",
                    sentiment_shift="stable",
                    latest_sentiment="neutral"
                ),
                analysis_timestamp=base_time,
                message_count=2,
                is_completed=True
            )
            
            conversations.append(conversation)
            return conversations
    
    # Create daily report generator
    config = {}
    mock_processor = MockContinuousProcessor()
    generator = DailyReportGenerator(config, mock_processor)
    
    # Generate daily report
    report = generator.generate_daily_report()
    
    # Verify report contains agent information
    assert report.total_records > 0, "No records in daily report"
    
    for record in report.records:
        print(f"✓ Daily record: {record.agent_name} ({record.agent_role}) - {record.overall_emotion}")
        
        # Verify all required fields are present
        required_fields = [
            'website_id', 'session_id', 'user_nickname', 'user_id',
            'agent_name', 'agent_role', 'conversation_start', 'conversation_end',
            'is_completed', 'overall_emotion', 'sentiment_shift', 'latest_sentiment',
            'message_count', 'analysis_timestamp'
        ]
        
        for field in required_fields:
            assert hasattr(record, field), f"Missing field: {field}"
    
    print(f"✓ Generated daily report with {report.total_records} records")
    return report


def test_postgresql_data_structure(report):
    """Test PostgreSQL data structure"""
    print("\nTesting PostgreSQL data structure...")
    
    # Create mock PostgreSQL service (disabled)
    config = {
        'enabled': False,
        'host': 'localhost',
        'port': 5432,
        'database': 'test_sentiment',
        'username': 'postgres',
        'password': 'password',
        'table_name': 'daily_sentiment_reports'
    }
    
    service = PostgreSQLService(config)
    
    # Convert report to dict format for PostgreSQL
    generator = DailyReportGenerator({}, None)
    dict_records = generator.convert_records_to_dict_list(report.records)
    
    # Verify data structure
    for record in dict_records:
        print(f"✓ PostgreSQL record structure: {list(record.keys())}")
        
        # Verify all required fields are present
        required_fields = [
            'website_id', 'session_id', 'user_nickname', 'user_id',
            'agent_name', 'agent_role', 'conversation_start', 'conversation_end',
            'is_completed', 'overall_emotion', 'sentiment_shift', 'latest_sentiment',
            'message_count', 'analysis_timestamp'
        ]
        
        for field in required_fields:
            assert field in record, f"Missing field in PostgreSQL record: {field}"
        
        # Verify data types
        assert isinstance(record['conversation_start'], int), "conversation_start should be int"
        assert isinstance(record['message_count'], int), "message_count should be int"
        assert isinstance(record['analysis_timestamp'], int), "analysis_timestamp should be int"
        assert isinstance(record['is_completed'], bool), "is_completed should be bool"
        
        break  # Just check first record
    
    print(f"✓ PostgreSQL data structure validated for {len(dict_records)} records")
    return dict_records


def main():
    """Run full integration test"""
    print("=" * 60)
    print("Full Integration Test: Webhook -> Database -> PostgreSQL")
    print("=" * 60)
    
    try:
        # Step 1: Test webhook processing
        enhanced_data = test_webhook_processing()
        
        # Step 2: Test database storage
        db = test_database_storage(enhanced_data)
        
        # Step 3: Test daily report generation
        report = test_daily_report_generation(db)
        
        # Step 4: Test PostgreSQL data structure
        dict_records = test_postgresql_data_structure(report)
        
        print("\n" + "=" * 60)
        print("✓ Full integration test completed successfully!")
        print("=" * 60)
        
        # Summary
        print(f"\nSummary:")
        print(f"- Processed {len(enhanced_data)} webhook messages")
        print(f"- Generated {report.total_records} daily records")
        print(f"- Validated {len(dict_records)} PostgreSQL records")
        print(f"- All agent information properly extracted and stored")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

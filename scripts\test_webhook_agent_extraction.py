#!/usr/bin/env python3
"""
Test script for webhook agent extraction functionality
"""

import sys
import os
import json
import time

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.api.webhooks import _extract_agent_information, _get_agent_info_from_webhook


def test_agent_extraction_basic():
    """Test basic agent extraction from webhook data"""
    print("Testing basic agent extraction...")
    
    # Test data with operator message
    webhook_data = {
        "event": "message:received",
        "website_id": "test-website",
        "data": {
            "session_id": "test-session",
            "type": "text",
            "content": "Hello, how can I help you?",
            "from": "operator",
            "origin": "chat",
            "timestamp": int(time.time()),
            "fingerprint": 123456789,
            "user": {
                "nickname": "John Support",
                "user_id": "agent-123"
            }
        }
    }
    
    # Extract agent information
    enhanced_data = _extract_agent_information(webhook_data)
    agent_info = enhanced_data["data"]["agent_info"]
    
    print(f"✓ Extracted agent info: {agent_info}")
    
    # Verify agent information
    assert agent_info["role"] == "operator"
    assert agent_info["name"] == "John Support"
    assert agent_info["user_id"] == "agent-123"
    
    print("✓ Basic agent extraction test passed")
    return True


def test_agent_extraction_bot():
    """Test agent extraction for bot messages"""
    print("\nTesting bot agent extraction...")
    
    # Test data with bot message
    webhook_data = {
        "event": "message:received",
        "website_id": "test-website",
        "data": {
            "session_id": "test-session",
            "type": "text",
            "content": "Hi there. Thank you for reaching UpPromote support.",
            "from": "operator",
            "origin": "urn:crisp.im:bot:0",
            "timestamp": int(time.time()),
            "fingerprint": 123456789,
            "user": {
                "nickname": "Customer",
                "user_id": "customer-123"
            }
        }
    }
    
    # Extract agent information
    enhanced_data = _extract_agent_information(webhook_data)
    agent_info = enhanced_data["data"]["agent_info"]
    
    print(f"✓ Extracted bot agent info: {agent_info}")
    
    # Verify bot information
    assert agent_info["role"] == "bot"
    assert agent_info["name"] == "Bot Assistant"
    assert "bot" in agent_info["origin"]
    
    print("✓ Bot agent extraction test passed")
    return True


def test_agent_extraction_user():
    """Test agent extraction for user messages"""
    print("\nTesting user message extraction...")
    
    # Test data with user message
    webhook_data = {
        "event": "message:received",
        "website_id": "test-website",
        "data": {
            "session_id": "test-session",
            "type": "text",
            "content": "I need help with my order",
            "from": "user",
            "origin": "chat",
            "timestamp": int(time.time()),
            "fingerprint": 123456789,
            "user": {
                "nickname": "Customer",
                "user_id": "customer-123"
            }
        }
    }
    
    # Extract agent information
    enhanced_data = _extract_agent_information(webhook_data)
    agent_info = enhanced_data["data"]["agent_info"]
    
    print(f"✓ Extracted user agent info: {agent_info}")
    
    # Verify user information
    assert agent_info["role"] == "user"
    assert agent_info["name"] is None  # No agent name for user messages
    
    print("✓ User message extraction test passed")
    return True


def test_agent_extraction_with_metadata():
    """Test agent extraction with metadata"""
    print("\nTesting agent extraction with metadata...")
    
    # Test data with metadata
    webhook_data = {
        "event": "message:received",
        "website_id": "test-website",
        "data": {
            "session_id": "test-session",
            "type": "text",
            "content": "Let me check that for you",
            "from": "operator",
            "origin": "chat",
            "timestamp": int(time.time()),
            "fingerprint": 123456789,
            "metadata": {
                "agent_name": "Sarah Johnson",
                "agent_id": "agent-sarah-001"
            },
            "user": {
                "nickname": "Customer",
                "user_id": "customer-123"
            }
        }
    }
    
    # Extract agent information
    enhanced_data = _extract_agent_information(webhook_data)
    agent_info = enhanced_data["data"]["agent_info"]
    
    print(f"✓ Extracted metadata agent info: {agent_info}")
    
    # Verify metadata information
    assert agent_info["role"] == "operator"
    assert agent_info["name"] == "Sarah Johnson"
    assert agent_info["user_id"] == "agent-sarah-001"
    
    print("✓ Metadata agent extraction test passed")
    return True


def test_agent_extraction_fallback():
    """Test agent extraction fallback scenarios"""
    print("\nTesting agent extraction fallback...")
    
    # Test data with minimal information
    webhook_data = {
        "event": "message:received",
        "website_id": "test-website",
        "data": {
            "session_id": "test-session",
            "type": "text",
            "content": "Thank you for contacting us",
            "from": "operator",
            "origin": "chat",
            "timestamp": int(time.time()),
            "fingerprint": 123456789
        }
    }
    
    # Extract agent information
    enhanced_data = _extract_agent_information(webhook_data)
    agent_info = enhanced_data["data"]["agent_info"]
    
    print(f"✓ Extracted fallback agent info: {agent_info}")
    
    # Verify fallback information
    assert agent_info["role"] == "operator"
    assert agent_info["name"] == "Support Agent"  # Default name
    assert agent_info["origin"] == "chat"
    
    print("✓ Fallback agent extraction test passed")
    return True


def test_database_integration():
    """Test integration with database storage"""
    print("\nTesting database integration...")
    
    # Create enhanced webhook data
    webhook_data = {
        "event": "message:received",
        "website_id": "test-website",
        "data": {
            "session_id": "test-session",
            "type": "text",
            "content": "How can I assist you today?",
            "from": "operator",
            "origin": "chat",
            "timestamp": int(time.time()),
            "fingerprint": 123456789,
            "user": {
                "nickname": "Hannah Mai",
                "user_id": "agent-hannah-001"
            }
        }
    }
    
    # Extract agent information
    enhanced_data = _extract_agent_information(webhook_data)
    
    # Verify the enhanced data structure
    assert "agent_info" in enhanced_data["data"]
    agent_info = enhanced_data["data"]["agent_info"]
    
    # Check that agent info is properly structured for database storage
    expected_fields = ["name", "role", "user_id", "origin"]
    for field in expected_fields:
        assert field in agent_info, f"Missing field: {field}"
    
    print(f"✓ Enhanced data structure: {json.dumps(enhanced_data, indent=2)}")
    print("✓ Database integration test passed")
    return True


def main():
    """Run all tests"""
    print("=" * 60)
    print("Webhook Agent Extraction Tests")
    print("=" * 60)
    
    tests = [
        test_agent_extraction_basic,
        test_agent_extraction_bot,
        test_agent_extraction_user,
        test_agent_extraction_with_metadata,
        test_agent_extraction_fallback,
        test_database_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("✓ All tests passed successfully!")
        return True
    else:
        print(f"✗ {failed} test(s) failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

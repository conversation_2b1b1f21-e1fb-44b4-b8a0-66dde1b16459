# API Credentials
credentials:
  CRISP_IDENTIFIER: a22a-4f2d
  CRISP_KEY: 1be659806e1
  CRISP_WEBHOOK_SECRET: 8346c5e40564635aba5c2
  NGROK_AUTH_TOKEN: 2tWRq7dvR4thqqawhNn
  HUGGINGFACE_TOKEN: "hf_VgcmabOfuQ"  # Replace with your actual Hugging Face token

# Security Configuration
security:
  # Website ID whitelist configuration
  website_id_whitelist:
    enabled: true  # Set to false to disable website ID validation
    whitelist_file: "config/allowed_websites.json"  # Path to the whitelist file
    reload_after_save: true  # Reload the whitelist after saving messages
    unauthorized_file: "logs/unauthorized_websites.json"  # Path to store unauthorized website IDs

# Server Configuration
server:
  host: 0.0.0.0
  port: 5000
  debug: false
  threaded: true
  use_ngrok: true


# Database Configuration
database:
  conversation_timeout_hours: 24  # 24 hours
  cleanup_interval_hours: 48  # Clean up old conversations every 48 hours

# Model Configuration
model:
  name: "Qwen/Qwen2.5-3B-Instruct"
  max_new_tokens: 100
  device: "auto"  # "auto", "cuda", or "cpu"
  torch_dtype: "bfloat16"  # "float16", "bfloat16", or "float32" - bfloat16 is faster than float32
  # List of allowed GPU indices (e.g. [0, 2] to use only the first and third GPU)
  # Leave empty to use all available GPUs, or set to null when using CPU
  allowed_gpus: [0,1,2,3]  # Only use the first GPU

# Processing Configuration
processing:
  # Processing mode: "batch" or "immediate"
  # - batch: Process requests in batches (better for GPU)
  # - immediate: Process each request immediately (better for CPU multi-threading)
  mode: "immediate"

  # Auto-adjust processing mode based on hardware
  # If true, will automatically switch to immediate mode on CPU and batch mode on GPU
  auto_adjust_mode: false

  # CPU multi-threading settings (used when mode is "immediate")
  # Set to 1 to process each request only once
  cpu_threads: 16  # Number of worker threads for CPU processing
  cpu_cores_per_request: 8  # 0 means use all available CPU cores for each request
  use_threadpool: true  # Use a thread pool for CPU processing

  # Resource monitoring settings
  resource_monitor_interval_sec: 30  # How often to log resource usage (in seconds)

  # Auto-save settings (for both database and processing)
  autosave_enabled: true  # Enable automatic saving of data
  autosave_interval_min: 5  # Auto-save every 5 minutes

  # Logging settings
  detailed_message_logging: false  # Set to true to log full conversation messages (verbose)
  minimal_logging: true  # Set to true to minimize logging output
  disable_timer_logging: true  # Set to true to disable timer checkpoint logging

  # Request queue management
  max_queue_size: 100  # Maximum number of requests in the queue (0 = unlimited)
  rate_limit_strategy: "wait"  # Strategy when queue is full: "reject" or "wait"
  queue_wait_timeout_sec: 5  # Maximum time to wait for queue space when rate_limit_strategy is "wait"
  queue_put_timeout_sec: 2  # Timeout for adding a request to the queue

  # Batch processing settings (used when mode is "batch")
  batch_interval_sec: 1.0  # Process batches every 1 second
  max_batch_size: 10  # Maximum number of requests to process in a single batch
  cleanup_interval_min: 360  # Clean up old responses every 360 minutes
  response_timeout_min: 1440  # Remove responses older than 1440 minutes

  # Sentiment analysis processing settings
  min_messages_for_analysis: 2      # Minimum messages required in conversation for sentiment analysis
  max_conversation_age_hours: 168   # Maximum age of conversations to include in processing (7 days)
  sentiment_batch_size: 50          # Batch size for processing conversations
  conversation_timeout_sec: 30      # Timeout for individual conversation processing (seconds)

# Daily Sentiment Analysis & MongoDB Upload Configuration
daily_sentiment:
  # Enable daily sentiment analysis processing
  enabled: true

  # Continuous processing intervals (in minutes) - process throughout the day
  # Data is processed continuously and uploaded once daily at upload_time
  processing_intervals:
    - 30    # Every 30 minutes - continuous processing
    - 60    # Every 1 hour
    - 180   # Every 3 hours
    - 360   # Every 6 hours

  # Daily report generation time window (for data collection)
  daily_report:
    start_time: "00:00"  # Start of day - collect data from midnight
    end_time: "23:59"    # End of day - collect data until end of day
    timezone: "Asia/Ho_Chi_Minh" # Timezone for processing

  # PostgreSQL configuration is now stored in postgresql_config.yaml in the root directory



# Sentiment Analysis Prompts
prompts:
  sentiment:
    overall_emotion: |
      User's overall emotion analysis task (where the role is 'user', not 'operator'):

      Carefully analyze the text below and determine the single dominant emotion that is most consistently present throughout the entire messages of user.
      Consider the overall tone, word choice, context, and any possible use of sarcasm, irony, slang, or language that may be dismissive, mocking, or insulting.
      Pay close attention to expressions—regardless of their literal meaning—that could indicate an underlying emotional state due to their usage, such as sarcasm, irony, or cultural context.
      Do not rely solely on the direct meaning of words; instead, interpret the underlying emotional intent and how the language is used in context.

      Select one of the following labels that best describes the prevailing emotional state:
        "anger": persistent feelings of intense displeasure, hostility, or rage (including sarcastic, mocking, or dismissive expressions of anger)
        "frustration": ongoing sense of being upset, annoyed, or blocked from achieving goals
        "anxiety": continuous unease, nervousness, or worry about uncertain outcomes
        "neutral": absence of strong emotion; calm, objective, or factual tone
        "satisfaction": prevailing sense of contentment, fulfillment, or happiness

      Example:
      If the text contains words or phrases that, while literally positive or neutral, are used sarcastically, mockingly, or as insults, classify the emotion as "anger" or "frustration" as appropriate, not "satisfaction" or "neutral".

      Respond **only** in this exact JSON format:
      ```json
      {
        "overall_emotion": "satisfaction"
      }
      ```
    sentiment_shift: |
      User's sentiment shift analysis task (where the role is 'user', not 'operator'):

      Examine the emotional progression throughout the entire messages of user. Assess whether there is a clear and meaningful shift in sentiment from the beginning to the end.

      A "shift" means a noticeable and sustained change in emotional tone, not just a brief fluctuation.

      **Important:**
      - If the entire text expresses only a neutral tone from beginning to end, select "unchanged".
      - Only select a shift label if there is a clear and sustained change in emotional tone.

      Respond **only** in this exact JSON format:
      ```json
      {
        "sentiment_shift": "anger > neutral > satisfaction"
      }
      ```
    latest_sentiment: |
      User's latest emotion analysis task (where the role is 'user', not 'operator'):

      Focus specifically on the final segment or most recent statements of the message of user.
      Identify the sentiment most clearly expressed at the end, taking into account the context, tone, and possible use of sarcasm, irony, slang, or language that may be dismissive, mocking, or insulting.
      Pay particular attention to any words or phrases—regardless of their literal meaning—that could indicate negative emotions due to their usage, such as sarcasm, irony, or cultural context.
      Do not rely solely on the direct meaning of words; instead, interpret the underlying emotional intent.

      Choose one of the following labels:
        "anger": persistent feelings of intense displeasure, hostility, or rage (including sarcastic, mocking, or dismissive expressions of anger)
        "frustration": ongoing sense of being upset, annoyed, or blocked from achieving goals
        "anxiety": continuous unease, nervousness, or worry about uncertain outcomes
        "neutral": absence of strong emotion; calm, objective, or factual tone
        "satisfaction": prevailing sense of contentment, fulfillment, or happiness

      Example:
        If the final words or phrases are used sarcastically, mockingly, or as an insult (even if they are typically positive or neutral), classify the sentiment as "anger" or "frustration" as appropriate, rather than "satisfaction" or "neutral".

      Respond **only** in this exact JSON format:
      ```json
      {
        "latest_sentiment": "neutral"
      }
      ```

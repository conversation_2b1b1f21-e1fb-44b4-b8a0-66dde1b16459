"""
Sentiment Analysis DAG for Apache Airflow
Manages daily sentiment analysis workflows and monitoring
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.http_operator import SimpleHttpOperator
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash_operator import <PERSON><PERSON><PERSON>perator
from airflow.sensors.http_sensor import <PERSON>ttpSensor
from airflow.hooks.http_hook import HttpHook
from airflow.models import Variable
import json
import logging

# Configuration
SENTIMENT_API_BASE_URL = Variable.get("SENTIMENT_API_BASE_URL", "http://your-sentiment-server:8000")
AIRFLOW_API_TOKEN = Variable.get("AIRFLOW_API_TOKEN", "airflow-secret-token")

# Default arguments
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
    'execution_timeout': timedelta(hours=2)
}

# Create DAG
dag = DAG(
    'sentiment_analysis_daily',
    default_args=default_args,
    description='Daily sentiment analysis processing and reporting',
    schedule_interval='0 2 * * *',  # Run daily at 2 AM
    catchup=False,
    max_active_runs=1,
    tags=['sentiment-analysis', 'daily', 'ml']
)

def check_system_health(**context):
    """Check if sentiment analysis system is healthy"""
    http_hook = HttpHook(method='GET', http_conn_id='sentiment_api')
    
    response = http_hook.run(
        endpoint='/airflow/health',
        headers={
            'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
            'Content-Type': 'application/json'
        }
    )
    
    health_data = json.loads(response.content)
    
    if health_data['status'] != 'healthy':
        raise Exception(f"System is not healthy: {health_data}")
    
    logging.info(f"System health check passed: {health_data}")
    return health_data

def create_daily_report_task(**context):
    """Create daily report generation task"""
    http_hook = HttpHook(method='POST', http_conn_id='sentiment_api')
    
    # Get execution date
    execution_date = context['execution_date']
    target_date = (execution_date - timedelta(days=1)).strftime('%Y-%m-%d')
    
    task_payload = {
        "task_type": "daily_report_generation",
        "parameters": {
            "target_date": target_date,
            "website_ids": []  # Empty means all websites
        },
        "priority": 1,
        "timeout": 3600
    }
    
    response = http_hook.run(
        endpoint='/airflow/tasks',
        data=json.dumps(task_payload),
        headers={
            'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
            'Content-Type': 'application/json'
        }
    )
    
    task_data = json.loads(response.content)
    task_id = task_data['task_id']
    
    logging.info(f"Created daily report task: {task_id}")
    
    # Store task ID for monitoring
    context['task_instance'].xcom_push(key='report_task_id', value=task_id)
    
    return task_id

def monitor_task_completion(**context):
    """Monitor task completion"""
    import time
    
    # Get task ID from previous task
    task_id = context['task_instance'].xcom_pull(key='report_task_id')
    
    if not task_id:
        raise Exception("No task ID found")
    
    http_hook = HttpHook(method='GET', http_conn_id='sentiment_api')
    
    max_wait_time = 3600  # 1 hour
    check_interval = 30   # 30 seconds
    elapsed_time = 0
    
    while elapsed_time < max_wait_time:
        response = http_hook.run(
            endpoint=f'/airflow/tasks/{task_id}',
            headers={
                'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
                'Content-Type': 'application/json'
            }
        )
        
        task_status = json.loads(response.content)
        status = task_status['status']
        
        logging.info(f"Task {task_id} status: {status} (progress: {task_status['progress']}%)")
        
        if status == 'completed':
            logging.info(f"Task completed successfully: {task_status}")
            return task_status
        elif status == 'failed':
            raise Exception(f"Task failed: {task_status.get('error', 'Unknown error')}")
        elif status == 'cancelled':
            raise Exception(f"Task was cancelled: {task_status}")
        
        time.sleep(check_interval)
        elapsed_time += check_interval
    
    raise Exception(f"Task {task_id} did not complete within {max_wait_time} seconds")

def create_batch_processing_task(**context):
    """Create batch processing task for high-volume websites"""
    http_hook = HttpHook(method='POST', http_conn_id='sentiment_api')
    
    # Get execution date
    execution_date = context['execution_date']
    start_date = (execution_date - timedelta(days=1)).strftime('%Y-%m-%d')
    end_date = execution_date.strftime('%Y-%m-%d')
    
    # High-volume websites that need batch processing
    high_volume_websites = Variable.get("HIGH_VOLUME_WEBSITES", "").split(",")
    
    if not high_volume_websites or high_volume_websites == [""]:
        logging.info("No high-volume websites configured, skipping batch processing")
        return "skipped"
    
    batch_payload = {
        "website_ids": high_volume_websites,
        "date_range": {
            "start_date": start_date,
            "end_date": end_date
        },
        "processing_type": "daily_sentiment",
        "batch_size": 200
    }
    
    response = http_hook.run(
        endpoint='/airflow/batch-process',
        data=json.dumps(batch_payload),
        headers={
            'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
            'Content-Type': 'application/json'
        }
    )
    
    task_data = json.loads(response.content)
    task_id = task_data['task_id']
    
    logging.info(f"Created batch processing task: {task_id}")
    
    # Store task ID for monitoring
    context['task_instance'].xcom_push(key='batch_task_id', value=task_id)
    
    return task_id

def cleanup_old_data(**context):
    """Cleanup old data and logs"""
    http_hook = HttpHook(method='POST', http_conn_id='sentiment_api')
    
    cleanup_payload = {
        "task_type": "data_cleanup",
        "parameters": {
            "cleanup_type": "all",
            "retention_days": 30,
            "dry_run": False
        },
        "priority": 3,
        "timeout": 1800
    }
    
    response = http_hook.run(
        endpoint='/airflow/tasks',
        data=json.dumps(cleanup_payload),
        headers={
            'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
            'Content-Type': 'application/json'
        }
    )
    
    task_data = json.loads(response.content)
    task_id = task_data['task_id']
    
    logging.info(f"Created cleanup task: {task_id}")
    return task_id

def send_daily_summary(**context):
    """Send daily processing summary"""
    # Get results from previous tasks
    report_task_id = context['task_instance'].xcom_pull(key='report_task_id')
    batch_task_id = context['task_instance'].xcom_pull(key='batch_task_id')
    
    http_hook = HttpHook(method='GET', http_conn_id='sentiment_api')
    
    # Get metrics
    response = http_hook.run(
        endpoint='/airflow/metrics',
        headers={
            'Authorization': f'Bearer {AIRFLOW_API_TOKEN}',
            'Content-Type': 'application/json'
        }
    )
    
    metrics = json.loads(response.content)
    
    # Prepare summary
    execution_date = context['execution_date']
    summary = {
        "date": execution_date.strftime('%Y-%m-%d'),
        "report_task_id": report_task_id,
        "batch_task_id": batch_task_id,
        "metrics": metrics,
        "status": "completed"
    }
    
    logging.info(f"Daily processing summary: {summary}")
    
    # Here you could send email, Slack notification, etc.
    # For now, just log the summary
    
    return summary

# Task definitions
health_check = PythonOperator(
    task_id='check_system_health',
    python_callable=check_system_health,
    dag=dag
)

create_daily_report = PythonOperator(
    task_id='create_daily_report',
    python_callable=create_daily_report_task,
    dag=dag
)

monitor_daily_report = PythonOperator(
    task_id='monitor_daily_report',
    python_callable=monitor_task_completion,
    dag=dag
)

create_batch_processing = PythonOperator(
    task_id='create_batch_processing',
    python_callable=create_batch_processing_task,
    dag=dag
)

monitor_batch_processing = PythonOperator(
    task_id='monitor_batch_processing',
    python_callable=monitor_task_completion,
    dag=dag
)

cleanup_data = PythonOperator(
    task_id='cleanup_old_data',
    python_callable=cleanup_old_data,
    dag=dag
)

send_summary = PythonOperator(
    task_id='send_daily_summary',
    python_callable=send_daily_summary,
    dag=dag,
    trigger_rule='none_failed_or_skipped'  # Run even if some tasks are skipped
)

# Task dependencies
health_check >> [create_daily_report, create_batch_processing]
create_daily_report >> monitor_daily_report
create_batch_processing >> monitor_batch_processing
[monitor_daily_report, monitor_batch_processing] >> cleanup_data
cleanup_data >> send_summary

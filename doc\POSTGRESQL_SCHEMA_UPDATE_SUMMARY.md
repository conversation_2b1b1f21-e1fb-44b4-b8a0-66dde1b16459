# PostgreSQL Schema Update Summary

## Tổng quan

Đã cập nhật thành công codebase để hỗ trợ các trường dữ liệu mới cho PostgreSQL theo yêu cầu. Dữ liệu được thêm vào (append-only) chứ không xóa hay thay đổi dữ liệu cũ.

## Các thay đổi chính

### 1. C<PERSON><PERSON> nhật <PERSON>hema (app/models/schemas.py)

**DailySentimentRecord** đã được mở rộng với các trường mới:

```python
class DailySentimentRecord(BaseModel):
    # Thông tin cơ bản
    website_id: str
    session_id: str
    
    # Thông tin người dùng
    user_nickname: str
    user_id: str
    
    # Thông tin nhân viên
    agent_name: str
    agent_role: str = "operator"
    
    # Thờ<PERSON> gian hộ<PERSON>o<PERSON> (MỚI)
    conversation_start: int  # Unix timestamp
    conversation_end: Optional[int] = None  # Unix timestamp
    is_completed: bool = False
    
    # Kết quả phân tích
    overall_emotion: str
    sentiment_shift: str
    latest_sentiment: str
    
    # Metadata (MỚI)
    message_count: int
    analysis_timestamp: int  # Unix timestamp
```

### 2. Cập nhật PostgreSQL Service (app/services/postgresql_service.py)

#### Thay đổi Database Schema:
- Thêm các cột mới: `conversation_start`, `conversation_end`, `is_completed`, `message_count`, `analysis_timestamp`, `updated_at`
- Sử dụng BIGINT cho Unix timestamps
- Thêm trigger tự động cập nhật `updated_at`

#### Thay đổi Logic Upload:
- **Trước**: DELETE existing records → INSERT new records
- **Sau**: INSERT only (append-only approach)
- Dữ liệu được thêm vào chứ không xóa dữ liệu cũ

#### Cải thiện Error Handling:
- Khởi tạo tất cả thuộc tính ngay cả khi service bị disabled
- Xử lý an toàn trong `__del__` và `close()` methods

### 3. Cập nhật Daily Report Generator (app/services/daily_report_generator.py)

#### Thu thập dữ liệu mới:
```python
# Extract conversation timing
conversation_start = conversation.conversation_start
conversation_end = conversation.conversation_end
is_completed = conversation.is_completed

# Extract message count and analysis timestamp
message_count = conversation.message_count
analysis_timestamp = conversation.analysis_timestamp
```

#### Cập nhật conversion methods:
- `convert_records_to_dict_list()` bao gồm tất cả các trường mới
- Đảm bảo tương thích với PostgreSQL upload

## Cấu trúc dữ liệu PostgreSQL

### Bảng: daily_sentiment_reports

| Trường | Kiểu dữ liệu | Mô tả |
|--------|-------------|-------|
| **Thông tin cơ bản** |
| id | SERIAL PRIMARY KEY | ID tự động tăng |
| report_date | DATE | Ngày báo cáo |
| website_id | VARCHAR(255) | ID website |
| session_id | VARCHAR(255) | ID phiên hội thoại |
| **Thông tin người dùng** |
| user_nickname | VARCHAR(255) | Tên hiển thị người dùng |
| user_id | VARCHAR(255) | ID người dùng |
| **Thông tin nhân viên** |
| agent_name | VARCHAR(255) | Tên nhân viên |
| agent_role | VARCHAR(50) | Vai trò nhân viên |
| **Thời gian hội thoại** |
| conversation_start | BIGINT | Thời gian bắt đầu (Unix timestamp) |
| conversation_end | BIGINT | Thời gian kết thúc (Unix timestamp) |
| is_completed | BOOLEAN | Trạng thái hoàn thành |
| **Kết quả phân tích** |
| overall_emotion | VARCHAR(50) | Cảm xúc tổng thể |
| sentiment_shift | TEXT | Sự thay đổi cảm xúc |
| latest_sentiment | VARCHAR(50) | Cảm xúc mới nhất |
| **Metadata** |
| message_count | INTEGER | Số lượng tin nhắn |
| analysis_timestamp | BIGINT | Thời gian phân tích (Unix timestamp) |
| created_at | TIMESTAMP | Thời gian tạo bản ghi |
| updated_at | TIMESTAMP | Thời gian cập nhật bản ghi |

## Files được tạo/cập nhật

### Files được cập nhật:
1. `app/models/schemas.py` - Mở rộng DailySentimentRecord schema
2. `app/services/postgresql_service.py` - Cập nhật database schema và logic
3. `app/services/daily_report_generator.py` - Thu thập dữ liệu mới

### Files mới được tạo:
1. `doc/POSTGRESQL_TABLE_CREATION.md` - Câu lệnh SQL để tạo bảng
2. `scripts/test_postgresql_schema.py` - Script test các thay đổi
3. `doc/POSTGRESQL_SCHEMA_UPDATE_SUMMARY.md` - Tài liệu này

## Kiểm tra và Testing

### Script Test:
```bash
python scripts/test_postgresql_schema.py
```

### Kết quả Test:
- ✅ DailySentimentRecord schema validation
- ✅ DailyReportGenerator functionality
- ✅ PostgreSQL service initialization
- ✅ Data conversion và field mapping

## Tính năng mới

### 1. Append-only Data Storage
- Dữ liệu được thêm vào chứ không xóa
- Lưu trữ lịch sử đầy đủ
- Hỗ trợ audit trail

### 2. Comprehensive Conversation Tracking
- Thời gian bắt đầu/kết thúc hội thoại
- Trạng thái hoàn thành
- Số lượng tin nhắn
- Timestamp phân tích

### 3. Enhanced Metadata
- Thời gian tạo/cập nhật tự động
- Indexing tối ưu cho truy vấn
- Trigger tự động cập nhật

## Migration Notes

### Đối với database hiện tại:
1. Backup dữ liệu hiện tại
2. Chạy ALTER TABLE để thêm các cột mới
3. Cập nhật indexes
4. Test với dữ liệu mẫu

### Đối với database mới:
1. Sử dụng SQL script trong `doc/POSTGRESQL_TABLE_CREATION.md`
2. Tạo bảng với schema đầy đủ
3. Thiết lập indexes và triggers

## Lưu ý quan trọng

1. **Backward Compatibility**: Code mới tương thích với dữ liệu cũ
2. **Data Integrity**: Append-only approach đảm bảo không mất dữ liệu
3. **Performance**: Indexes được tối ưu cho các truy vấn thường dùng
4. **Error Handling**: Xử lý an toàn khi service disabled hoặc connection failed

## Các bước tiếp theo

1. Deploy code lên staging environment
2. Test với dữ liệu thực tế
3. Migrate production database
4. Monitor performance và logs
5. Cập nhật documentation cho team

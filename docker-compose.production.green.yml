version: '3.8'

services:
  # Main application - Production Green
  sentiment-api:
    image: ghcr.io/your-org/sentiment-analysis:latest
    container_name: sentiment-api-green
    restart: unless-stopped
    ports:
      - "8002:8000"  # Green environment port
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/sentiment_analysis
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - LOG_LEVEL=WARNING
      - DEBUG=false
      - WORKERS=4
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config:ro
      - ./postgresql_config.production.yaml:/app/postgresql_config.yaml:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # PostgreSQL database - Production (Shared)
  postgres:
    image: postgres:15-alpine
    container_name: sentiment-postgres-production
    restart: unless-stopped
    environment:
      POSTGRES_DB: sentiment_analysis
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_production_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./backups/postgres:/backups
    ports:
      - "5432:5432"
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Redis for caching - Production (Shared)
  redis:
    image: redis:7-alpine
    container_name: sentiment-redis-production
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_production_data:/data
    ports:
      - "6379:6379"
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 256M

  # Nginx reverse proxy - Production
  nginx:
    image: nginx:alpine
    container_name: sentiment-nginx-production
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.production.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_production_logs:/var/log/nginx
    depends_on:
      - sentiment-api
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

volumes:
  postgres_production_data:
    driver: local
  redis_production_data:
    driver: local
  nginx_production_logs:
    driver: local

networks:
  sentiment-production-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

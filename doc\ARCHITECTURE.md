# System Architecture - Sentiment Analysis with Periodic Processing

## Overview

The system is designed as a modular, scalable sentiment analysis platform with real-time processing capabilities and automated periodic reporting. It consists of several key components that work together to provide comprehensive sentiment analysis and reporting.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    FastAPI Application                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Webhook API   │  │ Sentiment API   │  │  Periodic API   │ │
│  │   (Messages)    │  │  (Analysis)     │  │   (Reports)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    Core Services Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Message Database│  │ Sentiment Service│  │Periodic Scheduler│ │
│  │   (Storage)     │  │  (ML Processing)│  │  (Automation)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │Conversation     │  │Periodic Processor│  │Nightly Generator│ │
│  │   Tracker       │  │  (Analysis)     │  │   (Reports)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    Data Storage Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Messages      │  │ Periodic Results│  │ Nightly Reports │ │
│  │  (JSON Files)   │  │  (JSON Files)   │  │  (JSON Files)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │ Conversation    │  │      Logs       │                      │
│  │   Tracking      │  │   (Log Files)   │                      │
│  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Message Database (`app/core/database.py`)

**Purpose**: Manages conversation data storage and retrieval

**Key Features**:
- Real-time message storage
- Conversation timeout management
- Automatic data persistence
- Thread-safe operations
- Integration with conversation tracker

**Data Flow**:
```
Webhook → MessageDatabase → JSON Storage
                ↓
        ConversationTracker
```

### 2. Sentiment Analysis Service (`app/services/sentiment_service.py`)

**Purpose**: Performs sentiment analysis using ML models

**Key Features**:
- Batch and immediate processing modes
- GPU/CPU optimization
- Queue management
- Three types of sentiment analysis:
  - Overall emotion
  - Sentiment shift
  - Latest sentiment

**Processing Flow**:
```
Request → Queue → Model Processing → Result Storage → Response
```

### 3. Conversation Tracker (`app/services/conversation_tracker.py`)

**Purpose**: Tracks conversation states and analysis history

**Key Features**:
- Last message timestamp tracking
- Sentiment analysis history
- Completion status management
- Periodic analysis eligibility

**Data Structure**:
```json
{
  "website_id_session_id": {
    "session_id": "string",
    "website_id": "string", 
    "last_message_timestamp": "timestamp",
    "last_sentiment_analysis_timestamp": "timestamp",
    "message_count": "number",
    "is_completed": "boolean"
  }
}
```

### 4. Periodic Sentiment Processor (`app/services/periodic_sentiment_processor.py`)

**Purpose**: Handles automated periodic sentiment analysis

**Key Features**:
- Configurable processing intervals
- Smart reprocessing logic
- Batch processing
- Result history management

**Processing Logic**:
```
1. Get conversations needing analysis
2. Check if new messages since last analysis
3. If yes → reprocess entire conversation
4. If no → use existing results
5. Store results and update tracking
```

### 5. Nightly Report Generator (`app/services/nightly_report_generator.py`)

**Purpose**: Generates consolidated nightly reports

**Key Features**:
- Time-window based collection
- Complete conversation data
- Sentiment distribution analysis
- Historical report storage

**Report Structure**:
```json
{
  "report_id": "string",
  "report_date": "YYYY-MM-DD",
  "total_conversations": "number",
  "sentiment_distribution": {
    "anger": "count",
    "satisfaction": "count"
  },
  "conversations": [...]
}
```

### 6. Periodic Scheduler (`app/services/periodic_scheduler.py`)

**Purpose**: Manages automated task scheduling

**Key Features**:
- Multiple interval scheduling
- Automatic nightly report generation
- Task status monitoring
- Configuration hot-reload

**Scheduling Logic**:
```
Intervals: [30min, 1h, 3h, 6h, 12h, 24h]
Nightly: Daily at configured time
```

## Data Flow Architecture

### 1. Real-time Message Processing

```
Crisp Webhook → FastAPI → MessageDatabase → ConversationTracker
                    ↓
            Sentiment Request → SentimentService → ML Model → Response
```

### 2. Periodic Processing Flow

```
Scheduler → PeriodicProcessor → ConversationTracker
                ↓
        Get conversations needing analysis
                ↓
        For each conversation:
          - Get conversation data
          - Perform sentiment analysis  
          - Update tracking info
                ↓
        Save results to JSON file
```

### 3. Nightly Report Generation

```
Scheduler (Daily) → NightlyGenerator
                ↓
        Get conversations in time window
                ↓
        Extract complete conversation data
                ↓
        Include existing sentiment analysis
                ↓
        Generate consolidated report
                ↓
        Save to nightly_reports/ directory
```

## Configuration Architecture

### Configuration Hierarchy

```yaml
config/config.yaml:
├── credentials (API keys)
├── server (FastAPI settings)
├── database (storage settings)
├── model (ML model config)
├── processing (analysis settings)
├── periodic_sentiment
│   ├── enabled
│   ├── periodic_statistics (intervals)
│   ├── nightly_report (time windows)
│   ├── storage (file paths)
│   └── processing (analysis settings)
└── prompts (sentiment analysis prompts)
```

### Environment Integration

- Configuration loaded from YAML
- Environment variables override YAML
- Automatic path resolution
- Directory creation on startup

## Threading and Concurrency

### Thread Architecture

```
Main Thread (FastAPI)
├── Database Auto-save Thread
├── Database Cleanup Thread  
├── Sentiment Processing Threads (configurable)
├── Periodic Scheduler Thread
│   ├── Periodic Processing (on schedule)
│   └── Nightly Report Generation (daily)
└── Resource Monitor Thread
```

### Thread Safety

- All shared data structures use threading.Lock
- Database operations are thread-safe
- Conversation tracker uses locks
- Result storage is protected

## File System Architecture

### Directory Structure

```
data/
├── messages.json                    # Main conversation storage
├── periodic_sentiment_results.json  # Periodic processing history
├── conversation_tracking.json       # Conversation state tracking
└── nightly_reports/                # Daily reports
    ├── nightly_report_2025-01-15.json
    ├── nightly_report_2025-01-16.json
    └── ...

logs/
├── app.log                         # Application logs
├── ngrok.log                       # Ngrok logs (if used)
└── ...

config/
├── config.yaml                     # Main configuration
├── allowed_websites.json           # Website whitelist
└── ...
```

### File Management

- Automatic directory creation
- Atomic file operations
- Backup and recovery support
- Log rotation (configurable)

## Scalability Considerations

### Horizontal Scaling

- Stateless API design
- File-based storage (can be moved to database)
- Configurable processing intervals
- Modular service architecture

### Performance Optimization

- Batch processing for efficiency
- Configurable thread pools
- GPU/CPU automatic optimization
- Smart reprocessing logic

### Resource Management

- Memory usage monitoring
- Automatic cleanup of old data
- Configurable timeouts
- Resource usage logging

## Error Handling and Resilience

### Error Recovery

- Graceful degradation
- Automatic retry mechanisms
- Error logging and monitoring
- Service isolation

### Data Integrity

- Atomic operations
- Backup mechanisms
- Validation at all levels
- Consistency checks

## Security Architecture

### Access Control

- Website ID validation
- Request authentication
- Rate limiting support
- Input sanitization

### Data Protection

- Secure file permissions
- Configuration validation
- Sensitive data handling
- Audit logging

## Monitoring and Observability

### Logging Strategy

- Structured logging
- Configurable log levels
- Performance metrics
- Error tracking

### Health Monitoring

- Service status endpoints
- Resource usage tracking
- Processing statistics
- Alert mechanisms

## Extension Points

### Adding New Analysis Types

1. Extend sentiment prompts in config
2. Update SentimentAnalysisResult schema
3. Modify processing logic
4. Update report generation

### Custom Report Formats

1. Create new generator classes
2. Implement custom data extraction
3. Add configuration options
4. Integrate with scheduler

### External Integrations

1. Database backends (PostgreSQL, MongoDB)
2. Message queues (Redis, RabbitMQ)
3. Cloud storage (S3, GCS)
4. Monitoring systems (Prometheus, Grafana)

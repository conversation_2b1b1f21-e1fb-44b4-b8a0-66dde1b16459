version: '3.8'

services:
  # Main application - Staging
  sentiment-api:
    image: ghcr.io/your-org/sentiment-analysis:latest
    container_name: sentiment-api-staging
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=staging
      - DATABASE_URL=****************************************************/sentiment_analysis_staging
      - REDIS_URL=redis://:staging_redis_password@redis:6379/0
      - LOG_LEVEL=INFO
      - DEBUG=false
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config:ro
      - ./postgresql_config.staging.yaml:/app/postgresql_config.yaml:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sentiment-staging-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # PostgreSQL database - Staging
  postgres:
    image: postgres:15-alpine
    container_name: sentiment-postgres-staging
    restart: unless-stopped
    environment:
      POSTGRES_DB: sentiment_analysis_staging
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: staging_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5433:5432"  # Different port for staging
    networks:
      - sentiment-staging-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  # Redis for caching - Staging
  redis:
    image: redis:7-alpine
    container_name: sentiment-redis-staging
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass staging_redis_password
    volumes:
      - redis_staging_data:/data
    ports:
      - "6380:6379"  # Different port for staging
    networks:
      - sentiment-staging-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 256M

  # Nginx reverse proxy - Staging
  nginx:
    image: nginx:alpine
    container_name: sentiment-nginx-staging
    restart: unless-stopped
    ports:
      - "8080:80"  # Different port for staging
    volumes:
      - ./nginx/nginx.staging.conf:/etc/nginx/nginx.conf:ro
      - nginx_staging_logs:/var/log/nginx
    depends_on:
      - sentiment-api
    networks:
      - sentiment-staging-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus - Staging
  prometheus:
    image: prom/prometheus:latest
    container_name: sentiment-prometheus-staging
    restart: unless-stopped
    ports:
      - "9091:9090"  # Different port for staging
    volumes:
      - ./monitoring/prometheus.staging.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_staging_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=72h'  # Shorter retention for staging
      - '--web.enable-lifecycle'
    networks:
      - sentiment-staging-network

  # Grafana for visualization - Staging
  grafana:
    image: grafana/grafana:latest
    container_name: sentiment-grafana-staging
    restart: unless-stopped
    ports:
      - "3001:3000"  # Different port for staging
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=staging_admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_staging_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - sentiment-staging-network

volumes:
  postgres_staging_data:
    driver: local
  redis_staging_data:
    driver: local
  prometheus_staging_data:
    driver: local
  grafana_staging_data:
    driver: local
  nginx_staging_logs:
    driver: local

networks:
  sentiment-staging-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

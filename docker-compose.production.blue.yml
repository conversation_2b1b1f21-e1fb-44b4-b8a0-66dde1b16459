version: '3.8'

services:
  # Main application - Production Blue
  sentiment-api:
    image: ghcr.io/your-org/sentiment-analysis:latest
    container_name: sentiment-api-blue
    restart: unless-stopped
    ports:
      - "8001:8000"  # Blue environment port
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/sentiment_analysis
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - LOG_LEVEL=WARNING
      - DEBUG=false
      - WORKERS=4
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config:ro
      - ./postgresql_config.production.yaml:/app/postgresql_config.yaml:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # PostgreSQL database - Production (Shared)
  postgres:
    image: postgres:15-alpine
    container_name: sentiment-postgres-production
    restart: unless-stopped
    environment:
      POSTGRES_DB: sentiment_analysis
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_production_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./backups/postgres:/backups
    ports:
      - "5432:5432"
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Redis for caching - Production (Shared)
  redis:
    image: redis:7-alpine
    container_name: sentiment-redis-production
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_production_data:/data
    ports:
      - "6379:6379"
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 256M

  # Nginx reverse proxy - Production
  nginx:
    image: nginx:alpine
    container_name: sentiment-nginx-production
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.production.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_production_logs:/var/log/nginx
    depends_on:
      - sentiment-api
    networks:
      - sentiment-production-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

  # Monitoring with Prometheus - Production
  prometheus:
    image: prom/prometheus:latest
    container_name: sentiment-prometheus-production
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.production.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_production_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=720h'  # 30 days retention
      - '--web.enable-lifecycle'
    networks:
      - sentiment-production-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  # Grafana for visualization - Production
  grafana:
    image: grafana/grafana:latest
    container_name: sentiment-grafana-production
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - GF_SECURITY_DISABLE_GRAVATAR=true
      - GF_ANALYTICS_REPORTING_ENABLED=false
    volumes:
      - grafana_production_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - sentiment-production-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

volumes:
  postgres_production_data:
    driver: local
  redis_production_data:
    driver: local
  prometheus_production_data:
    driver: local
  grafana_production_data:
    driver: local
  nginx_production_logs:
    driver: local

networks:
  sentiment-production-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

-- Database initialization script for Sentiment Analysis System
-- This script creates necessary tables and indexes

-- Create extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create daily_sentiment_reports table if it doesn't exist
CREATE TABLE IF NOT EXISTS daily_sentiment_reports (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    website_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    user_nickname VARCHAR(255),
    user_id VARCHAR(255),
    agent_name VARCHAR(255),
    agent_role VARCHAR(50) DEFAULT 'operator',
    overall_emotion VARCHAR(50),
    sentiment_shift TEXT,
    latest_sentiment VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_date ON daily_sentiment_reports(report_date);
CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_website ON daily_sentiment_reports(website_id);
CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_session ON daily_sentiment_reports(session_id);
CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_agent ON daily_sentiment_reports(agent_name);
CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_emotion ON daily_sentiment_reports(overall_emotion);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_composite ON daily_sentiment_reports(report_date, website_id, session_id);

-- Create system_health table for monitoring
CREATE TABLE IF NOT EXISTS system_health (
    id SERIAL PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    response_time_ms INTEGER,
    error_message TEXT,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for system_health
CREATE INDEX IF NOT EXISTS idx_system_health_service ON system_health(service_name);
CREATE INDEX IF NOT EXISTS idx_system_health_checked_at ON system_health(checked_at);

-- Create deployment_history table
CREATE TABLE IF NOT EXISTS deployment_history (
    id SERIAL PRIMARY KEY,
    version VARCHAR(100) NOT NULL,
    environment VARCHAR(50) NOT NULL,
    deployed_by VARCHAR(100),
    deployment_status VARCHAR(20) NOT NULL,
    deployment_start TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deployment_end TIMESTAMP WITH TIME ZONE,
    rollback_version VARCHAR(100),
    notes TEXT
);

-- Create index for deployment_history
CREATE INDEX IF NOT EXISTS idx_deployment_history_env ON deployment_history(environment);
CREATE INDEX IF NOT EXISTS idx_deployment_history_version ON deployment_history(version);
CREATE INDEX IF NOT EXISTS idx_deployment_history_date ON deployment_history(deployment_start);

-- Create performance_metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    metric_unit VARCHAR(20),
    environment VARCHAR(50) NOT NULL,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for performance_metrics
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_env ON performance_metrics(environment);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_date ON performance_metrics(recorded_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for daily_sentiment_reports
DROP TRIGGER IF EXISTS update_daily_sentiment_reports_updated_at ON daily_sentiment_reports;
CREATE TRIGGER update_daily_sentiment_reports_updated_at
    BEFORE UPDATE ON daily_sentiment_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial system health check
INSERT INTO system_health (service_name, status, response_time_ms) 
VALUES ('database', 'healthy', 0) 
ON CONFLICT DO NOTHING;

-- Create user for application (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'sentiment_app') THEN
        CREATE USER sentiment_app WITH PASSWORD 'app_password';
    END IF;
END
$$;

-- Grant permissions to application user
GRANT SELECT, INSERT, UPDATE, DELETE ON daily_sentiment_reports TO sentiment_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON system_health TO sentiment_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON deployment_history TO sentiment_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON performance_metrics TO sentiment_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO sentiment_app;

-- Create view for recent sentiment analysis
CREATE OR REPLACE VIEW recent_sentiment_analysis AS
SELECT 
    report_date,
    website_id,
    COUNT(*) as total_conversations,
    COUNT(CASE WHEN overall_emotion = 'satisfaction' THEN 1 END) as satisfied_count,
    COUNT(CASE WHEN overall_emotion = 'anger' THEN 1 END) as angry_count,
    COUNT(CASE WHEN overall_emotion = 'frustration' THEN 1 END) as frustrated_count,
    COUNT(CASE WHEN overall_emotion = 'anxiety' THEN 1 END) as anxious_count,
    COUNT(CASE WHEN overall_emotion = 'neutral' THEN 1 END) as neutral_count,
    ROUND(
        COUNT(CASE WHEN overall_emotion = 'satisfaction' THEN 1 END) * 100.0 / COUNT(*), 2
    ) as satisfaction_percentage
FROM daily_sentiment_reports
WHERE report_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY report_date, website_id
ORDER BY report_date DESC, website_id;

-- Grant access to view
GRANT SELECT ON recent_sentiment_analysis TO sentiment_app;

-- Create function for health check
CREATE OR REPLACE FUNCTION health_check()
RETURNS TABLE(
    service VARCHAR(100),
    status VARCHAR(20),
    last_check TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sh.service_name,
        sh.status,
        sh.checked_at
    FROM system_health sh
    WHERE sh.id IN (
        SELECT MAX(id)
        FROM system_health
        GROUP BY service_name
    )
    ORDER BY sh.service_name;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION health_check() TO sentiment_app;

-- Log successful initialization
INSERT INTO system_health (service_name, status, response_time_ms, error_message) 
VALUES ('database_init', 'completed', 0, 'Database initialization completed successfully');

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE 'Database initialization completed successfully';
    RAISE NOTICE 'Tables created: daily_sentiment_reports, system_health, deployment_history, performance_metrics';
    RAISE NOTICE 'Views created: recent_sentiment_analysis';
    RAISE NOTICE 'Functions created: health_check()';
    RAISE NOTICE 'User created: sentiment_app (with appropriate permissions)';
END
$$;

from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# Create a router for the webhook routes
router = APIRouter(tags=["webhooks"])


def _extract_agent_information(webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract and enhance webhook data with agent information.

    Args:
        webhook_data: Raw webhook data

    Returns:
        Enhanced webhook data with agent information
    """
    try:
        # Create a copy of the original data
        enhanced_data = webhook_data.copy()

        # Extract agent information from various possible sources
        agent_info = _get_agent_info_from_webhook(webhook_data)

        # Add agent information to the data structure
        if "data" in enhanced_data:
            enhanced_data["data"]["agent_info"] = agent_info

        logger.debug(f"Extracted agent info: {agent_info}")
        return enhanced_data

    except Exception as e:
        logger.error(f"Error extracting agent information: {e}")
        # Return original data if extraction fails
        return webhook_data


def _get_agent_info_from_webhook(webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract agent information from webhook data.

    Args:
        webhook_data: Raw webhook data

    Returns:
        Dictionary with agent information
    """
    agent_info = {
        "name": None,
        "role": "operator",
        "user_id": None,
        "origin": None
    }

    try:
        data = webhook_data.get("data", {})

        # Extract role from message
        role = data.get("from", "user")
        agent_info["role"] = role

        # Extract origin information
        origin = data.get("origin", "chat")
        agent_info["origin"] = origin

        # Extract agent name and ID from different possible sources
        if role == "operator":
            # Try to extract agent information from various webhook fields

            # Method 1: From user field (if it contains agent info)
            user_info = data.get("user", {})
            if isinstance(user_info, dict):
                # Check if this is actually agent info
                if "nickname" in user_info:
                    agent_info["name"] = user_info.get("nickname")
                    agent_info["user_id"] = user_info.get("user_id")

            # Method 2: From origin field (for bot messages)
            if origin and origin.startswith("urn:crisp.im:bot:"):
                agent_info["name"] = "Bot Assistant"
                agent_info["role"] = "bot"

            # Method 3: From operator field (if exists)
            operator_info = data.get("operator", {})
            if isinstance(operator_info, dict):
                if "nickname" in operator_info:
                    agent_info["name"] = operator_info.get("nickname")
                    agent_info["user_id"] = operator_info.get("user_id")

            # Method 4: From metadata or custom fields
            metadata = data.get("metadata", {})
            if isinstance(metadata, dict):
                if "agent_name" in metadata:
                    agent_info["name"] = metadata.get("agent_name")
                if "agent_id" in metadata:
                    agent_info["user_id"] = metadata.get("agent_id")

        # Default agent name if not found
        if not agent_info["name"] and role == "operator":
            agent_info["name"] = "Support Agent"

        logger.debug(f"Extracted agent info from webhook: {agent_info}")

    except Exception as e:
        logger.error(f"Error parsing agent info from webhook: {e}")

    return agent_info

@router.post("/webhook")
@router.get("/webhook")
async def webhook_listener(request: Request):
    """Webhook endpoint for receiving messages"""
    try:
        logger.info("Received webhook request")

        # Parse the request data
        body_bytes = await request.body()
        body_str = body_bytes.decode('utf-8')
        data = json.loads(body_str)

        # Log webhook data for debugging (remove in production)
        logger.debug(f"Webhook data: {json.dumps(data, indent=2)}")

        # Process only message events
        event_type = data.get("event")
        if event_type in ["message:send", "message:received"]:
            # Validate website ID against whitelist
            website_id = data.get("website_id")
            if not website_id:
                logger.warning("Missing website_id in webhook data")
                return {"status": "error", "message": "Missing website_id"}

            # Check if website ID is allowed
            from app import get_website_validator
            website_validator = get_website_validator()
            if not website_validator.validate_website_id(website_id):
                logger.warning(f"Rejected webhook from unauthorized website_id: {website_id}")
                return {"status": "rejected", "message": "Unauthorized website_id"}

            # Extract and enhance message data with agent information
            enhanced_data = _extract_agent_information(data)

            # Store the enhanced message in the database
            from app import get_db
            db = get_db()
            db.add_message(enhanced_data)
            logger.info(f"Stored message from event: {event_type} for website_id: {website_id}")

        return {"status": "received"}
    except Exception as e:
        logger.error(f"Error in webhook_listener: {e}")
        return JSONResponse(
            content={"status": "error", "message": str(e)},
            status_code=500
        )

from fastapi import APIRouter, Request, Response, status
from fastapi.responses import JSONResponse
import json
import logging

logger = logging.getLogger(__name__)

# Create a router for the webhook routes
router = APIRouter(tags=["webhooks"])

@router.post("/webhook")
@router.get("/webhook")
async def webhook_listener(request: Request):
    """Webhook endpoint for receiving messages"""
    try:
        logger.info("Received webhook request")

        # Parse the request data
        body_bytes = await request.body()
        body_str = body_bytes.decode('utf-8')
        data = json.loads(body_str)

        # Process only message events
        event_type = data.get("event")
        if event_type in ["message:send", "message:received"]:
            # Validate website ID against whitelist
            website_id = data.get("website_id")
            if not website_id:
                logger.warning("Missing website_id in webhook data")
                return {"status": "error", "message": "Missing website_id"}

            # Check if website ID is allowed
            from app import get_website_validator
            website_validator = get_website_validator()
            if not website_validator.validate_website_id(website_id):
                logger.warning(f"Rejected webhook from unauthorized website_id: {website_id}")
                return {"status": "rejected", "message": "Unauthorized website_id"}

            # Store the message in the database
            from app import get_db
            db = get_db()
            db.add_message(data)
            logger.info(f"Stored message from event: {event_type} for website_id: {website_id}")

        return {"status": "received"}
    except Exception as e:
        logger.error(f"Error in webhook_listener: {e}")
        return JSONResponse(
            content={"status": "error", "message": str(e)},
            status_code=500
        )

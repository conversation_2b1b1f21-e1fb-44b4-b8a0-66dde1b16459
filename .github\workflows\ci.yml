name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.10'
  NODE_VERSION: '18'

jobs:
  # Job 1: Code Quality and Security
  code-quality:
    runs-on: ubuntu-latest
    name: Code Quality & Security
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort bandit safety mypy
        pip install -r requirements.txt
        
    - name: Code formatting check (Black)
      run: black --check --diff .
      
    - name: Import sorting check (isort)
      run: isort --check-only --diff .
      
    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: Type checking with mypy
      run: mypy app/ --ignore-missing-imports
      continue-on-error: true
      
    - name: Security scan with bandit
      run: bandit -r app/ -f json -o bandit-report.json
      continue-on-error: true
      
    - name: Check dependencies for security vulnerabilities
      run: safety check --json --output safety-report.json
      continue-on-error: true
      
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # Job 2: Unit Tests
  unit-tests:
    runs-on: ubuntu-latest
    name: Unit Tests
    needs: code-quality
    
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('**/requirements.txt') }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-asyncio pytest-mock
        pip install -r requirements.txt
        
    - name: Run unit tests with coverage
      run: |
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html --cov-report=term
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          htmlcov/
          coverage.xml

  # Job 3: Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    name: Integration Tests
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_sentiment_analysis
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-asyncio pytest-mock
        pip install -r requirements.txt
        
    - name: Create test config files
      run: |
        cp config/config.yaml config/test_config.yaml
        cp postgresql_config.yaml test_postgresql_config.yaml
        
    - name: Update test PostgreSQL config
      run: |
        sed -i 's/localhost/localhost/g' test_postgresql_config.yaml
        sed -i 's/postgres/test_user/g' test_postgresql_config.yaml
        sed -i 's/password/test_password/g' test_postgresql_config.yaml
        sed -i 's/sentiment_analysis/test_sentiment_analysis/g' test_postgresql_config.yaml
        
    - name: Run integration tests
      env:
        POSTGRESQL_CONFIG_PATH: test_postgresql_config.yaml
        CONFIG_PATH: config/test_config.yaml
      run: |
        pytest tests/integration/ -v --tb=short
        
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: |
          tests/integration/reports/

  # Job 4: Build and Test Docker Image
  docker-build:
    runs-on: ubuntu-latest
    name: Docker Build & Test
    needs: [unit-tests, integration-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: sentiment-analysis:test
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Test Docker image
      run: |
        docker run --rm -d --name test-container -p 8000:8000 sentiment-analysis:test
        sleep 30
        curl -f http://localhost:8000/health || exit 1
        docker stop test-container
        
    - name: Save Docker image
      run: |
        docker save sentiment-analysis:test | gzip > sentiment-analysis-test.tar.gz
        
    - name: Upload Docker image artifact
      uses: actions/upload-artifact@v3
      with:
        name: docker-image
        path: sentiment-analysis-test.tar.gz
        retention-days: 1

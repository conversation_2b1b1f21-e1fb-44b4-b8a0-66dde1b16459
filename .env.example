# Environment Configuration Example
# Copy this file to .env and update with your actual values

# Environment
ENVIRONMENT=development

# Database Configuration
POSTGRES_PASSWORD=your_secure_password
POSTGRES_USER=postgres
POSTGRES_DB=sentiment_analysis
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Redis Configuration
REDIS_PASSWORD=your_redis_password
REDIS_HOST=localhost
REDIS_PORT=6379

# Application Configuration
LOG_LEVEL=INFO
DEBUG=true
WORKERS=1

# API Credentials (from config/config.yaml)
CRISP_IDENTIFIER=your_crisp_identifier
CRISP_KEY=your_crisp_key
CRISP_WEBHOOK_SECRET=your_webhook_secret
NGROK_AUTH_TOKEN=your_ngrok_token
HUGGINGFACE_TOKEN=your_huggingface_token

# Monitoring
GRAFANA_ADMIN_PASSWORD=your_grafana_password

# Deployment Configuration
STAGING_HOST=your-staging-server.com
STAGING_USER=deploy
STAGING_PATH=/opt/sentiment-analysis
STAGING_URL=https://staging.your-domain.com
STAGING_PORT=22

PRODUCTION_HOST=your-production-server.com
PRODUCTION_USER=deploy
PRODUCTION_PATH=/opt/sentiment-analysis
PRODUCTION_URL=https://your-domain.com
PRODUCTION_PORT=22

# Notification Configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# SSL Configuration (for production)
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Security Configuration
JWT_SECRET_KEY=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# Performance Configuration
MAX_WORKERS=4
WORKER_TIMEOUT=30
KEEP_ALIVE=2

# Feature Flags
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_RATE_LIMITING=true
ENABLE_CACHING=true

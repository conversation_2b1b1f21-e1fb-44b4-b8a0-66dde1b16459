"""
Sentiment Analysis API System
----------------------------
A webhook-based system for sentiment analysis using the Qwen/Qwen2.5-0.5B-Instruct model.
"""

import logging
import sys
import os

# Setup logging
# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/app.log')
    ]
)
logger = logging.getLogger(__name__)

# Global service instances
# These variables will be set in main.py and are used throughout the application
# They are initialized here to avoid circular imports

# Database instance for storing and retrieving messages
# Will be set to a MessageDatabase instance in main.py
db = None

# Sentiment analysis service for processing text sentiment
# Will be set to a SentimentAnalysisService instance in main.py
sentiment_analyzer = None

# Async sentiment analysis service for non-blocking processing
# Will be set to an AsyncSentimentService instance in main.py
async_sentiment_analyzer = None

# Hugging Face service for interacting with Hugging Face Hub
# Will be set to a HuggingFaceService instance in main.py
huggingface_service = None

# Website validator for validating website IDs against a whitelist
# Will be set to a WebsiteValidator instance in main.py
website_validator = None


def get_db():
    """Get the database instance, raising an error if it's not initialized"""
    if db is None:
        raise RuntimeError("Database is not initialized. Make sure the application has been properly started.")
    return db


def get_sentiment_analyzer():
    """Get the sentiment analyzer instance, raising an error if it's not initialized"""
    if sentiment_analyzer is None:
        raise RuntimeError("Sentiment analyzer is not initialized. Make sure the application has been properly started.")
    return sentiment_analyzer


def get_async_sentiment_analyzer():
    """Get the async sentiment analyzer instance, raising an error if it's not initialized"""
    if async_sentiment_analyzer is None:
        raise RuntimeError("Async sentiment analyzer is not initialized. Make sure the application has been properly started.")
    return async_sentiment_analyzer


def get_huggingface_service():
    """Get the Hugging Face service instance, raising an error if it's not initialized"""
    if huggingface_service is None:
        raise RuntimeError("Hugging Face service is not initialized. Make sure the application has been properly started.")
    return huggingface_service


def get_website_validator():
    """Get the website validator instance, raising an error if it's not initialized"""
    if website_validator is None:
        raise RuntimeError("Website validator is not initialized. Make sure the application has been properly started.")
    return website_validator

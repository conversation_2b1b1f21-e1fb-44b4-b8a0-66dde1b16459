# CI/CD Pipeline Guide

## Tổng quan

Hệ thống CI/CD được thiết kế để đảm bảo chất lượng code, tự động hóa testing, và triển khai an toàn cho ứng dụng Sentiment Analysis.

## Kiến trúc CI/CD

### 1. Continuous Integration (CI)
- **Code Quality**: Black, isort, flake8, mypy
- **Security Scanning**: Bandit, Safety
- **Unit Testing**: pytest với coverage
- **Integration Testing**: PostgreSQL, Redis
- **Docker Build & Test**: Multi-stage builds

### 2. Continuous Deployment (CD)
- **Staging Deployment**: Tự động từ main branch
- **Production Deployment**: Blue-green deployment từ tags
- **Security Scanning**: Trivy container scanning
- **Performance Testing**: k6 load testing
- **Monitoring**: Prometheus, Grafana

## Workflow Structure

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Code Quality  │    │   Unit Tests    │    │ Integration     │
│   & Security    │───▶│   (Multi-ver)   │───▶│ Tests           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Docker Build  │◀───│   Build & Push  │◀───│                 │
│   & Test        │    │   to Registry   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   Deploy to     │
                    │   Staging       │
                    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   Performance   │
                    │   Testing       │
                    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   Deploy to     │
                    │   Production    │
                    │   (Tags only)   │
                    └─────────────────┘
```

## Environment Setup

### 1. GitHub Secrets

Cấu hình các secrets sau trong GitHub repository:

#### Staging Environment
```
STAGING_HOST=your-staging-server.com
STAGING_USER=deploy
STAGING_SSH_KEY=<private-ssh-key>
STAGING_PORT=22
STAGING_PATH=/opt/sentiment-analysis
STAGING_URL=https://staging.your-domain.com
```

#### Production Environment
```
PRODUCTION_HOST=your-production-server.com
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=<private-ssh-key>
PRODUCTION_PORT=22
PRODUCTION_PATH=/opt/sentiment-analysis
PRODUCTION_URL=https://your-domain.com
```

#### Notification
```
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

#### Container Registry
```
GITHUB_TOKEN=<automatically-provided>
```

### 2. Server Setup

#### Staging Server
```bash
# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create deployment directory
sudo mkdir -p /opt/sentiment-analysis
sudo chown $USER:$USER /opt/sentiment-analysis

# Clone repository
cd /opt/sentiment-analysis
git clone <your-repo-url> .

# Setup environment
cp .env.example .env.staging
# Edit .env.staging with staging values

# Create staging-specific configs
cp postgresql_config.yaml postgresql_config.staging.yaml
# Edit staging config
```

#### Production Server
```bash
# Same Docker setup as staging

# Create production directory
sudo mkdir -p /opt/sentiment-analysis
sudo chown $USER:$USER /opt/sentiment-analysis

# Clone repository
cd /opt/sentiment-analysis
git clone <your-repo-url> .

# Setup environment
cp .env.example .env.production
# Edit .env.production with production values

# Create production-specific configs
cp postgresql_config.yaml postgresql_config.production.yaml
# Edit production config

# Setup SSL certificates
sudo mkdir -p /opt/sentiment-analysis/nginx/ssl
# Copy SSL certificates
```

## Deployment Strategies

### 1. Staging Deployment (Rolling Update)

**Trigger**: Push to `main` branch

**Process**:
1. Pull new Docker image
2. Scale up new instance
3. Health check new instance
4. Scale down old instance
5. Run smoke tests

**Rollback**: Automatic on failure

### 2. Production Deployment (Blue-Green)

**Trigger**: Push tag `v*` (e.g., `v1.0.0`)

**Process**:
1. Deploy to inactive environment (blue/green)
2. Comprehensive health checks
3. Switch traffic via nginx
4. Monitor for stability
5. Shutdown old environment

**Rollback**: Manual script generated

## Testing Strategy

### 1. Unit Tests
```bash
# Run locally
pytest tests/ -v --cov=app

# Multiple Python versions in CI
pytest tests/ -v --cov=app --cov-report=xml
```

### 2. Integration Tests
```bash
# With PostgreSQL and Redis
docker-compose -f docker-compose.test.yml up -d
pytest tests/integration/ -v
```

### 3. Performance Tests
```bash
# k6 load testing
k6 run tests/performance/load-test.js --env BASE_URL=https://staging.your-domain.com
```

### 4. Security Tests
```bash
# Code security
bandit -r app/ -f json

# Dependency security
safety check

# Container security
trivy image your-image:tag
```

## Monitoring và Alerting

### 1. Application Metrics
- Response time
- Error rate
- Request volume
- Database connections
- Memory/CPU usage

### 2. Infrastructure Metrics
- Container health
- Database performance
- Redis performance
- Nginx metrics

### 3. Alerts
- High error rate (>5%)
- Slow response time (>1s p95)
- High memory usage (>80%)
- Database connection issues
- Deployment failures

## Best Practices

### 1. Code Quality
- Sử dụng pre-commit hooks
- Code review bắt buộc
- Automated formatting (Black, isort)
- Type hints (mypy)

### 2. Security
- Regular dependency updates
- Container scanning
- Secrets management
- Network security

### 3. Deployment
- Feature flags
- Database migrations
- Backup before deployment
- Monitoring after deployment

### 4. Testing
- Test coverage >80%
- Integration tests cho critical paths
- Performance benchmarks
- Security testing

## Troubleshooting

### 1. CI Pipeline Failures

#### Code Quality Issues
```bash
# Fix formatting
black .
isort .

# Fix linting
flake8 . --show-source
```

#### Test Failures
```bash
# Run tests locally
pytest tests/ -v -x --tb=short

# Debug specific test
pytest tests/test_specific.py::test_function -v -s
```

#### Docker Build Issues
```bash
# Build locally
docker build -t sentiment-analysis:test .

# Check logs
docker logs <container-id>
```

### 2. Deployment Issues

#### Staging Deployment
```bash
# Check deployment logs
tail -f /opt/sentiment-analysis/logs/deploy-staging.log

# Manual health check
curl -f https://staging.your-domain.com/health

# Check container status
docker-compose -f docker-compose.staging.yml ps
```

#### Production Deployment
```bash
# Check deployment logs
tail -f /opt/sentiment-analysis/logs/deploy-production.log

# Check current environment
cat /opt/sentiment-analysis/.current-production-env

# Manual rollback
./rollback-production.sh
```

### 3. Performance Issues

#### Monitoring
```bash
# Check Grafana dashboards
# URL: https://your-domain.com:3000

# Check Prometheus metrics
# URL: https://your-domain.com:9090
```

#### Database Performance
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Check connections
docker-compose exec postgres psql -U postgres -c "SELECT * FROM pg_stat_activity;"
```

## Maintenance

### 1. Regular Tasks
- Update dependencies monthly
- Review and rotate secrets quarterly
- Clean up old Docker images weekly
- Review monitoring alerts weekly

### 2. Backup Strategy
- Database backups daily
- Configuration backups before deployment
- Docker image backups for rollback

### 3. Disaster Recovery
- Documented recovery procedures
- Regular recovery testing
- Off-site backups
- RTO/RPO targets defined

## Getting Started

1. **Setup Repository**:
   ```bash
   git clone <your-repo>
   cd sentiment-analysis
   cp .env.example .env
   # Edit .env with your values
   ```

2. **Local Development**:
   ```bash
   docker-compose up -d
   # Application available at http://localhost:8000
   ```

3. **Run Tests**:
   ```bash
   pytest tests/ -v
   ```

4. **Deploy to Staging**:
   ```bash
   git push origin main
   # Watch GitHub Actions for deployment
   ```

5. **Deploy to Production**:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   # Watch GitHub Actions for deployment
   ```

## Quick Start Commands

### 1. Setup Server
```bash
# Setup staging server
./scripts/setup-server.sh staging

# Setup production server
./scripts/setup-server.sh production
```

### 2. Deploy Application
```bash
# Deploy to staging (automatic on main branch push)
git push origin main

# Deploy to production (create and push tag)
git tag v1.0.0
git push origin v1.0.0
```

### 3. Health Checks
```bash
# Basic health check
./scripts/health-check.sh staging

# Detailed health check
./scripts/health-check.sh production --detailed
```

### 4. Rollback (if needed)
```bash
# Rollback staging to previous version
./scripts/rollback.sh staging

# Rollback production to specific version
./scripts/rollback.sh production v1.0.0
```

### 5. Manual Deployment
```bash
# Manual staging deployment
./scripts/deploy-staging.sh ghcr.io/your-org/sentiment-analysis:latest

# Manual production deployment
./scripts/deploy-production.sh ghcr.io/your-org/sentiment-analysis:v1.0.0
```

## File Structure Summary

```
.github/workflows/          # GitHub Actions workflows
├── ci.yml                  # Continuous Integration
└── cd.yml                  # Continuous Deployment

scripts/                    # Deployment and utility scripts
├── setup-server.sh         # Server setup script
├── deploy-staging.sh       # Staging deployment
├── deploy-production.sh    # Production blue-green deployment
├── health-check.sh         # Health check script
├── rollback.sh            # Rollback script
└── init-db.sql            # Database initialization

docker-compose*.yml         # Docker Compose configurations
├── docker-compose.yml      # Development
├── docker-compose.staging.yml
├── docker-compose.production.blue.yml
└── docker-compose.production.green.yml

monitoring/                 # Monitoring configuration
├── prometheus.yml          # Prometheus config
├── alert_rules.yml         # Alert rules
└── grafana/               # Grafana dashboards

nginx/                     # Nginx configuration
└── nginx.conf             # Reverse proxy config

tests/                     # Test suites
├── integration/           # Integration tests
└── performance/           # Performance tests
```

## Monitoring URLs

### Staging
- Application: http://staging.your-domain.com:8080
- Grafana: http://staging.your-domain.com:3001
- Prometheus: http://staging.your-domain.com:9091

### Production
- Application: https://your-domain.com
- Grafana: https://your-domain.com:3000
- Prometheus: https://your-domain.com:9090

## Support and Troubleshooting

### Common Issues

1. **Docker permission denied**:
   ```bash
   sudo usermod -aG docker $USER
   # Logout and login again
   ```

2. **Port already in use**:
   ```bash
   sudo netstat -tulpn | grep :8000
   sudo kill -9 <PID>
   ```

3. **SSL certificate issues**:
   ```bash
   # Check certificate
   openssl x509 -in nginx/ssl/cert.pem -text -noout

   # Renew Let's Encrypt certificate
   sudo certbot renew
   ```

4. **Database connection issues**:
   ```bash
   # Check PostgreSQL logs
   docker-compose logs postgres

   # Test connection
   docker-compose exec postgres psql -U postgres -c "SELECT 1;"
   ```

### Log Locations

- Application logs: `/opt/sentiment-analysis/logs/`
- Deployment logs: `/opt/sentiment-analysis/logs/deploy-*.log`
- Nginx logs: `/opt/sentiment-analysis/logs/nginx/`
- System logs: `/var/log/syslog`

### Emergency Contacts

- DevOps Team: <EMAIL>
- On-call Engineer: +1-xxx-xxx-xxxx
- Slack Channel: #sentiment-analysis-alerts

Để biết thêm chi tiết, xem các file documentation khác trong thư mục `doc/`.

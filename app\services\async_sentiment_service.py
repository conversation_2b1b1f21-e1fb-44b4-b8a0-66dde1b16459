"""
Asynchronous Sentiment Analysis Service
--------------------------------------
This module provides an asynchronous wrapper around the SentimentAnalysisService
to enable non-blocking processing of sentiment analysis requests.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor

from app.services.sentiment_service import SentimentAnalysisService

logger = logging.getLogger(__name__)

class AsyncSentimentService:
    """
    Asynchronous wrapper for the SentimentAnalysisService.
    This class provides asynchronous methods for adding requests and getting results.
    """

    def __init__(self, sentiment_service: SentimentAnalysisService):
        """
        Initialize the asynchronous sentiment service.

        Args:
            sentiment_service: The underlying SentimentAnalysisService instance
        """
        self.sentiment_service = sentiment_service
        self.executor = ThreadPoolExecutor(max_workers=1)
        self._processing_tasks = {}  # Track async tasks by request_id
        self._loop = asyncio.get_event_loop()

    async def add_request(self, request_data: Dict[str, Any]) -> str:
        """
        Add a request for processing asynchronously.

        Args:
            request_data: Request data dictionary containing website_id, session_id, and item_id

        Returns:
            request_id: Unique request ID
        """
        # Generate a unique request ID (same format as in SentimentAnalysisService)
        request_id = f"{request_data['website_id']}_{request_data['session_id']}_{request_data['item_id']}_{int(time.time()*1000)}"

        # Create an async task to process the request
        task = self._loop.create_task(self._process_request(request_id, request_data))
        self._processing_tasks[request_id] = task

        # Return the request ID immediately
        return request_id

    async def _process_request(self, request_id: str, request_data: Dict[str, Any]) -> None:
        """
        Process a request asynchronously.

        Args:
            request_id: The unique request ID
            request_data: Request data dictionary
        """
        try:
            # Run the synchronous add_request method in a thread pool
            await self._loop.run_in_executor(
                self.executor,
                lambda: self.sentiment_service.add_request({
                    'request_id': request_id,
                    'item_id': request_data['item_id'],
                    'session_id': request_data['session_id'],
                    'website_id': request_data['website_id'],
                    'priority': request_data.get('priority', 0)
                })
            )
        except Exception as e:
            logger.error(f"Error in async processing of request {request_id}: {e}")
        finally:
            # Clean up the task reference
            if request_id in self._processing_tasks:
                del self._processing_tasks[request_id]

    async def get_result(self, request_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the result for a request asynchronously.

        Args:
            request_id: Request ID

        Returns:
            result: Result or None if not ready
        """
        # Run the synchronous get_result method in a thread pool without logging
        return await self._loop.run_in_executor(
            self.executor,
            lambda: self.sentiment_service.get_result(request_id)
        )

    async def wait_for_result(self, request_id: str, timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        Wait for the result of a request with a timeout.

        Args:
            request_id: Request ID
            timeout: Maximum time to wait in seconds

        Returns:
            result: Result or None if not ready within the timeout
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            result = await self.get_result(request_id)
            if result:
                return result
            # Use asyncio.sleep instead of time.sleep to avoid blocking the event loop
            await asyncio.sleep(0.1)
        return None

    async def get_queue_status(self) -> Dict[str, Any]:
        """
        Get the current status of the sentiment analysis queue asynchronously.

        Returns:
            dict: Queue status information
        """
        # Run the synchronous get_queue_status method in a thread pool
        return await self._loop.run_in_executor(
            self.executor,
            self.sentiment_service.get_queue_status
        )

    def shutdown(self):
        """
        Shut down the async service and its executor.
        """
        self.executor.shutdown(wait=True)

# PostgreSQL Configuration Guide

## Tổng quan

Từ phiên bản n<PERSON>, c<PERSON>u hình PostgreSQL đã được tách riêng khỏi file `config/config.yaml` chính và được lưu trong file `postgresql_config.yaml` ở thư mục gốc của project.

## Lý do tách riêng

1. **B<PERSON>o mật tốt hơn**: Thông tin đăng nhập database đư<PERSON><PERSON> t<PERSON><PERSON>, dễ dàng quản lý quyền truy cập
2. **Quản lý dễ dàng**: <PERSON><PERSON> thể backup, restore, hoặc chia sẻ cấu hình PostgreSQL độc lập
3. **Tổ chức rõ ràng**: Tách biệt cấu hình database khỏi cấu hình ứng dụng chính
4. **Linh hoạt triển khai**: <PERSON><PERSON> <PERSON>ể sử dụng các file cấu hình khác n<PERSON>u cho các môi trường khác nhau

## Cấu trúc file

### File `postgresql_config.yaml` (thư mục gốc)

```yaml
postgresql:
  # Enable/disable PostgreSQL functionality
  enabled: true
  
  # Database connection settings
  host: "localhost"                               # PostgreSQL host
  port: 5432                                      # PostgreSQL port
  database: "sentiment_analysis"                  # Database name
  username: "postgres"                            # Username
  password: "password"                            # Password
  
  # Table configuration
  table_name: "daily_sentiment_reports"           # Table name for daily reports
  
  # Upload scheduling
  upload_time: "09:00"                           # Time to upload data (9:00 AM daily)
  
  # Error handling and retry configuration
  retry_attempts: 3                              # Number of retry attempts on failure
  retry_delay_seconds: 60                        # Delay between retry attempts (seconds)
  
  # Connection settings
  connect_timeout: 10                            # Connection timeout in seconds
  
  # Backup and recovery settings
  backup_enabled: true                           # Enable local JSON backup before upload
  backup_path: "data/postgresql_backups"         # Path to store backup files
```

### File `config/config.yaml` (cấu hình chính)

```yaml
daily_sentiment:
  enabled: true
  processing_intervals: [30, 60, 180, 360]
  daily_report:
    start_time: "00:00"
    end_time: "23:59"
    timezone: "Asia/Ho_Chi_Minh"
  # PostgreSQL configuration is now stored in postgresql_config.yaml in the root directory
```

## Cách sử dụng

### 1. Thiết lập cấu hình

1. Copy file `postgresql_config.yaml` mẫu (nếu chưa có)
2. Chỉnh sửa thông tin kết nối database:
   ```yaml
   postgresql:
     enabled: true
     host: "your-postgres-host"
     port: 5432
     database: "your-database-name"
     username: "your-username"
     password: "your-password"
   ```

### 2. Kiểm tra cấu hình

```bash
# Test load cấu hình PostgreSQL
python -c "from app.core.config import Config; config = Config(); print(config.get_postgresql_config())"

# Test merge với daily sentiment config
python -c "from app.core.config import Config; config = Config(); print(config.get_daily_sentiment_config()['postgresql'])"
```

### 3. Khởi động hệ thống

Hệ thống sẽ tự động:
1. Load cấu hình từ `postgresql_config.yaml`
2. Merge vào daily sentiment configuration
3. Khởi tạo PostgreSQL service với cấu hình mới

## Tùy chọn cấu hình

### Connection Settings
- `host`: Địa chỉ PostgreSQL server
- `port`: Port kết nối (mặc định: 5432)
- `database`: Tên database
- `username`: Tên đăng nhập
- `password`: Mật khẩu
- `connect_timeout`: Timeout kết nối (giây)

### Upload Settings
- `upload_time`: Thời gian upload hàng ngày (format: "HH:MM")
- `table_name`: Tên bảng lưu trữ dữ liệu

### Error Handling
- `retry_attempts`: Số lần thử lại khi lỗi
- `retry_delay_seconds`: Thời gian chờ giữa các lần thử lại

### Backup Settings
- `backup_enabled`: Bật/tắt backup local trước khi upload
- `backup_path`: Đường dẫn lưu file backup

## Bảo mật

### Khuyến nghị bảo mật

1. **File permissions**: Đặt quyền truy cập hạn chế cho `postgresql_config.yaml`
   ```bash
   chmod 600 postgresql_config.yaml
   ```

2. **Environment variables**: Có thể override bằng biến môi trường
   ```bash
   export POSTGRESQL_PASSWORD="your-secure-password"
   ```

3. **Gitignore**: Thêm vào `.gitignore` nếu chứa thông tin nhạy cảm
   ```
   postgresql_config.yaml
   ```

### Sử dụng Environment Variables

Hệ thống hỗ trợ override cấu hình bằng environment variables:

```bash
export POSTGRESQL_HOST="production-db-host"
export POSTGRESQL_PASSWORD="secure-password"
export POSTGRESQL_DATABASE="production_sentiment_db"
```

## Troubleshooting

### Lỗi thường gặp

1. **File not found**: 
   ```
   PostgreSQL config file not found: postgresql_config.yaml
   ```
   - Tạo file `postgresql_config.yaml` trong thư mục gốc

2. **Connection failed**:
   ```
   Failed to connect to PostgreSQL: connection refused
   ```
   - Kiểm tra host, port, username, password
   - Đảm bảo PostgreSQL server đang chạy

3. **Permission denied**:
   ```
   FATAL: password authentication failed
   ```
   - Kiểm tra username/password
   - Kiểm tra pg_hba.conf settings

### Debug mode

Bật debug logging để xem chi tiết:

```yaml
# Trong config/config.yaml
server:
  debug: true
```

## Migration từ cấu hình cũ

Nếu bạn đang sử dụng cấu hình PostgreSQL trong `config/config.yaml`, hãy:

1. Copy phần cấu hình PostgreSQL sang `postgresql_config.yaml`
2. Xóa phần cấu hình PostgreSQL khỏi `config/config.yaml`
3. Restart ứng dụng

Hệ thống sẽ tự động sử dụng cấu hình mới.

"""
MongoDB Service
--------------
Service for uploading sentiment analysis data to MongoDB
"""

import json
import os
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading

try:
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, DuplicateKeyError
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    MongoClient = None

from app.models.schemas import NightlyReport

logger = logging.getLogger(__name__)


class MongoDBService:
    """
    Service for uploading sentiment analysis data to MongoDB.
    Handles connection management, data upload, and error recovery.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize MongoDB service.

        Args:
            config: MongoDB configuration from config.yaml
        """
        self.config = config
        self.enabled = config.get('enabled', False)
        
        if not PYMONGO_AVAILABLE:
            logger.warning("PyMongo not available. MongoDB functionality disabled.")
            self.enabled = False
            return
        
        if not self.enabled:
            logger.info("MongoDB service disabled in configuration")
            return
            
        # MongoDB settings
        self.connection_string = config.get('connection_string', 'mongodb://localhost:27017/')
        self.database_name = config.get('database_name', 'sentiment_analysis')
        self.collection_name = config.get('collection_name', 'daily_reports')
        self.retry_attempts = config.get('retry_attempts', 3)
        self.retry_delay = config.get('retry_delay_seconds', 60)
        
        # Connection management
        self.client = None
        self.database = None
        self.collection = None
        self._lock = threading.Lock()
        
        # Initialize connection
        self._connect()
        
        logger.info(f"MongoDB service initialized: {self.database_name}.{self.collection_name}")

    def _connect(self):
        """Establish connection to MongoDB"""
        if not self.enabled:
            return False
            
        try:
            with self._lock:
                # Close existing connection if any
                if self.client:
                    self.client.close()
                
                # Create new connection
                self.client = MongoClient(
                    self.connection_string,
                    serverSelectionTimeoutMS=5000,  # 5 second timeout
                    connectTimeoutMS=5000,
                    socketTimeoutMS=5000
                )
                
                # Test connection
                self.client.admin.command('ping')
                
                # Get database and collection
                self.database = self.client[self.database_name]
                self.collection = self.database[self.collection_name]
                
                # Create index on report_date for efficient queries
                self.collection.create_index("report_date", unique=True)
                
                logger.info(f"Successfully connected to MongoDB: {self.connection_string}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self.client = None
            self.database = None
            self.collection = None
            return False

    def _is_connected(self) -> bool:
        """Check if MongoDB connection is active"""
        if not self.enabled or not self.client:
            return False
            
        try:
            self.client.admin.command('ping')
            return True
        except Exception:
            return False

    def upload_nightly_report(self, report: NightlyReport, backup_locally: bool = True) -> bool:
        """
        Upload nightly report to MongoDB.

        Args:
            report: NightlyReport object to upload
            backup_locally: Whether to save a local backup

        Returns:
            True if upload successful, False otherwise
        """
        if not self.enabled:
            logger.debug("MongoDB upload skipped - service disabled")
            return False

        # Save local backup if requested
        if backup_locally:
            self._save_local_backup(report)

        # Attempt upload with retries
        for attempt in range(self.retry_attempts):
            try:
                # Ensure connection
                if not self._is_connected():
                    logger.info(f"Reconnecting to MongoDB (attempt {attempt + 1})")
                    if not self._connect():
                        continue

                # Prepare document
                document = self._prepare_document(report)
                
                # Upload to MongoDB
                with self._lock:
                    result = self.collection.replace_one(
                        {"report_date": report.report_date},
                        document,
                        upsert=True
                    )
                
                if result.acknowledged:
                    action = "updated" if result.matched_count > 0 else "inserted"
                    logger.info(f"Successfully {action} report for {report.report_date} in MongoDB")
                    return True
                else:
                    logger.warning(f"MongoDB operation not acknowledged for {report.report_date}")
                    
            except DuplicateKeyError:
                logger.info(f"Report for {report.report_date} already exists, updating...")
                continue
            except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                logger.warning(f"MongoDB connection error (attempt {attempt + 1}): {e}")
                if attempt < self.retry_attempts - 1:
                    time.sleep(self.retry_delay)
                continue
            except Exception as e:
                logger.error(f"Unexpected error uploading to MongoDB (attempt {attempt + 1}): {e}")
                if attempt < self.retry_attempts - 1:
                    time.sleep(self.retry_delay)
                continue

        logger.error(f"Failed to upload report for {report.report_date} after {self.retry_attempts} attempts")
        return False

    def _prepare_document(self, report: NightlyReport) -> Dict[str, Any]:
        """
        Prepare report data for MongoDB storage.

        Args:
            report: NightlyReport object

        Returns:
            Dictionary ready for MongoDB insertion
        """
        # Convert to dict using model_dump instead of deprecated dict()
        if hasattr(report, 'model_dump'):
            document = report.model_dump()
        else:
            # Fallback for older pydantic versions
            document = report.dict()
        
        # Add metadata
        document['uploaded_at'] = datetime.utcnow()
        document['upload_source'] = 'periodic_sentiment_system'
        
        # Ensure report_date is properly formatted
        document['report_date'] = report.report_date
        
        return document

    def _save_local_backup(self, report: NightlyReport):
        """
        Save local backup of the report.

        Args:
            report: NightlyReport object to backup
        """
        try:
            # Get backup path from config
            backup_path = self.config.get('backup_reports_path', 'data/backup_reports/')
            os.makedirs(backup_path, exist_ok=True)
            
            # Create backup file
            backup_file = os.path.join(backup_path, f"backup_report_{report.report_date}.json")
            
            # Save backup
            if hasattr(report, 'model_dump'):
                data = report.model_dump()
            else:
                data = report.dict()
                
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Local backup saved: {backup_file}")
            
        except Exception as e:
            logger.warning(f"Failed to save local backup: {e}")

    def get_report(self, report_date: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a report from MongoDB by date.

        Args:
            report_date: Report date in YYYY-MM-DD format

        Returns:
            Report document or None if not found
        """
        if not self.enabled or not self._is_connected():
            return None

        try:
            with self._lock:
                document = self.collection.find_one({"report_date": report_date})
            
            if document:
                # Remove MongoDB's _id field
                document.pop('_id', None)
                return document
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving report for {report_date}: {e}")
            return None

    def list_available_reports(self, limit: int = 100) -> List[str]:
        """
        List available report dates in MongoDB.

        Args:
            limit: Maximum number of reports to return

        Returns:
            List of report dates in YYYY-MM-DD format
        """
        if not self.enabled or not self._is_connected():
            return []

        try:
            with self._lock:
                cursor = self.collection.find(
                    {},
                    {"report_date": 1, "_id": 0}
                ).sort("report_date", -1).limit(limit)
                
                return [doc["report_date"] for doc in cursor]
                
        except Exception as e:
            logger.error(f"Error listing available reports: {e}")
            return []

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get MongoDB connection status and statistics.

        Returns:
            Dictionary with connection status information
        """
        status = {
            "enabled": self.enabled,
            "connected": False,
            "database": self.database_name,
            "collection": self.collection_name,
            "connection_string": self.connection_string.replace(
                self.connection_string.split('@')[-1] if '@' in self.connection_string else '',
                '***'
            ) if '@' in self.connection_string else self.connection_string
        }

        if not self.enabled:
            status["message"] = "MongoDB service disabled"
            return status

        if not PYMONGO_AVAILABLE:
            status["message"] = "PyMongo not available"
            return status

        try:
            if self._is_connected():
                status["connected"] = True
                
                # Get collection stats
                with self._lock:
                    stats = self.database.command("collStats", self.collection_name)
                    status["document_count"] = stats.get("count", 0)
                    status["storage_size"] = stats.get("storageSize", 0)
                    
                status["message"] = "Connected and operational"
            else:
                status["message"] = "Connection failed"
                
        except Exception as e:
            status["message"] = f"Error checking status: {e}"

        return status

    def test_connection(self) -> bool:
        """
        Test MongoDB connection.

        Returns:
            True if connection successful, False otherwise
        """
        if not self.enabled:
            logger.info("MongoDB service disabled")
            return False

        if not PYMONGO_AVAILABLE:
            logger.error("PyMongo not available")
            return False

        try:
            # Test connection
            if self._connect():
                logger.info("MongoDB connection test successful")
                return True
            else:
                logger.error("MongoDB connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"MongoDB connection test error: {e}")
            return False

    def close(self):
        """Close MongoDB connection"""
        if self.client:
            with self._lock:
                self.client.close()
                self.client = None
                self.database = None
                self.collection = None
            logger.info("MongoDB connection closed")

    def __del__(self):
        """Cleanup on object destruction"""
        self.close()

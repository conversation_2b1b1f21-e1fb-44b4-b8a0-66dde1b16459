#!/bin/bash

# Staging Deployment Script
# Usage: ./deploy-staging.sh <docker-image-tag>

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.staging.yml"
ENV_FILE="$PROJECT_DIR/.env.staging"
BACKUP_DIR="$PROJECT_DIR/backups/staging"
LOG_FILE="$PROJECT_DIR/logs/deploy-staging.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if image tag is provided
if [ $# -eq 0 ]; then
    error "Usage: $0 <docker-image-tag>"
fi

IMAGE_TAG="$1"

log "Starting staging deployment with image: $IMAGE_TAG"

# Create necessary directories
mkdir -p "$BACKUP_DIR" "$(dirname "$LOG_FILE")"

# Check if required files exist
if [ ! -f "$COMPOSE_FILE" ]; then
    error "Docker compose file not found: $COMPOSE_FILE"
fi

if [ ! -f "$ENV_FILE" ]; then
    warning "Environment file not found: $ENV_FILE. Using default values."
fi

# Load environment variables
if [ -f "$ENV_FILE" ]; then
    set -a
    source "$ENV_FILE"
    set +a
    log "Loaded environment variables from $ENV_FILE"
fi

# Pre-deployment checks
log "Running pre-deployment checks..."

# Check Docker
if ! command -v docker &> /dev/null; then
    error "Docker is not installed or not in PATH"
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    error "Docker Compose is not installed or not in PATH"
fi

# Check if services are running
if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
    log "Existing services detected. Preparing for rolling update..."
    ROLLING_UPDATE=true
else
    log "No existing services detected. Performing fresh deployment..."
    ROLLING_UPDATE=false
fi

# Backup current state
if [ "$ROLLING_UPDATE" = true ]; then
    log "Creating backup of current deployment..."
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/staging_backup_$BACKUP_TIMESTAMP"
    
    mkdir -p "$BACKUP_PATH"
    
    # Backup database
    log "Backing up PostgreSQL database..."
    docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U postgres sentiment_analysis > "$BACKUP_PATH/database_backup.sql" || warning "Database backup failed"
    
    # Backup application data
    log "Backing up application data..."
    cp -r "$PROJECT_DIR/data" "$BACKUP_PATH/" 2>/dev/null || warning "Data backup failed"
    
    # Backup configuration
    cp "$PROJECT_DIR/config/"* "$BACKUP_PATH/" 2>/dev/null || warning "Config backup failed"
    cp "$PROJECT_DIR/postgresql_config.yaml" "$BACKUP_PATH/" 2>/dev/null || warning "PostgreSQL config backup failed"
    
    success "Backup completed: $BACKUP_PATH"
fi

# Pull new image
log "Pulling new Docker image: $IMAGE_TAG"
docker pull "$IMAGE_TAG" || error "Failed to pull Docker image"

# Update docker-compose file with new image
log "Updating docker-compose file with new image tag..."
sed -i.bak "s|image: .*sentiment-analysis.*|image: $IMAGE_TAG|g" "$COMPOSE_FILE"

# Deploy new version
log "Deploying new version..."

if [ "$ROLLING_UPDATE" = true ]; then
    # Rolling update strategy
    log "Performing rolling update..."
    
    # Scale up new instance
    docker-compose -f "$COMPOSE_FILE" up -d --scale sentiment-api=2 --no-recreate
    
    # Wait for new instance to be healthy
    log "Waiting for new instance to be healthy..."
    sleep 30
    
    # Check health
    for i in {1..10}; do
        if curl -f http://localhost:8000/health &>/dev/null; then
            success "New instance is healthy"
            break
        fi
        if [ $i -eq 10 ]; then
            error "New instance failed health check"
        fi
        log "Health check attempt $i/10 failed, retrying in 10 seconds..."
        sleep 10
    done
    
    # Scale down old instance
    log "Scaling down old instance..."
    docker-compose -f "$COMPOSE_FILE" up -d --scale sentiment-api=1
    
else
    # Fresh deployment
    log "Starting fresh deployment..."
    docker-compose -f "$COMPOSE_FILE" up -d
fi

# Wait for services to be ready
log "Waiting for all services to be ready..."
sleep 45

# Health checks
log "Running health checks..."

# Check main application
if curl -f http://localhost:8000/health &>/dev/null; then
    success "Main application health check passed"
else
    error "Main application health check failed"
fi

# Check database connectivity
if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U postgres &>/dev/null; then
    success "Database health check passed"
else
    error "Database health check failed"
fi

# Run smoke tests
log "Running smoke tests..."
cd "$PROJECT_DIR"

# Test API endpoints
curl -f http://localhost:8000/api/health || error "API health endpoint failed"

# Test sentiment analysis endpoint (if available)
# Add more specific tests here

success "Smoke tests passed"

# Cleanup old images
log "Cleaning up old Docker images..."
docker image prune -f || warning "Failed to cleanup old images"

# Update deployment status
echo "$IMAGE_TAG" > "$PROJECT_DIR/.current-staging-version"
echo "$(date)" > "$PROJECT_DIR/.last-staging-deployment"

success "Staging deployment completed successfully!"
log "Deployed version: $IMAGE_TAG"
log "Deployment time: $(date)"

# Send notification (if configured)
if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚀 Staging deployment successful! Version: $IMAGE_TAG\"}" \
        "$SLACK_WEBHOOK_URL" || warning "Failed to send Slack notification"
fi

log "Staging deployment script completed"

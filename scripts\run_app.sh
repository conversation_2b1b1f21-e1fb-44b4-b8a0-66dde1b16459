#!/bin/bash
# Shell script to run the Sentiment Analysis API System

# Check if Conda is installed
if ! command -v conda &>/dev/null; then
    echo "Conda is not installed. Please install Miniconda or Anaconda."
    echo "Visit https://docs.conda.io/en/latest/miniconda.html for installation instructions."
    exit 1
fi

# Check if the environment exists
if ! conda env list | grep -q "sentiment-api"; then
    echo "Conda environment 'sentiment-api' does not exist."
    echo "Please run the installation script first: bash scripts/install_dependencies.sh"
    exit 1
fi

# Activate Conda environment
echo "Activating Conda environment..."
eval "$(conda shell.bash hook)"
conda activate sentiment-api
echo "Conda environment activated."

# Create logs directory if it doesn't exist
mkdir -p logs

# Run the application
echo "Starting the application..."

# Check if ngrok is needed
if [ "$1" == "--ngrok" ]; then
    echo "Starting with ngrok support..."
    python run.py
else
    # Run directly with uvicorn
    echo "Starting with uvicorn..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
fi

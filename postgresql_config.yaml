# PostgreSQL Configuration for Sentiment Analysis System
# This file contains all PostgreSQL-related settings

postgresql:
  # Enable/disable PostgreSQL functionality
  enabled: true
  
  # Database connection settings
  host: "localhost"                               # PostgreSQL host
  port: 5432                                      # PostgreSQL port
  database: "sentiment_analysis"                  # Database name
  username: "postgres"                            # Username
  password: "password"                            # Password
  
  # Table configuration
  table_name: "daily_sentiment_reports"           # Table name for daily reports
  
  # Upload scheduling
  upload_time: "09:00"                           # Time to upload data (9:00 AM daily)
  
  # Error handling and retry configuration
  retry_attempts: 3                              # Number of retry attempts on failure
  retry_delay_seconds: 60                        # Delay between retry attempts (seconds)
  
  # Connection settings
  connect_timeout: 10                            # Connection timeout in seconds
  
  # Backup and recovery settings
  backup_enabled: true                           # Enable local JSON backup before upload
  backup_path: "data/postgresql_backups"         # Path to store backup files

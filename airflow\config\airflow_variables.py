"""
Airflow Variables Configuration
Script to setup variables for Sentiment Analysis integration
"""

from airflow.models import Variable
import logging

logger = logging.getLogger(__name__)

def setup_variables():
    """Setup all required Airflow variables"""
    
    variables = {
        # API Configuration
        'SENTIMENT_API_BASE_URL': 'http://your-sentiment-server.com:8000',
        'AIRFLOW_API_TOKEN': 'airflow-secret-token-change-this',
        
        # Email Configuration
        'ALERT_EMAIL': '<EMAIL>',
        'NOTIFICATION_EMAIL': '<EMAIL>',
        
        # Processing Configuration
        'HIGH_VOLUME_WEBSITES': 'website1,website2,website3',
        'BATCH_SIZE': '200',
        'MAX_RETRY_ATTEMPTS': '3',
        'TASK_TIMEOUT': '3600',
        
        # Monitoring Configuration
        'HEALTH_CHECK_INTERVAL': '300',  # 5 minutes
        'ALERT_THRESHOLD_CPU': '80',
        'ALERT_THRESHOLD_MEMORY': '85',
        'ALERT_THRESHOLD_DISK': '90',
        'ALERT_THRESHOLD_FAILED_TASKS': '5',
        
        # Cleanup Configuration
        'DATA_RETENTION_DAYS': '30',
        'LOG_RETENTION_DAYS': '7',
        'CLEANUP_ENABLED': 'true',
        
        # Environment Configuration
        'ENVIRONMENT': 'production',  # or 'staging', 'development'
        'DEBUG_MODE': 'false',
        
        # Slack Configuration (optional)
        'SLACK_WEBHOOK_URL': '',
        'SLACK_CHANNEL': '#sentiment-analysis-alerts',
        
        # Database Configuration
        'POSTGRES_BACKUP_ENABLED': 'true',
        'POSTGRES_BACKUP_RETENTION_DAYS': '7',
        
        # Performance Configuration
        'PARALLEL_TASKS': '5',
        'WORKER_CONCURRENCY': '4',
        
        # Security Configuration
        'API_RATE_LIMIT': '100',
        'AUTH_TOKEN_EXPIRY': '3600'
    }
    
    logger.info("Setting up Airflow variables...")
    
    for key, value in variables.items():
        try:
            # Check if variable already exists
            existing_value = Variable.get(key, default_var=None)
            
            if existing_value is not None:
                logger.info(f"Variable {key} already exists with value: {existing_value}")
                # Optionally update the value
                # Variable.set(key, value)
                # logger.info(f"Updated variable {key} to: {value}")
            else:
                Variable.set(key, value)
                logger.info(f"Created variable {key} with value: {value}")
                
        except Exception as e:
            logger.error(f"Failed to set variable {key}: {e}")
    
    logger.info("Airflow variables setup completed")

def get_variable_descriptions():
    """Get descriptions for all variables"""
    return {
        'SENTIMENT_API_BASE_URL': 'Base URL for the Sentiment Analysis API server',
        'AIRFLOW_API_TOKEN': 'Authentication token for Airflow to access the API',
        'ALERT_EMAIL': 'Email address for critical alerts',
        'NOTIFICATION_EMAIL': 'Email address for general notifications',
        'HIGH_VOLUME_WEBSITES': 'Comma-separated list of high-volume websites for batch processing',
        'BATCH_SIZE': 'Number of items to process in each batch',
        'MAX_RETRY_ATTEMPTS': 'Maximum number of retry attempts for failed tasks',
        'TASK_TIMEOUT': 'Task timeout in seconds',
        'HEALTH_CHECK_INTERVAL': 'Health check interval in seconds',
        'ALERT_THRESHOLD_CPU': 'CPU usage threshold for alerts (%)',
        'ALERT_THRESHOLD_MEMORY': 'Memory usage threshold for alerts (%)',
        'ALERT_THRESHOLD_DISK': 'Disk usage threshold for alerts (%)',
        'ALERT_THRESHOLD_FAILED_TASKS': 'Number of failed tasks threshold for alerts',
        'DATA_RETENTION_DAYS': 'Number of days to retain data',
        'LOG_RETENTION_DAYS': 'Number of days to retain log files',
        'CLEANUP_ENABLED': 'Whether automatic cleanup is enabled',
        'ENVIRONMENT': 'Current environment (production, staging, development)',
        'DEBUG_MODE': 'Whether debug mode is enabled',
        'SLACK_WEBHOOK_URL': 'Slack webhook URL for notifications',
        'SLACK_CHANNEL': 'Slack channel for notifications',
        'POSTGRES_BACKUP_ENABLED': 'Whether PostgreSQL backup is enabled',
        'POSTGRES_BACKUP_RETENTION_DAYS': 'Number of days to retain database backups',
        'PARALLEL_TASKS': 'Number of parallel tasks to run',
        'WORKER_CONCURRENCY': 'Worker concurrency level',
        'API_RATE_LIMIT': 'API rate limit per minute',
        'AUTH_TOKEN_EXPIRY': 'Authentication token expiry time in seconds'
    }

def print_variable_documentation():
    """Print documentation for all variables"""
    descriptions = get_variable_descriptions()
    
    print("Airflow Variables Documentation")
    print("=" * 50)
    print()
    
    for var_name, description in descriptions.items():
        current_value = Variable.get(var_name, default_var="Not set")
        print(f"Variable: {var_name}")
        print(f"Description: {description}")
        print(f"Current Value: {current_value}")
        print("-" * 30)

if __name__ == "__main__":
    setup_variables()
    print("\n")
    print_variable_documentation()

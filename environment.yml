name: sentiment-api
channels:
  - conda-forge
  - defaults
  - pytorch
dependencies:
  - python=3.10
  - pip>=23.0.0
  - pytorch>=2.0.0
  - pip:
    # Web Framework
    - fastapi>=0.115.0,<0.116.0
    - uvicorn>=0.29.0,<0.30.0
    - pydantic>=2.0.0,<3.0.0

    # Utilities
    - python-dotenv>=1.0.0,<2.0.0
    - requests>=2.31.0,<3.0.0
    - pyngrok>=6.0.0,<7.0.0
    - pyyaml>=6.0.0,<7.0.0
    - psutil>=5.9.0,<6.0.0  # For system resource monitoring
    - filelock>=3.18.0,<4.0.0

    # Machine Learning
    - transformers>=4.51.0,<5.0.0
    - accelerate>=1.6.0,<2.0.0
    - huggingface-hub>=0.30.0,<0.31.0

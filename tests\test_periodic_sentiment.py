"""
Test cases for periodic sentiment analysis system
"""

import unittest
import tempfile
import os
import json
import time
from unittest.mock import Mock, patch

from app.services.conversation_tracker import ConversationTracker
from app.services.periodic_sentiment_processor import PeriodicSentimentProcessor
from app.services.nightly_report_generator import NightlyReportGenerator
from app.models.schemas import ConversationTrackingInfo


class TestConversationTracker(unittest.TestCase):
    """Test cases for ConversationTracker"""

    def setUp(self):
        """Set up test environment"""
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        self.temp_file.close()
        self.tracker = ConversationTracker(self.temp_file.name)

    def tearDown(self):
        """Clean up test environment"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def test_update_conversation(self):
        """Test updating conversation information"""
        website_id = "test_website"
        session_id = "test_session"
        timestamp = int(time.time())
        message_count = 5

        # Update conversation
        self.tracker.update_conversation(
            website_id=website_id,
            session_id=session_id,
            last_message_timestamp=timestamp,
            message_count=message_count
        )

        # Verify tracking info
        tracking_info = self.tracker.get_tracking_info(website_id, session_id)
        self.assertIsNotNone(tracking_info)
        self.assertEqual(tracking_info.website_id, website_id)
        self.assertEqual(tracking_info.session_id, session_id)
        self.assertEqual(tracking_info.last_message_timestamp, timestamp)
        self.assertEqual(tracking_info.message_count, message_count)
        self.assertFalse(tracking_info.is_completed)

    def test_update_sentiment_analysis(self):
        """Test updating sentiment analysis timestamp"""
        website_id = "test_website"
        session_id = "test_session"
        timestamp = int(time.time())
        analysis_timestamp = timestamp + 100

        # First update conversation
        self.tracker.update_conversation(
            website_id=website_id,
            session_id=session_id,
            last_message_timestamp=timestamp,
            message_count=3
        )

        # Then update sentiment analysis
        self.tracker.update_sentiment_analysis(
            website_id=website_id,
            session_id=session_id,
            analysis_timestamp=analysis_timestamp
        )

        # Verify sentiment analysis timestamp
        tracking_info = self.tracker.get_tracking_info(website_id, session_id)
        self.assertEqual(tracking_info.last_sentiment_analysis_timestamp, analysis_timestamp)

    def test_get_conversations_for_periodic_analysis(self):
        """Test getting conversations that need periodic analysis"""
        current_time = int(time.time())
        
        # Add conversation that needs analysis (new messages)
        self.tracker.update_conversation(
            website_id="website1",
            session_id="session1",
            last_message_timestamp=current_time,
            message_count=5
        )

        # Add conversation that doesn't need analysis (already analyzed)
        self.tracker.update_conversation(
            website_id="website2",
            session_id="session2",
            last_message_timestamp=current_time - 100,
            message_count=3
        )
        self.tracker.update_sentiment_analysis(
            website_id="website2",
            session_id="session2",
            analysis_timestamp=current_time
        )

        # Add conversation with too few messages
        self.tracker.update_conversation(
            website_id="website3",
            session_id="session3",
            last_message_timestamp=current_time,
            message_count=1
        )

        # Get conversations for analysis
        conversations = self.tracker.get_conversations_for_periodic_analysis(
            max_age_hours=24,
            min_messages=2
        )

        # Should only return the first conversation
        self.assertEqual(len(conversations), 1)
        self.assertEqual(conversations[0].session_id, "session1")

    def test_mark_conversation_completed(self):
        """Test marking conversation as completed"""
        website_id = "test_website"
        session_id = "test_session"
        timestamp = int(time.time())

        # Update conversation first
        self.tracker.update_conversation(
            website_id=website_id,
            session_id=session_id,
            last_message_timestamp=timestamp,
            message_count=5
        )

        # Mark as completed
        self.tracker.mark_conversation_completed(website_id, session_id)

        # Verify completion
        tracking_info = self.tracker.get_tracking_info(website_id, session_id)
        self.assertTrue(tracking_info.is_completed)
        self.assertIsNotNone(tracking_info.completion_timestamp)

    def test_get_statistics(self):
        """Test getting conversation statistics"""
        current_time = int(time.time())

        # Add some test conversations
        self.tracker.update_conversation("w1", "s1", current_time, 5, is_completed=True)
        self.tracker.update_conversation("w1", "s2", current_time, 3, is_completed=False)
        self.tracker.update_sentiment_analysis("w1", "s1", current_time)

        stats = self.tracker.get_statistics()
        
        self.assertEqual(stats["total_conversations"], 2)
        self.assertEqual(stats["completed_conversations"], 1)
        self.assertEqual(stats["ongoing_conversations"], 1)
        self.assertEqual(stats["analyzed_conversations"], 1)
        self.assertEqual(stats["unanalyzed_conversations"], 1)


class TestPeriodicSentimentProcessor(unittest.TestCase):
    """Test cases for PeriodicSentimentProcessor"""

    def setUp(self):
        """Set up test environment"""
        self.config = {
            'storage': {
                'periodic_results_path': tempfile.NamedTemporaryFile(delete=False, suffix='.json').name,
                'conversation_tracking_path': tempfile.NamedTemporaryFile(delete=False, suffix='.json').name
            },
            'processing': {
                'min_messages_for_analysis': 2,
                'max_conversation_age_hours': 168,
                'batch_size': 10,
                'conversation_timeout_sec': 30
            }
        }
        
        # Mock sentiment service and database
        self.mock_sentiment_service = Mock()
        self.mock_database = Mock()
        
        self.processor = PeriodicSentimentProcessor(
            self.config, 
            self.mock_sentiment_service, 
            self.mock_database
        )

    def tearDown(self):
        """Clean up test environment"""
        for path in [self.config['storage']['periodic_results_path'],
                     self.config['storage']['conversation_tracking_path']]:
            if os.path.exists(path):
                os.unlink(path)

    def test_initialization(self):
        """Test processor initialization"""
        self.assertIsNotNone(self.processor.conversation_tracker)
        self.assertEqual(self.processor.min_messages, 2)
        self.assertEqual(self.processor.max_age_hours, 168)
        self.assertEqual(self.processor.batch_size, 10)

    def test_get_processing_status(self):
        """Test getting processing status"""
        status = self.processor.get_processing_status()
        
        self.assertIn("is_running", status)
        self.assertIn("current_processing_id", status)
        self.assertIn("last_processing_timestamp", status)
        self.assertIn("total_processing_runs", status)
        
        self.assertFalse(status["is_running"])
        self.assertIsNone(status["current_processing_id"])

    def test_get_latest_results(self):
        """Test getting latest results"""
        results = self.processor.get_latest_results(limit=5)
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 0)  # No results initially


class TestNightlyReportGenerator(unittest.TestCase):
    """Test cases for NightlyReportGenerator"""

    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'nightly_report': {
                'start_nightly_time': '18:00',
                'end_nightly_time': '06:00',
                'timezone': 'UTC'
            },
            'storage': {
                'nightly_reports_path': self.temp_dir
            },
            'processing': {
                'min_messages_for_analysis': 2
            }
        }
        
        # Mock periodic processor and database
        self.mock_processor = Mock()
        self.mock_database = Mock()
        
        self.generator = NightlyReportGenerator(
            self.config,
            self.mock_processor,
            self.mock_database
        )

    def tearDown(self):
        """Clean up test environment"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_initialization(self):
        """Test generator initialization"""
        self.assertEqual(self.generator.start_nightly_time, '18:00')
        self.assertEqual(self.generator.end_nightly_time, '06:00')
        self.assertEqual(self.generator.timezone_str, 'UTC')
        self.assertEqual(self.generator.reports_dir, self.temp_dir)

    def test_parse_time_string(self):
        """Test parsing time strings"""
        hour, minute = self.generator._parse_time_string('18:30')
        self.assertEqual(hour, 18)
        self.assertEqual(minute, 30)

        # Test invalid format
        hour, minute = self.generator._parse_time_string('invalid')
        self.assertEqual(hour, 18)  # Default
        self.assertEqual(minute, 0)  # Default

    def test_list_available_reports(self):
        """Test listing available reports"""
        # Initially no reports
        reports = self.generator.list_available_reports()
        self.assertEqual(len(reports), 0)

        # Create a test report file
        test_file = os.path.join(self.temp_dir, 'nightly_report_2025-01-15.json')
        with open(test_file, 'w') as f:
            json.dump({'test': 'data'}, f)

        reports = self.generator.list_available_reports()
        self.assertEqual(len(reports), 1)
        self.assertEqual(reports[0], '2025-01-15')

    def test_get_generation_status(self):
        """Test getting generation status"""
        status = self.generator.get_generation_status()
        
        self.assertIn("is_generating", status)
        self.assertIn("available_reports", status)
        
        self.assertFalse(status["is_generating"])


if __name__ == '__main__':
    unittest.main()

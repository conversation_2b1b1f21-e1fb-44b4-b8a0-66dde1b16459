-- PostgreSQL Schema Migration Script
-- Cập nhật bảng daily_sentiment_reports với các trường mới

-- Backup bảng hiện tại (tù<PERSON> chọn)
-- CREATE TABLE daily_sentiment_reports_backup AS SELECT * FROM daily_sentiment_reports;

-- Thê<PERSON> các cột mới vào bảng hiện tại
ALTER TABLE daily_sentiment_reports 
ADD COLUMN IF NOT EXISTS conversation_start BIGINT,
ADD COLUMN IF NOT EXISTS conversation_end BIGINT,
ADD COLUMN IF NOT EXISTS is_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS message_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS analysis_timestamp BIGINT,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Cập nhật dữ liệu hiện tại với giá trị mặc định
UPDATE daily_sentiment_reports 
SET 
    is_completed = FALSE,
    message_count = 0,
    updated_at = CURRENT_TIMESTAMP
WHERE is_completed IS NULL;

-- T<PERSON><PERSON> các index mới để tối ưu hiệu suất
CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_website 
ON daily_sentiment_reports (website_id);

CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_session 
ON daily_sentiment_reports (session_id);

CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_agent 
ON daily_sentiment_reports (agent_name);

CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_composite 
ON daily_sentiment_reports (report_date, website_id, session_id);

CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_conversation_start 
ON daily_sentiment_reports (conversation_start);

CREATE INDEX IF NOT EXISTS idx_daily_sentiment_reports_is_completed 
ON daily_sentiment_reports (is_completed);

-- Tạo function để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Tạo trigger để tự động cập nhật updated_at
DROP TRIGGER IF EXISTS update_daily_sentiment_reports_updated_at ON daily_sentiment_reports;
CREATE TRIGGER update_daily_sentiment_reports_updated_at 
    BEFORE UPDATE ON daily_sentiment_reports 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Kiểm tra cấu trúc bảng sau khi migration
\d daily_sentiment_reports;

-- Hiển thị thống kê bảng
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT report_date) as unique_dates,
    COUNT(DISTINCT website_id) as unique_websites,
    COUNT(DISTINCT session_id) as unique_sessions,
    COUNT(DISTINCT agent_name) as unique_agents,
    MIN(report_date) as earliest_date,
    MAX(report_date) as latest_date
FROM daily_sentiment_reports;

-- Hiển thị sample data với các cột mới
SELECT 
    id,
    report_date,
    website_id,
    session_id,
    agent_name,
    conversation_start,
    conversation_end,
    is_completed,
    message_count,
    analysis_timestamp,
    created_at,
    updated_at
FROM daily_sentiment_reports 
ORDER BY created_at DESC 
LIMIT 5;

# PostgreSQL Migration Summary

## Những thay đổi đã thực hiện

### 1. **C<PERSON><PERSON> nhật c<PERSON>u hình (config.yaml)**

**<PERSON><PERSON><PERSON><PERSON><PERSON> (MongoDB):**
```yaml
mongodb:
  enabled: true
  connection_string: "mongodb://localhost:27017/"
  database_name: "sentiment_analysis"
  collection_name: "daily_reports"
  upload_time: "09:00"
```

**Sau (PostgreSQL):**
```yaml
postgresql:
  enabled: true
  host: "localhost"
  port: 5432
  database: "sentiment_analysis"
  username: "postgres"
  password: "password"
  table_name: "daily_sentiment_reports"
  upload_time: "09:00"
```

### 2. **Cập nhật dependencies (requirements.txt)**

```diff
- pymongo==4.6.1  # For MongoDB integration
+ psycopg2-binary==2.9.9  # For PostgreSQL integration
```

### 3. **Tạo PostgreSQL Service mới**

**File:** `app/services/postgresql_service.py`

**Tính năng:**
- Connection management với retry mechanism
- Tự động tạo table và index
- Upload dữ liệu với upsert logic
- Backup và recovery
- Connection status monitoring

**Database Schema:**
```sql
CREATE TABLE daily_sentiment_reports (
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    website_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    user_nickname VARCHAR(255),
    user_id VARCHAR(255),
    agent_name VARCHAR(255),
    agent_role VARCHAR(50) DEFAULT 'operator',
    overall_emotion VARCHAR(50),
    sentiment_shift TEXT,
    latest_sentiment VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(report_date, website_id, session_id, agent_name)
);
```

### 4. **Cập nhật Data Schema**

**Trước (Phức tạp):**
```python
class NightlyReport(BaseModel):
    report_id: str
    conversations: List[ConversationSentiment]  # Chứa toàn bộ messages
    # ... nhiều field khác
```

**Sau (Đơn giản):**
```python
class DailySentimentRecord(BaseModel):
    website_id: str
    session_id: str
    user_nickname: str
    user_id: str
    agent_name: str
    agent_role: str = "operator"
    overall_emotion: str
    sentiment_shift: str
    latest_sentiment: str

class DailySentimentReport(BaseModel):
    report_date: str
    total_records: int
    total_conversations: int
    total_agents: int
    records: List[DailySentimentRecord]
```

### 5. **Tạo Daily Report Generator mới**

**File:** `app/services/daily_report_generator.py`

**Tính năng:**
- Tạo báo cáo đơn giản hóa (không chứa messages)
- Tạo record riêng cho mỗi agent-conversation pair
- Backup file JSON local
- Convert data format cho PostgreSQL

### 6. **Cập nhật Continuous Sentiment Processor**

**File:** `app/services/continuous_sentiment_processor.py`

**Tính năng:**
- Xử lý sentiment analysis liên tục trong ngày
- Lưu trữ kết quả trong memory
- Reset data hàng ngày
- Tích hợp với processing config chung

### 7. **Cập nhật Periodic Scheduler**

**Thay đổi:**
- MongoDB → PostgreSQL
- `_run_mongodb_upload()` → `_run_postgresql_upload()`
- Backup file trước khi upload
- Cập nhật status tracking

### 8. **Cập nhật Main Application**

**Thay đổi:**
```python
# Trước
nightly_generator = NightlyReportGenerator(config, sentiment_service, db)
scheduler = PeriodicScheduler(config, nightly_generator)

# Sau
continuous_processor = ContinuousSentimentProcessor(config, sentiment_service, db)
daily_generator = DailyReportGenerator(config, continuous_processor)
scheduler = PeriodicScheduler(config, continuous_processor, daily_generator)
```

## Workflow mới

### 1. **Xử lý liên tục trong ngày**
```
30min → Phân tích sentiment cho conversations mới
60min → Phân tích sentiment cho conversations mới
3h    → Phân tích sentiment cho conversations mới
6h    → Phân tích sentiment cho conversations mới
```

### 2. **Upload hàng ngày vào 9h sáng**
```
09:00 → Tạo daily report từ dữ liệu đã xử lý
      → Convert thành records đơn giản
      → Upload lên PostgreSQL
      → Backup file JSON local
      → Clear memory data
```

### 3. **Cấu trúc dữ liệu PostgreSQL**

**Ví dụ data:**
```
| report_date | website_id | session_id | user_nickname | agent_name | overall_emotion | sentiment_shift | latest_sentiment |
|-------------|------------|------------|---------------|------------|-----------------|-----------------|------------------|
| 2024-01-15  | web123     | sess456    | John Doe      | Agent A    | satisfaction    | neutral > happy | satisfaction     |
| 2024-01-15  | web123     | sess456    | John Doe      | Agent B    | satisfaction    | neutral > happy | satisfaction     |
| 2024-01-15  | web123     | sess789    | Jane Smith    | Agent A    | frustration     | angry > neutral | neutral          |
```

**Đặc điểm:**
- Mỗi dòng = 1 agent + 1 conversation
- Nếu 1 conversation có nhiều agent → nhiều dòng
- Không chứa nội dung chat (messages)
- Chỉ chứa kết quả sentiment analysis

## Lợi ích của việc migration

### 1. **Dữ liệu đơn giản hơn**
- ❌ Trước: Lưu toàn bộ messages, metadata phức tạp
- ✅ Sau: Chỉ lưu kết quả sentiment analysis cần thiết

### 2. **Performance tốt hơn**
- ❌ Trước: Upload file JSON lớn lên MongoDB
- ✅ Sau: Upload records nhỏ lên PostgreSQL với index

### 3. **Phân tích dễ dàng hơn**
- ❌ Trước: Cần parse JSON phức tạp
- ✅ Sau: Query SQL trực tiếp, join dễ dàng

### 4. **Scalability tốt hơn**
- ❌ Trước: MongoDB document size limit
- ✅ Sau: PostgreSQL table với proper indexing

### 5. **Backup và recovery**
- ✅ Local JSON backup trước khi upload
- ✅ PostgreSQL native backup tools
- ✅ Easy data migration và export

## Cách sử dụng

### 1. **Setup PostgreSQL**
```sql
CREATE DATABASE sentiment_analysis;
-- Table sẽ được tự động tạo khi khởi động service
```

### 2. **Cấu hình**

**File config/config.yaml:**
```yaml
daily_sentiment:
  enabled: true
  processing_intervals: [30, 60, 180, 360]
  # PostgreSQL configuration is now stored in postgresql_config.yaml in the root directory
```

**File postgresql_config.yaml (trong thư mục chính):**
```yaml
postgresql:
  enabled: true
  host: "localhost"
  port: 5432
  database: "sentiment_analysis"
  username: "postgres"
  password: "your_password"
  table_name: "daily_sentiment_reports"
  upload_time: "09:00"
  retry_attempts: 3
  retry_delay_seconds: 60
  connect_timeout: 10
  backup_enabled: true
  backup_path: "data/postgresql_backups"
```

### 3. **Khởi động hệ thống**
```bash
python app/main.py
```

### 4. **Kiểm tra dữ liệu**
```sql
SELECT * FROM daily_sentiment_reports
WHERE report_date = '2024-01-15'
ORDER BY session_id, agent_name;
```

Hệ thống giờ đây **đơn giản, hiệu quả** và **dễ phân tích** hơn với PostgreSQL!

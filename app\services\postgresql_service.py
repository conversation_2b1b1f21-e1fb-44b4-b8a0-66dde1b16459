"""
PostgreSQL Service
-----------------
Service for uploading sentiment analysis data to PostgreSQL
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading

try:
    import psycopg2
    from psycopg2.extras import RealDict<PERSON>ursor
    from psycopg2 import sql
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False
    psycopg2 = None

logger = logging.getLogger(__name__)


class PostgreSQLService:
    """
    Service for uploading sentiment analysis data to PostgreSQL.
    Handles connection management, data upload, and error recovery.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize PostgreSQL service.

        Args:
            config: PostgreSQL configuration from config.yaml
        """
        self.config = config
        self.enabled = config.get('enabled', False)

        # Always initialize basic settings
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 5432)
        self.database = config.get('database', 'sentiment_analysis')
        self.username = config.get('username', 'postgres')
        self.password = config.get('password', 'password')
        self.table_name = config.get('table_name', 'daily_sentiment_reports')
        self.retry_attempts = config.get('retry_attempts', 3)
        self.retry_delay = config.get('retry_delay_seconds', 60)

        # Connection management
        self.connection = None
        self._lock = threading.Lock()

        if not PSYCOPG2_AVAILABLE:
            logger.warning("psycopg2 not available. PostgreSQL functionality disabled.")
            self.enabled = False
            return

        if not self.enabled:
            logger.info("PostgreSQL service disabled in configuration")
            return

        # Initialize connection and create table only if enabled
        self._connect()
        self._create_table_if_not_exists()

        logger.info(f"PostgreSQL service initialized: {self.database}.{self.table_name}")

    def _connect(self):
        """Establish connection to PostgreSQL"""
        if not self.enabled:
            return False

        try:
            with self._lock:
                # Close existing connection if any
                if self.connection:
                    self.connection.close()

                # Create new connection
                self.connection = psycopg2.connect(
                    host=self.host,
                    port=self.port,
                    database=self.database,
                    user=self.username,
                    password=self.password,
                    connect_timeout=10
                )

                # Test connection
                with self.connection.cursor() as cursor:
                    cursor.execute("SELECT 1")

                logger.info(f"Successfully connected to PostgreSQL: {self.host}:{self.port}/{self.database}")
                return True

        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            self.connection = None
            return False

    def _is_connected(self) -> bool:
        """Check if PostgreSQL connection is active"""
        if not self.enabled or not self.connection:
            return False

        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            return True
        except Exception:
            return False

    def _create_table_if_not_exists(self):
        """Create the daily sentiment reports table if it doesn't exist"""
        if not self.enabled or not self._is_connected():
            return False

        try:
            with self._lock:
                with self.connection.cursor() as cursor:
                    create_table_query = sql.SQL("""
                        CREATE TABLE IF NOT EXISTS {} (
                            id SERIAL PRIMARY KEY,
                            report_date DATE NOT NULL,
                            website_id VARCHAR(255) NOT NULL,
                            session_id VARCHAR(255) NOT NULL,
                            user_nickname VARCHAR(255),
                            user_id VARCHAR(255),
                            agent_name VARCHAR(255),
                            agent_role VARCHAR(50) DEFAULT 'operator',
                            conversation_start BIGINT,
                            conversation_end BIGINT,
                            is_completed BOOLEAN DEFAULT FALSE,
                            overall_emotion VARCHAR(50),
                            sentiment_shift TEXT,
                            latest_sentiment VARCHAR(50),
                            message_count INTEGER DEFAULT 0,
                            analysis_timestamp BIGINT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """).format(sql.Identifier(self.table_name))

                    cursor.execute(create_table_query)
                    self.connection.commit()

                    # Create index for better performance
                    index_query = sql.SQL("""
                        CREATE INDEX IF NOT EXISTS idx_{}_report_date
                        ON {} (report_date)
                    """).format(
                        sql.Identifier(self.table_name + "_date"),
                        sql.Identifier(self.table_name)
                    )
                    cursor.execute(index_query)
                    self.connection.commit()

                    logger.info(f"Table {self.table_name} created/verified successfully")
                    return True

        except Exception as e:
            logger.error(f"Error creating table {self.table_name}: {e}")
            return False

    def upload_daily_sentiment_data(self, sentiment_records: List[Dict[str, Any]], report_date: str) -> bool:
        """
        Upload daily sentiment data to PostgreSQL.

        Args:
            sentiment_records: List of sentiment records to upload
            report_date: Report date in YYYY-MM-DD format

        Returns:
            True if upload successful, False otherwise
        """
        if not self.enabled:
            logger.debug("PostgreSQL upload skipped - service disabled")
            return False

        if not sentiment_records:
            logger.info("No sentiment records to upload")
            return True

        # Attempt upload with retries
        for attempt in range(self.retry_attempts):
            try:
                # Ensure connection
                if not self._is_connected():
                    logger.info(f"Reconnecting to PostgreSQL (attempt {attempt + 1})")
                    if not self._connect():
                        continue

                # Upload records (append-only, no deletion)
                with self._lock:
                    with self.connection.cursor() as cursor:
                        # Insert new records (append-only approach)
                        insert_query = sql.SQL("""
                            INSERT INTO {} (
                                report_date, website_id, session_id, user_nickname, user_id,
                                agent_name, agent_role, conversation_start, conversation_end, is_completed,
                                overall_emotion, sentiment_shift, latest_sentiment, message_count, analysis_timestamp
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """).format(sql.Identifier(self.table_name))

                        records_data = []
                        for record in sentiment_records:
                            records_data.append((
                                report_date,
                                record.get('website_id'),
                                record.get('session_id'),
                                record.get('user_nickname'),
                                record.get('user_id'),
                                record.get('agent_name'),
                                record.get('agent_role', 'operator'),
                                record.get('conversation_start'),
                                record.get('conversation_end'),
                                record.get('is_completed', False),
                                record.get('overall_emotion'),
                                record.get('sentiment_shift'),
                                record.get('latest_sentiment'),
                                record.get('message_count', 0),
                                record.get('analysis_timestamp')
                            ))

                        cursor.executemany(insert_query, records_data)
                        self.connection.commit()

                logger.info(f"Successfully uploaded {len(sentiment_records)} sentiment records for {report_date} to PostgreSQL")
                return True

            except Exception as e:
                logger.error(f"Error uploading to PostgreSQL (attempt {attempt + 1}): {e}")
                if attempt < self.retry_attempts - 1:
                    time.sleep(self.retry_delay)
                continue

        logger.error(f"Failed to upload sentiment data for {report_date} after {self.retry_attempts} attempts")
        return False

    def get_sentiment_data(self, report_date: str) -> List[Dict[str, Any]]:
        """
        Retrieve sentiment data from PostgreSQL by date.

        Args:
            report_date: Report date in YYYY-MM-DD format

        Returns:
            List of sentiment records or empty list if not found
        """
        if not self.enabled or not self._is_connected():
            return []

        try:
            with self._lock:
                with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                    query = sql.SQL("SELECT * FROM {} WHERE report_date = %s ORDER BY session_id, agent_name").format(
                        sql.Identifier(self.table_name)
                    )
                    cursor.execute(query, (report_date,))

                    records = cursor.fetchall()
                    return [dict(record) for record in records]

        except Exception as e:
            logger.error(f"Error retrieving sentiment data for {report_date}: {e}")
            return []

    def get_available_dates(self, limit: int = 100) -> List[str]:
        """
        List available report dates in PostgreSQL.

        Args:
            limit: Maximum number of dates to return

        Returns:
            List of report dates in YYYY-MM-DD format
        """
        if not self.enabled or not self._is_connected():
            return []

        try:
            with self._lock:
                with self.connection.cursor() as cursor:
                    query = sql.SQL("""
                        SELECT DISTINCT report_date
                        FROM {}
                        ORDER BY report_date DESC
                        LIMIT %s
                    """).format(sql.Identifier(self.table_name))

                    cursor.execute(query, (limit,))
                    dates = cursor.fetchall()

                    return [str(date[0]) for date in dates]

        except Exception as e:
            logger.error(f"Error listing available dates: {e}")
            return []

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get PostgreSQL connection status and statistics.

        Returns:
            Dictionary with connection status information
        """
        status = {
            "enabled": self.enabled,
            "connected": False,
            "host": self.host,
            "port": self.port,
            "database": self.database,
            "table": self.table_name
        }

        if not self.enabled:
            status["message"] = "PostgreSQL service disabled"
            return status

        if not PSYCOPG2_AVAILABLE:
            status["message"] = "psycopg2 not available"
            return status

        try:
            if self._is_connected():
                status["connected"] = True

                # Get table stats
                with self._lock:
                    with self.connection.cursor() as cursor:
                        cursor.execute(sql.SQL("SELECT COUNT(*) FROM {}").format(
                            sql.Identifier(self.table_name)
                        ))
                        count = cursor.fetchone()[0]
                        status["record_count"] = count

                status["message"] = "Connected and operational"
            else:
                status["message"] = "Connection failed"

        except Exception as e:
            status["message"] = f"Error checking status: {e}"

        return status

    def test_connection(self) -> bool:
        """
        Test PostgreSQL connection.

        Returns:
            True if connection successful, False otherwise
        """
        if not self.enabled:
            logger.info("PostgreSQL service disabled")
            return False

        if not PSYCOPG2_AVAILABLE:
            logger.error("psycopg2 not available")
            return False

        try:
            # Test connection
            if self._connect():
                logger.info("PostgreSQL connection test successful")
                return True
            else:
                logger.error("PostgreSQL connection test failed")
                return False

        except Exception as e:
            logger.error(f"PostgreSQL connection test error: {e}")
            return False

    def close(self):
        """Close PostgreSQL connection"""
        if hasattr(self, 'connection') and self.connection:
            with self._lock:
                self.connection.close()
                self.connection = None
            logger.info("PostgreSQL connection closed")

    def __del__(self):
        """Cleanup on object destruction"""
        if hasattr(self, '_lock'):
            self.close()

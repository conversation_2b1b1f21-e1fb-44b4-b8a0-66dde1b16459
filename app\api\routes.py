from fastapi import APIRouter, Request, Response, status, Query
from fastapi.responses import JSONResponse
import json
import logging
import time
from typing import Dict, Any, Optional

# Import Hugging Face router
from app.api.huggingface import router as huggingface_router

logger = logging.getLogger(__name__)

# Create a router for the API routes
router = APIRouter(tags=["api"])

# Include Hugging Face router
router.include_router(huggingface_router)

@router.post("/sentiment_widget/callback")
@router.get("/sentiment_widget/callback")
async def sentiment_widget_callback():
    """Callback endpoint for sentiment widget"""
    logger.info("Received callback request")
    return {"status": "ok"}

@router.post("/sentiment_widget/setting")
@router.get("/sentiment_widget/setting")
async def sentiment_widget_setting():
    """Settings endpoint for sentiment widget"""
    logger.info("Received setting request")
    return {"status": "ok"}

@router.post("/sentiment_widget/action")
@router.get("/sentiment_widget/action")
async def sentiment_widget_action(request: Request):
    """Action endpoint for sentiment widget"""
    try:
        # Log the incoming request
        body_bytes = await request.body()
        body_str = body_bytes.decode('utf-8')
        logger.info(f"Received sentiment action request: {body_str}")

        # Parse the request body
        body = json.loads(body_str)
        item_id = body.get('widget', {}).get('item_id')
        session_id = body.get('origin', {}).get('session_id')
        website_id = body.get('origin', {}).get('website_id')

        # Validate required fields
        if not all([item_id, session_id, website_id]):
            logger.error("Missing required fields in request")
            return JSONResponse(
                content={
                    "data": {
                        "value": "error_missing_fields"
                    }
                },
                status_code=200
            )

        # Validate website ID against whitelist
        from app import get_website_validator
        website_validator = get_website_validator()
        if not website_validator.validate_website_id(website_id):
            logger.warning(f"Rejected sentiment request from unauthorized website_id: {website_id}")
            return JSONResponse(
                content={
                    "data": {
                        "value": "error_unauthorized_website"
                    }
                },
                status_code=200
            )

        # Add the request to the sentiment analyzer
        from app import get_sentiment_analyzer
        sentiment_analyzer = get_sentiment_analyzer()
        logger.info(f"Adding request get_sentiment_analyzer for item_id: {item_id}, session_id: {session_id}, website_id: {website_id}")
        request_id = sentiment_analyzer.add_request({
            'item_id': item_id,
            'session_id': session_id,
            'website_id': website_id
        })

        # Wait for the result with a timeout
        # GPU processing typically takes a few seconds, so we'll wait for a short period
        max_wait_time = 5  # Maximum wait time in seconds
        polling_interval = 0.5  # Check every 0.5 seconds
        wait_time = 0

        # Poll for the result until timeout
        while wait_time < max_wait_time:
            result = sentiment_analyzer.get_result(request_id)
            if result:
                logger.info(f"Got result for request {request_id} after {wait_time} seconds")
                return result

            # Wait before checking again
            time.sleep(polling_interval)
            wait_time += polling_interval

        # If we've waited the maximum time and still no result, return processing status
        logger.info(f"Timeout reached for request {request_id}, returning processing status")
        return JSONResponse(
            content={
                "data": {
                    "value": "processing",
                    "request_id": request_id  # Include the request_id for status checking
                }
            },
            status_code=202  # 202 Accepted indicates the request is being processed
        )
    except Exception as e:
        logger.error(f"Error in sentiment_widget_action: {e}")
        return JSONResponse(
            content={
                "data": {
                    "value": "error"  # Error value
                }
            },
            status_code=200  # Still return 200 to avoid webhook failures
        )

@router.get("/sentiment_status/{request_id}")
async def sentiment_status(request_id: str, timeout: float = Query(3.0, ge=0.5, le=10.0)):
    """Check the status of a sentiment analysis request"""
    try:
        from app import get_sentiment_analyzer
        sentiment_analyzer = get_sentiment_analyzer()

        polling_interval = 0.2  # Check every 0.2 seconds
        wait_time = 0

        # Poll for the result until timeout
        while wait_time < timeout:
            result = sentiment_analyzer.get_result(request_id)
            if result:
                logger.info(f"Got result for request {request_id} after {wait_time} seconds")
                return result

            # Wait before checking again
            time.sleep(polling_interval)
            wait_time += polling_interval

        # If we've waited the maximum time and still no result, return processing status
        logger.info(f"Timeout reached for status check of request {request_id}")
        return JSONResponse(
            content={
                "status": "processing",
                "request_id": request_id,
                "message": f"Still processing after {timeout} seconds"
            },
            status_code=202  # 202 Accepted indicates the request is being processed
        )
    except Exception as e:
        logger.error(f"Error in sentiment_status: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": str(e)
            },
            status_code=500
        )

@router.get("/sentiment_queue_status")
async def sentiment_queue_status():
    """Get the current status of the sentiment analysis queue

    This endpoint provides information about the current state of the request queue,
    including the number of requests in the queue, processed, and failed.
    """
    try:
        from app import get_sentiment_analyzer
        sentiment_analyzer = get_sentiment_analyzer()

        # Get the queue status
        status = sentiment_analyzer.get_queue_status()

        # Add some human-readable timestamps
        status['timestamp_readable'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(status['timestamp']))

        # Calculate additional metrics
        if status['total_requests_received'] > 0:
            status['failure_rate'] = (status['total_requests_failed'] / status['total_requests_received']) * 100
            status['completion_rate'] = ((status['total_requests_processed'] + status['total_requests_failed']) /
                                       status['total_requests_received']) * 100
        else:
            status['failure_rate'] = 0
            status['completion_rate'] = 0

        # Format rates as percentages with 2 decimal places
        status['success_rate'] = f"{status['success_rate']:.2f}%"
        status['failure_rate'] = f"{status['failure_rate']:.2f}%"
        status['completion_rate'] = f"{status['completion_rate']:.2f}%"

        return {
            "status": "ok",
            "queue": status
        }
    except Exception as e:
        logger.error(f"Error in sentiment_queue_status: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": str(e)
            },
            status_code=500
        )

@router.get("/sentiment_wait/{request_id}")
async def sentiment_wait(request_id: str, timeout: float = Query(15.0, ge=1.0, le=30.0)):
    """Wait for the sentiment analysis result with a longer timeout

    This endpoint is specifically designed for cases where you want to wait
    for the GPU to complete processing. It uses a longer default timeout.
    """
    try:
        from app import get_sentiment_analyzer
        sentiment_analyzer = get_sentiment_analyzer()

        polling_interval = 0.5  # Check every 0.5 seconds
        wait_time = 0

        logger.info(f"Waiting up to {timeout} seconds for result of request {request_id}")

        # Poll for the result until timeout
        while wait_time < timeout:
            result = sentiment_analyzer.get_result(request_id)
            if result:
                logger.info(f"Got result for request {request_id} after {wait_time} seconds")
                return result

            # Wait before checking again
            time.sleep(polling_interval)
            wait_time += polling_interval

            # Log progress for long waits
            if wait_time % 5 < polling_interval:  # Log approximately every 5 seconds
                logger.info(f"Still waiting for result of request {request_id}, elapsed time: {wait_time:.1f}s")

        # If we've waited the maximum time and still no result, return processing status
        logger.warning(f"Maximum wait time ({timeout}s) reached for request {request_id} without result")
        return JSONResponse(
            content={
                "status": "processing",
                "request_id": request_id,
                "message": f"Still processing after {timeout} seconds. The GPU may be busy or the request may be complex."
            },
            status_code=202  # 202 Accepted indicates the request is being processed
        )
    except Exception as e:
        logger.error(f"Error in sentiment_wait: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": str(e)
            },
            status_code=500
        )

# Quickstart Guide

This guide will help you quickly set up and run the Sentiment Analysis API System with Ngrok integration.

## Prerequisites

- Python 3.9 or higher
- pip (Python package manager)
- An Ngrok account and auth token (if you want to expose the API to the internet)

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd uppromote-system-sentiment-plugin-server
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

   For CUDA support with llama-cpp-python:
   ```bash
   CMAKE_ARGS="-DLLAMA_CUBLAS=on" pip install llama-cpp-python==0.2.65
   ```

## Configuration

1. Edit the `config/config.yaml` file to configure the system:

   - Set your Ngrok auth token:
     ```yaml
     credentials:
       NGROK_AUTH_TOKEN: "your-ngrok-auth-token"
     ```

   - Configure the model:
     ```yaml
     model:
       name: "TheBloke/Llama-3-8B-Instruct-GGUF"  # Use Llama model
       model_type: "llama.cpp"  # Use llama.cpp
       gguf:
         quantization: "q4_k_m"  # Quantization level
         gpu_layers: 0  # Number of layers to offload to GPU (0 for CPU only)
         context_size: 2048  # Context size
     ```

   - Configure processing:
     ```yaml
     processing:
       mode: "immediate"  # Process each request immediately
       cpu_threads: 1  # Number of worker threads
       cpu_cores_per_request: 0  # Use all available CPU cores for each request
     ```

## Running the Server

There are two ways to run the server:

### Option 1: Run the server without Ngrok

1. Run the server using the main.py script:
   ```bash
   python main.py
   ```

2. The server will start and display:
   - The local URL (e.g., http://0.0.0.0:5000)
   - A note about running Ngrok separately

### Option 2: Run the server with Ngrok (recommended)

1. Start the server in one terminal:
   ```bash
   python main.py
   ```

2. Start Ngrok in another terminal:
   ```bash
   python run_ngrok.py --port 5000 --keep-alive
   ```

3. The server will be accessible via:
   - The local URL (e.g., http://0.0.0.0:5000)
   - The public Ngrok URL (displayed by run_ngrok.py)
   - Webhook URLs for integration (displayed by run_ngrok.py)

## Ngrok Management

The system includes several tools for managing Ngrok tunnels:

### 1. Using run_ngrok.py

This script allows you to start and manage Ngrok tunnels independently:

```bash
# Start a tunnel to port 5000 and keep it alive
python run_ngrok.py --port 5000 --keep-alive

# Start a tunnel with a custom auth token
python run_ngrok.py --token "your-ngrok-auth-token"

# Kill all existing ngrok processes and exit
python run_ngrok.py --kill

# Start a tunnel without restarting existing ngrok processes
python run_ngrok.py --no-restart

# Get help
python run_ngrok.py --help
```

The script automatically handles the "limited to 1 simultaneous ngrok agent session" error by killing existing ngrok processes and retrying.

### 2. Using ngrok_dashboard.py

This script provides an interactive dashboard for managing Ngrok tunnels:

```bash
python ngrok_dashboard.py
```

The dashboard allows you to:
- Start new tunnels
- List active tunnels
- Stop specific tunnels
- Stop all tunnels (including killing all ngrok processes)
- View Ngrok configuration

The dashboard now includes improved handling for the "limited to 1 simultaneous ngrok agent session" error by automatically killing existing ngrok processes when needed.

## API Endpoints

- **Webhook**: `{public_url}/webhook`
- **Sentiment Widget Action**: `{public_url}/sentiment_widget/action`
- **Sentiment Status**: `{public_url}/sentiment_status/{request_id}`

## Monitoring

- Check the logs in the `logs/app.log` file for detailed information
- The server will display resource usage information in the console

## Stopping the Server

- Press `Ctrl+C` to stop the server
- The server will gracefully shut down all services and stop the Ngrok tunnel

## Troubleshooting

- If you encounter issues with Ngrok, make sure your auth token is correct
- If the model fails to load, check the model configuration in `config/config.yaml`
- For CPU usage issues, adjust the `cpu_cores_per_request` and `cpu_threads` settings
- Use the Ngrok dashboard to check the status of your tunnels

"""
Hugging Face Service
------------------
Service for interacting with Hugging Face Hub
"""

import os
import logging
from huggingface_hub import login, HfApi, hf_hub_download
from app.core.config import Config

logger = logging.getLogger(__name__)

class HuggingFaceService:
    """Service for interacting with Hugging Face Hub"""
    
    _instance = None
    _is_logged_in = False
    
    @classmethod
    def get_instance(cls):
        """Get singleton instance of the service"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """Initialize the Hugging Face service"""
        self.config = Config()
        self.api = HfApi()
        self.token = None
        
    def login(self, token=None, force=False):
        """
        Login to Hugging Face Hub
        
        Args:
            token: Hugging Face token (if None, will use token from config)
            force: Whether to force login even if already logged in
            
        Returns:
            bool: Whether login was successful
        """
        # If already logged in and not forcing, return True
        if HuggingFaceService._is_logged_in and not force:
            logger.info("Already logged in to Hugging Face Hub")
            return True
            
        try:
            # Get token from config if not provided
            if token is None:
                _, _, _, _, token = self.config.get_credentials()
            
            # Store token for later use
            self.token = token
            
            # If token is None or empty, return False
            if not token:
                logger.warning("No Hugging Face token provided")
                return False
                
            # Login to Hugging Face Hub
            login(token=token, add_to_git_credential=False)
            logger.info("Successfully logged in to Hugging Face Hub")
            
            # Set logged in flag
            HuggingFaceService._is_logged_in = True
            
            return True
        except Exception as e:
            logger.error(f"Error logging in to Hugging Face Hub: {e}")
            return False
    
    def download_model(self, repo_id, filename, local_dir=None, force_download=False):
        """
        Download a model from Hugging Face Hub
        
        Args:
            repo_id: Repository ID (e.g., "meta-llama/Llama-3.2-1B")
            filename: Filename to download
            local_dir: Local directory to save the file (if None, will use cache)
            force_download: Whether to force download even if file exists
            
        Returns:
            str: Path to the downloaded file
        """
        try:
            # Login if not already logged in
            if not HuggingFaceService._is_logged_in:
                self.login()
                
            # Download the file
            logger.info(f"Downloading {filename} from {repo_id}...")
            path = hf_hub_download(
                repo_id=repo_id,
                filename=filename,
                token=self.token,
                local_dir=local_dir,
                force_download=force_download
            )
            
            logger.info(f"Successfully downloaded {filename} to {path}")
            return path
        except Exception as e:
            logger.error(f"Error downloading {filename} from {repo_id}: {e}")
            raise
    
    def list_models(self, filter_criteria=None):
        """
        List models from Hugging Face Hub
        
        Args:
            filter_criteria: Filter criteria for models
            
        Returns:
            list: List of models
        """
        try:
            # Login if not already logged in
            if not HuggingFaceService._is_logged_in:
                self.login()
                
            # List models
            models = self.api.list_models(filter=filter_criteria)
            return list(models)
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []
    
    def get_model_info(self, repo_id):
        """
        Get information about a model
        
        Args:
            repo_id: Repository ID (e.g., "meta-llama/Llama-3.2-1B")
            
        Returns:
            dict: Model information
        """
        try:
            # Login if not already logged in
            if not HuggingFaceService._is_logged_in:
                self.login()
                
            # Get model info
            model_info = self.api.model_info(repo_id, token=self.token)
            return model_info
        except Exception as e:
            logger.error(f"Error getting model info for {repo_id}: {e}")
            return None

"""
Airflow Integration API endpoints
Provides endpoints for Apache Airflow to manage and monitor sentiment analysis tasks
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import asyncio
import uuid
import json
import os
import time
from datetime import datetime, timedelta
import logging

from app.core.config import Config
from app.services.sentiment_service import SentimentService
from app.services.postgresql_service import PostgreSQLService
from app.utils.task_executors import (
    execute_sentiment_analysis,
    execute_batch_sentiment_analysis,
    execute_daily_report_generation,
    execute_data_cleanup
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/airflow", tags=["airflow-integration"])
security = HTTPBearer()

# Task status tracking
task_registry = {}

class TaskRequest(BaseModel):
    task_type: str
    parameters: Dict[str, Any]
    priority: int = 1
    timeout: int = 3600
    retry_count: int = 3

class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str
    created_at: datetime
    estimated_duration: Optional[int] = None

class TaskStatus(BaseModel):
    task_id: str
    status: str
    progress: float
    message: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None

class SystemHealth(BaseModel):
    status: str
    services: Dict[str, str]
    metrics: Dict[str, Any]
    timestamp: datetime

class BatchProcessRequest(BaseModel):
    website_ids: List[str]
    date_range: Dict[str, str]
    processing_type: str = "daily_sentiment"
    batch_size: int = 100

# Authentication dependency
async def verify_airflow_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify Airflow authentication token"""
    expected_token = os.getenv("AIRFLOW_API_TOKEN", "airflow-secret-token")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    return credentials.credentials

@router.get("/health", response_model=SystemHealth)
async def get_system_health():
    """Get comprehensive system health for Airflow monitoring"""
    try:
        config = Config()

        # Check service health
        services = {}

        # Check database
        try:
            postgresql_service = PostgreSQLService(config.get_postgresql_config())
            if postgresql_service.test_connection():
                services["postgresql"] = "healthy"
            else:
                services["postgresql"] = "unhealthy"
        except Exception as e:
            services["postgresql"] = f"error: {str(e)}"

        # Check sentiment service
        try:
            sentiment_service = SentimentService(config)
            services["sentiment_analysis"] = "healthy"
        except Exception as e:
            services["sentiment_analysis"] = f"error: {str(e)}"

        # Get system metrics
        metrics = {
            "active_tasks": len([t for t in task_registry.values() if t["status"] == "running"]),
            "pending_tasks": len([t for t in task_registry.values() if t["status"] == "pending"]),
            "completed_tasks": len([t for t in task_registry.values() if t["status"] == "completed"]),
            "failed_tasks": len([t for t in task_registry.values() if t["status"] == "failed"]),
            "cpu_usage": get_cpu_usage(),
            "memory_usage": get_memory_usage(),
            "disk_usage": get_disk_usage()
        }

        # Determine overall status
        overall_status = "healthy"
        if any(status != "healthy" for status in services.values()):
            overall_status = "degraded"

        return SystemHealth(
            status=overall_status,
            services=services,
            metrics=metrics,
            timestamp=datetime.now()
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.post("/tasks", response_model=TaskResponse)
async def create_task(
    task_request: TaskRequest,
    background_tasks: BackgroundTasks,
    token: str = Depends(verify_airflow_token)
):
    """Create a new task for execution"""
    try:
        task_id = str(uuid.uuid4())

        # Register task
        task_info = {
            "task_id": task_id,
            "task_type": task_request.task_type,
            "parameters": task_request.parameters,
            "status": "pending",
            "progress": 0.0,
            "message": "Task created",
            "created_at": datetime.now(),
            "priority": task_request.priority,
            "timeout": task_request.timeout,
            "retry_count": task_request.retry_count,
            "current_retry": 0
        }

        task_registry[task_id] = task_info

        # Execute task in background
        background_tasks.add_task(execute_task, task_id, task_request)

        # Estimate duration based on task type
        estimated_duration = estimate_task_duration(task_request.task_type, task_request.parameters)

        return TaskResponse(
            task_id=task_id,
            status="pending",
            message="Task created and queued for execution",
            created_at=task_info["created_at"],
            estimated_duration=estimated_duration
        )

    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")

@router.get("/tasks/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str, token: str = Depends(verify_airflow_token)):
    """Get status of a specific task"""
    if task_id not in task_registry:
        raise HTTPException(status_code=404, detail="Task not found")

    task_info = task_registry[task_id]

    return TaskStatus(
        task_id=task_id,
        status=task_info["status"],
        progress=task_info["progress"],
        message=task_info["message"],
        result=task_info.get("result"),
        error=task_info.get("error"),
        started_at=task_info.get("started_at"),
        completed_at=task_info.get("completed_at"),
        duration=task_info.get("duration")
    )

@router.get("/tasks", response_model=List[TaskStatus])
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 100,
    token: str = Depends(verify_airflow_token)
):
    """List all tasks with optional filtering"""
    tasks = []

    for task_id, task_info in task_registry.items():
        if status and task_info["status"] != status:
            continue

        tasks.append(TaskStatus(
            task_id=task_id,
            status=task_info["status"],
            progress=task_info["progress"],
            message=task_info["message"],
            result=task_info.get("result"),
            error=task_info.get("error"),
            started_at=task_info.get("started_at"),
            completed_at=task_info.get("completed_at"),
            duration=task_info.get("duration")
        ))

    # Sort by created_at descending and limit
    tasks.sort(key=lambda x: task_registry[x.task_id]["created_at"], reverse=True)
    return tasks[:limit]

@router.delete("/tasks/{task_id}")
async def cancel_task(task_id: str, token: str = Depends(verify_airflow_token)):
    """Cancel a running task"""
    if task_id not in task_registry:
        raise HTTPException(status_code=404, detail="Task not found")

    task_info = task_registry[task_id]

    if task_info["status"] in ["completed", "failed", "cancelled"]:
        raise HTTPException(status_code=400, detail="Task cannot be cancelled")

    task_info["status"] = "cancelled"
    task_info["message"] = "Task cancelled by user"
    task_info["completed_at"] = datetime.now()

    return {"message": "Task cancelled successfully"}

@router.post("/batch-process", response_model=TaskResponse)
async def create_batch_process(
    batch_request: BatchProcessRequest,
    background_tasks: BackgroundTasks,
    token: str = Depends(verify_airflow_token)
):
    """Create a batch processing task"""
    task_request = TaskRequest(
        task_type="batch_sentiment_analysis",
        parameters={
            "website_ids": batch_request.website_ids,
            "date_range": batch_request.date_range,
            "processing_type": batch_request.processing_type,
            "batch_size": batch_request.batch_size
        },
        priority=2,
        timeout=7200  # 2 hours for batch processing
    )

    return await create_task(task_request, background_tasks, token)

@router.get("/metrics")
async def get_metrics(token: str = Depends(verify_airflow_token)):
    """Get detailed metrics for monitoring"""
    try:
        config = Config()

        # Task metrics
        task_metrics = {
            "total_tasks": len(task_registry),
            "active_tasks": len([t for t in task_registry.values() if t["status"] == "running"]),
            "pending_tasks": len([t for t in task_registry.values() if t["status"] == "pending"]),
            "completed_tasks": len([t for t in task_registry.values() if t["status"] == "completed"]),
            "failed_tasks": len([t for t in task_registry.values() if t["status"] == "failed"]),
            "cancelled_tasks": len([t for t in task_registry.values() if t["status"] == "cancelled"])
        }

        # Performance metrics
        completed_tasks = [t for t in task_registry.values() if t["status"] == "completed" and "duration" in t]
        if completed_tasks:
            durations = [t["duration"] for t in completed_tasks]
            performance_metrics = {
                "avg_task_duration": sum(durations) / len(durations),
                "min_task_duration": min(durations),
                "max_task_duration": max(durations),
                "total_processing_time": sum(durations)
            }
        else:
            performance_metrics = {
                "avg_task_duration": 0,
                "min_task_duration": 0,
                "max_task_duration": 0,
                "total_processing_time": 0
            }

        # System metrics
        system_metrics = {
            "cpu_usage": get_cpu_usage(),
            "memory_usage": get_memory_usage(),
            "disk_usage": get_disk_usage(),
            "uptime": get_uptime()
        }

        return {
            "task_metrics": task_metrics,
            "performance_metrics": performance_metrics,
            "system_metrics": system_metrics,
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

# Helper functions
async def execute_task(task_id: str, task_request: TaskRequest):
    """Execute a task in the background"""
    try:
        task_info = task_registry[task_id]
        task_info["status"] = "running"
        task_info["started_at"] = datetime.now()
        task_info["message"] = "Task execution started"

        config = Config()

        if task_request.task_type == "sentiment_analysis":
            result = await execute_sentiment_analysis(task_request.parameters, task_info)
        elif task_request.task_type == "batch_sentiment_analysis":
            result = await execute_batch_sentiment_analysis(task_request.parameters, task_info)
        elif task_request.task_type == "daily_report_generation":
            result = await execute_daily_report_generation(task_request.parameters, task_info)
        elif task_request.task_type == "data_cleanup":
            result = await execute_data_cleanup(task_request.parameters, task_info)
        else:
            raise ValueError(f"Unknown task type: {task_request.task_type}")

        # Task completed successfully
        task_info["status"] = "completed"
        task_info["result"] = result
        task_info["progress"] = 100.0
        task_info["message"] = "Task completed successfully"
        task_info["completed_at"] = datetime.now()
        task_info["duration"] = (task_info["completed_at"] - task_info["started_at"]).total_seconds()

    except Exception as e:
        # Task failed
        task_info["status"] = "failed"
        task_info["error"] = str(e)
        task_info["message"] = f"Task failed: {str(e)}"
        task_info["completed_at"] = datetime.now()
        task_info["duration"] = (task_info["completed_at"] - task_info["started_at"]).total_seconds()
        logger.error(f"Task {task_id} failed: {e}")

def estimate_task_duration(task_type: str, parameters: Dict[str, Any]) -> int:
    """Estimate task duration in seconds"""
    estimates = {
        "sentiment_analysis": 30,
        "batch_sentiment_analysis": 300,
        "daily_report_generation": 120,
        "data_cleanup": 60
    }

    base_duration = estimates.get(task_type, 60)

    # Adjust based on parameters
    if task_type == "batch_sentiment_analysis":
        batch_size = parameters.get("batch_size", 100)
        website_count = len(parameters.get("website_ids", []))
        base_duration = base_duration * (website_count * batch_size / 1000)

    return int(base_duration)

def get_cpu_usage() -> float:
    """Get current CPU usage percentage"""
    try:
        import psutil
        return psutil.cpu_percent(interval=1)
    except ImportError:
        return 0.0

def get_memory_usage() -> float:
    """Get current memory usage percentage"""
    try:
        import psutil
        return psutil.virtual_memory().percent
    except ImportError:
        return 0.0

def get_disk_usage() -> float:
    """Get current disk usage percentage"""
    try:
        import psutil
        return psutil.disk_usage('/').percent
    except ImportError:
        return 0.0

def get_uptime() -> float:
    """Get system uptime in seconds"""
    try:
        import psutil
        return time.time() - psutil.boot_time()
    except ImportError:
        return 0.0

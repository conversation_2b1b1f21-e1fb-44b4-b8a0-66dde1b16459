"""
Conversation Tracker Service
---------------------------
Service for tracking conversation states and managing periodic sentiment analysis
"""

import json
import os
import threading
import time
import logging
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta

from app.models.schemas import ConversationTrackingInfo

logger = logging.getLogger(__name__)


class ConversationTracker:
    """
    Service for tracking conversation states and managing periodic sentiment analysis.
    This service keeps track of:
    - Last message timestamp for each conversation
    - Last sentiment analysis timestamp
    - Conversation completion status
    - Message counts
    """

    def __init__(self, tracking_file_path: str = "data/conversation_tracking.json"):
        """
        Initialize the conversation tracker.

        Args:
            tracking_file_path: Path to the tracking data file
        """
        self.tracking_file_path = tracking_file_path
        self._lock = threading.Lock()
        self._tracking_data: Dict[str, ConversationTrackingInfo] = {}
        
        # Load existing tracking data
        self._load_tracking_data()
        
        logger.info(f"ConversationTracker initialized with {len(self._tracking_data)} tracked conversations")

    def _load_tracking_data(self):
        """Load tracking data from disk"""
        try:
            if os.path.exists(self.tracking_file_path):
                with open(self.tracking_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Convert dict data to ConversationTrackingInfo objects
                for key, value in data.items():
                    self._tracking_data[key] = ConversationTrackingInfo(**value)
                    
                logger.info(f"Loaded {len(self._tracking_data)} conversation tracking records")
            else:
                logger.info("No existing tracking data found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading tracking data: {e}")
            self._tracking_data = {}

    def _save_tracking_data(self):
        """Save tracking data to disk"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.tracking_file_path), exist_ok=True)
            
            # Convert ConversationTrackingInfo objects to dict for JSON serialization
            data = {}
            for key, value in self._tracking_data.items():
                data[key] = value.dict()
            
            with open(self.tracking_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.debug(f"Saved {len(self._tracking_data)} conversation tracking records")
        except Exception as e:
            logger.error(f"Error saving tracking data: {e}")

    def _get_conversation_key(self, website_id: str, session_id: str) -> str:
        """Generate a unique key for conversation tracking"""
        return f"{website_id}_{session_id}"

    def update_conversation(self, website_id: str, session_id: str, 
                          last_message_timestamp: int, message_count: int,
                          is_completed: bool = False):
        """
        Update conversation tracking information.

        Args:
            website_id: Website ID
            session_id: Session ID
            last_message_timestamp: Timestamp of the last message
            message_count: Total number of messages in conversation
            is_completed: Whether the conversation is completed
        """
        with self._lock:
            key = self._get_conversation_key(website_id, session_id)
            
            if key in self._tracking_data:
                # Update existing tracking info
                tracking_info = self._tracking_data[key]
                tracking_info.last_message_timestamp = last_message_timestamp
                tracking_info.message_count = message_count
                
                if is_completed and not tracking_info.is_completed:
                    tracking_info.is_completed = True
                    tracking_info.completion_timestamp = int(time.time())
            else:
                # Create new tracking info
                tracking_info = ConversationTrackingInfo(
                    session_id=session_id,
                    website_id=website_id,
                    last_message_timestamp=last_message_timestamp,
                    message_count=message_count,
                    is_completed=is_completed,
                    completion_timestamp=int(time.time()) if is_completed else None
                )
                self._tracking_data[key] = tracking_info
            
            # Save to disk
            self._save_tracking_data()

    def update_sentiment_analysis(self, website_id: str, session_id: str, 
                                analysis_timestamp: int):
        """
        Update the last sentiment analysis timestamp for a conversation.

        Args:
            website_id: Website ID
            session_id: Session ID
            analysis_timestamp: Timestamp when sentiment analysis was performed
        """
        with self._lock:
            key = self._get_conversation_key(website_id, session_id)
            
            if key in self._tracking_data:
                self._tracking_data[key].last_sentiment_analysis_timestamp = analysis_timestamp
                self._save_tracking_data()
            else:
                logger.warning(f"Attempted to update sentiment analysis for unknown conversation: {key}")

    def get_conversations_for_periodic_analysis(self, 
                                              max_age_hours: int = 168,
                                              min_messages: int = 2) -> List[ConversationTrackingInfo]:
        """
        Get conversations that need periodic sentiment analysis.

        Args:
            max_age_hours: Maximum age of conversations to include (in hours)
            min_messages: Minimum number of messages required for analysis

        Returns:
            List of conversations that need analysis
        """
        with self._lock:
            current_time = int(time.time())
            max_age_seconds = max_age_hours * 3600
            
            conversations_for_analysis = []
            
            for tracking_info in self._tracking_data.values():
                # Skip if conversation is too old
                if current_time - tracking_info.last_message_timestamp > max_age_seconds:
                    continue
                
                # Skip if not enough messages
                if tracking_info.message_count < min_messages:
                    continue
                
                # Include if never analyzed or has new messages since last analysis
                if (tracking_info.last_sentiment_analysis_timestamp is None or
                    tracking_info.last_message_timestamp > tracking_info.last_sentiment_analysis_timestamp):
                    conversations_for_analysis.append(tracking_info)
            
            return conversations_for_analysis

    def get_conversations_in_time_range(self, start_time: int, end_time: int) -> List[ConversationTrackingInfo]:
        """
        Get conversations that were active in a specific time range.

        Args:
            start_time: Start timestamp
            end_time: End timestamp

        Returns:
            List of conversations active in the time range
        """
        with self._lock:
            conversations_in_range = []
            
            for tracking_info in self._tracking_data.values():
                # Check if conversation was active during the time range
                if (tracking_info.last_message_timestamp >= start_time and
                    tracking_info.last_message_timestamp <= end_time):
                    conversations_in_range.append(tracking_info)
            
            return conversations_in_range

    def mark_conversation_completed(self, website_id: str, session_id: str):
        """
        Mark a conversation as completed.

        Args:
            website_id: Website ID
            session_id: Session ID
        """
        with self._lock:
            key = self._get_conversation_key(website_id, session_id)
            
            if key in self._tracking_data:
                if not self._tracking_data[key].is_completed:
                    self._tracking_data[key].is_completed = True
                    self._tracking_data[key].completion_timestamp = int(time.time())
                    self._save_tracking_data()
                    logger.info(f"Marked conversation {key} as completed")
            else:
                logger.warning(f"Attempted to mark unknown conversation as completed: {key}")

    def get_tracking_info(self, website_id: str, session_id: str) -> Optional[ConversationTrackingInfo]:
        """
        Get tracking information for a specific conversation.

        Args:
            website_id: Website ID
            session_id: Session ID

        Returns:
            ConversationTrackingInfo or None if not found
        """
        with self._lock:
            key = self._get_conversation_key(website_id, session_id)
            return self._tracking_data.get(key)

    def cleanup_old_conversations(self, max_age_days: int = 30):
        """
        Clean up tracking data for very old conversations.

        Args:
            max_age_days: Maximum age in days to keep tracking data
        """
        with self._lock:
            current_time = int(time.time())
            max_age_seconds = max_age_days * 24 * 3600
            
            keys_to_remove = []
            for key, tracking_info in self._tracking_data.items():
                if current_time - tracking_info.last_message_timestamp > max_age_seconds:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self._tracking_data[key]
            
            if keys_to_remove:
                self._save_tracking_data()
                logger.info(f"Cleaned up {len(keys_to_remove)} old conversation tracking records")

    def get_statistics(self) -> Dict[str, int]:
        """
        Get statistics about tracked conversations.

        Returns:
            Dictionary with statistics
        """
        with self._lock:
            total_conversations = len(self._tracking_data)
            completed_conversations = sum(1 for info in self._tracking_data.values() if info.is_completed)
            analyzed_conversations = sum(1 for info in self._tracking_data.values() 
                                       if info.last_sentiment_analysis_timestamp is not None)
            
            return {
                "total_conversations": total_conversations,
                "completed_conversations": completed_conversations,
                "ongoing_conversations": total_conversations - completed_conversations,
                "analyzed_conversations": analyzed_conversations,
                "unanalyzed_conversations": total_conversations - analyzed_conversations
            }

"""
Airflow Connections Configuration
Script to setup connections for Sentiment Analysis integration
"""

from airflow.models import Connection
from airflow import settings
import logging

logger = logging.getLogger(__name__)

def create_sentiment_api_connection():
    """Create HTTP connection to Sentiment Analysis API"""
    
    # Connection details
    conn_id = 'sentiment_api'
    conn_type = 'http'
    host = 'your-sentiment-server.com'  # Replace with your server
    port = 8000
    schema = 'http'  # or 'https' for production
    
    # Create connection
    connection = Connection(
        conn_id=conn_id,
        conn_type=conn_type,
        host=host,
        port=port,
        schema=schema,
        extra={
            'timeout': 30,
            'verify': True  # Set to False for self-signed certificates
        }
    )
    
    # Get session
    session = settings.Session()
    
    try:
        # Check if connection already exists
        existing_conn = session.query(Connection).filter(Connection.conn_id == conn_id).first()
        
        if existing_conn:
            logger.info(f"Connection {conn_id} already exists, updating...")
            existing_conn.host = host
            existing_conn.port = port
            existing_conn.schema = schema
            existing_conn.extra = connection.extra
        else:
            logger.info(f"Creating new connection {conn_id}...")
            session.add(connection)
        
        session.commit()
        logger.info(f"Connection {conn_id} configured successfully")
        
    except Exception as e:
        logger.error(f"Failed to create connection {conn_id}: {e}")
        session.rollback()
        raise
    finally:
        session.close()

def create_email_connection():
    """Create email connection for notifications"""
    
    conn_id = 'email_default'
    conn_type = 'email'
    
    connection = Connection(
        conn_id=conn_id,
        conn_type=conn_type,
        host='smtp.gmail.com',  # Replace with your SMTP server
        port=587,
        login='<EMAIL>',  # Replace with your email
        password='your-app-password',  # Replace with your password/app password
        extra={
            'use_tls': True,
            'use_ssl': False
        }
    )
    
    session = settings.Session()
    
    try:
        existing_conn = session.query(Connection).filter(Connection.conn_id == conn_id).first()
        
        if existing_conn:
            logger.info(f"Connection {conn_id} already exists, updating...")
            existing_conn.host = connection.host
            existing_conn.port = connection.port
            existing_conn.login = connection.login
            existing_conn.password = connection.password
            existing_conn.extra = connection.extra
        else:
            logger.info(f"Creating new connection {conn_id}...")
            session.add(connection)
        
        session.commit()
        logger.info(f"Connection {conn_id} configured successfully")
        
    except Exception as e:
        logger.error(f"Failed to create connection {conn_id}: {e}")
        session.rollback()
        raise
    finally:
        session.close()

def setup_all_connections():
    """Setup all required connections"""
    logger.info("Setting up Airflow connections for Sentiment Analysis...")
    
    try:
        create_sentiment_api_connection()
        create_email_connection()
        logger.info("All connections setup successfully")
    except Exception as e:
        logger.error(f"Failed to setup connections: {e}")
        raise

if __name__ == "__main__":
    setup_all_connections()

# No Flask dependency needed
import json
import re
import logging
import threading
import queue
import time
import concurrent.futures
import gc  # For garbage collection and object inspection
from transformers import pipeline
import torch
import traceback
from datetime import datetime

logger = logging.getLogger(__name__)

class Timer:
    """Utility class for measuring and logging execution times"""
    def __init__(self, name, log_level=logging.INFO):
        self.name = name
        self.start_time = None
        self.log_level = log_level
        self.checkpoints = []

        # Check if timer logging is disabled in the global config
        from app import sentiment_analyzer
        if sentiment_analyzer and hasattr(sentiment_analyzer, 'processing_config'):
            self.disable_logging = sentiment_analyzer.processing_config.get('disable_timer_logging', False)
        else:
            self.disable_logging = False

    def __enter__(self):
        self.start_time = time.time()
        if not self.disable_logging:
            logger.log(self.log_level, f"⏱️ Starting timer: {self.name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        elapsed = time.time() - self.start_time
        if not self.disable_logging:
            logger.log(self.log_level, f"⏱️ {self.name} completed in {elapsed:.4f} seconds")
            if self.checkpoints:
                logger.log(self.log_level, f"⏱️ {self.name} checkpoints:")
                for name, time_taken in self.checkpoints:
                    logger.log(self.log_level, f"   - {name}: {time_taken:.4f} seconds")

    def checkpoint(self, name):
        """Record a checkpoint with the given name"""
        if self.start_time is None:
            return
        elapsed = time.time() - self.start_time
        self.checkpoints.append((name, elapsed))
        if not self.disable_logging:
            logger.log(self.log_level, f"⏱️ {self.name} - {name}: {elapsed:.4f} seconds")
        return elapsed

class SentimentAnalysisService:
    """Service for sentiment analysis using the Qwen model"""

    # Common patterns for JSON extraction
    JSON_PATTERNS = [
        r'```json\s*(\{.*?\})\s*```',  # Markdown JSON code block
        r"'(\{.*?\})'|\"(\{.*?\})\"|'(\{.*?\})'|`(\{.*?\})`",  # Quoted JSON
        r'(\{\s*"[^"]+"\s*:\s*"[^"]+"\s*\})',  # Simple JSON object
    ]

    # Field patterns for direct value extraction
    FIELD_PATTERNS = {
        "overall_emotion": r'"overall_emotion"\s*:\s*"([^"]+)"',
        "sentiment_shift": r'"sentiment_shift"\s*:\s*"([^"]+)"',
        "latest_sentiment": r'"latest_sentiment"\s*:\s*"([^"]+)"'
    }

    # Resource tracking
    _active_threads = 0
    _active_threads_lock = threading.RLock()
    _gpu_in_use = False
    _gpu_lock = threading.RLock()
    _gpu_status = {}  # Dictionary to track individual GPU status: {gpu_id: is_in_use}
    _gpu_status_lock = threading.RLock()

    @classmethod
    def increment_active_threads(cls):
        """Increment the count of active threads and log the current count"""
        with cls._active_threads_lock:
            cls._active_threads += 1

            # Check if minimal logging is enabled in the global config
            from app import sentiment_analyzer
            if sentiment_analyzer and hasattr(sentiment_analyzer, 'processing_config'):
                minimal_logging = sentiment_analyzer.processing_config.get('minimal_logging', False)
                if not minimal_logging:
                    logger.info(f"🧵 Active CPU threads: {cls._active_threads}")
            else:
                logger.info(f"🧵 Active CPU threads: {cls._active_threads}")

    @classmethod
    def decrement_active_threads(cls):
        """Decrement the count of active threads and log the current count"""
        with cls._active_threads_lock:
            cls._active_threads = max(0, cls._active_threads - 1)  # Ensure it doesn't go below 0

            # Check if minimal logging is enabled in the global config
            from app import sentiment_analyzer
            if sentiment_analyzer and hasattr(sentiment_analyzer, 'processing_config'):
                minimal_logging = sentiment_analyzer.processing_config.get('minimal_logging', False)
                if not minimal_logging:
                    logger.info(f"🧵 Active CPU threads: {cls._active_threads}")
            else:
                logger.info(f"🧵 Active CPU threads: {cls._active_threads}")

    @classmethod
    def set_gpu_in_use(cls, in_use=True, gpu_id=None):
        """
        Set the GPU usage status and log it

        Args:
            in_use: Whether the GPU is in use
            gpu_id: Specific GPU ID to set status for. If None, sets global status.
        """
        # Check if minimal logging is enabled in the global config
        from app import sentiment_analyzer
        minimal_logging = False
        if sentiment_analyzer and hasattr(sentiment_analyzer, 'processing_config'):
            minimal_logging = sentiment_analyzer.processing_config.get('minimal_logging', False)

        # If specific GPU ID is provided, update that GPU's status
        if gpu_id is not None:
            with cls._gpu_status_lock:
                cls._gpu_status[gpu_id] = in_use

                # Also update global status if any GPU is in use
                any_gpu_in_use = any(cls._gpu_status.values())
                with cls._gpu_lock:
                    cls._gpu_in_use = any_gpu_in_use

                if not minimal_logging:
                    status = "in use" if in_use else "idle"
                    logger.info(f"🖥️ GPU {gpu_id} status: {status}")
        else:
            # Legacy behavior - set global status
            with cls._gpu_lock:
                cls._gpu_in_use = in_use

                if not minimal_logging:
                    status = "in use" if in_use else "idle"
                    logger.info(f"🖥️ GPU status: {status}")

    @classmethod
    def is_gpu_in_use(cls, gpu_id=None):
        """
        Check if the GPU is currently in use

        Args:
            gpu_id: Specific GPU ID to check status for. If None, checks global status.

        Returns:
            bool: Whether the GPU is in use
        """
        if gpu_id is not None:
            with cls._gpu_status_lock:
                return cls._gpu_status.get(gpu_id, False)
        else:
            with cls._gpu_lock:
                return cls._gpu_in_use

    @classmethod
    def get_available_gpu(cls, allowed_gpus=None):
        """
        Find an available GPU that is not in use

        Args:
            allowed_gpus: List of allowed GPU IDs to consider. If None, consider all tracked GPUs.

        Returns:
            int or None: ID of an available GPU, or None if all GPUs are in use
        """
        with cls._gpu_status_lock:
            # If allowed_gpus is provided, only consider those GPUs
            if allowed_gpus is not None:
                for gpu_id, in_use in cls._gpu_status.items():
                    # Only consider GPUs that are both allowed and not in use
                    if gpu_id in allowed_gpus and not in_use:
                        return gpu_id
            else:
                # Consider all tracked GPUs
                for gpu_id, in_use in cls._gpu_status.items():
                    if not in_use:
                        return gpu_id
            return None

    @classmethod
    def log_resource_usage(cls):
        """Log the current resource usage"""
        # Check if minimal logging is enabled in the global config
        from app import sentiment_analyzer
        if sentiment_analyzer and hasattr(sentiment_analyzer, 'processing_config'):
            minimal_logging = sentiment_analyzer.processing_config.get('minimal_logging', False)
            if minimal_logging:
                return  # Skip resource logging if minimal logging is enabled

        with cls._active_threads_lock, cls._gpu_lock:
            # Get CPU usage information
            try:
                import psutil
                cpu_percent = psutil.cpu_percent(interval=0.1)
                memory_percent = psutil.virtual_memory().percent
                cpu_info = f"CPU: {cpu_percent}%, Memory: {memory_percent}%"
            except ImportError:
                cpu_info = "CPU usage info not available (psutil not installed)"

            # Get GPU usage information if available
            gpu_info = ""
            if torch.cuda.is_available():
                try:
                    # Get the list of allowed GPUs from the service instance if available
                    # This is a bit of a hack to access the class instance's allowed_gpus attribute
                    # from a class method, but it works for our monitoring purposes
                    allowed_gpus = []
                    for instance in gc.get_objects():
                        if isinstance(instance, SentimentAnalysisService) and hasattr(instance, 'allowed_gpus'):
                            allowed_gpus = instance.allowed_gpus
                            break

                    # If no specific allowed_gpus found, monitor all GPUs
                    if not allowed_gpus:
                        gpu_count = torch.cuda.device_count()
                        gpus_to_monitor = list(range(gpu_count))
                    else:
                        # Only monitor the allowed GPUs
                        gpu_count = torch.cuda.device_count()
                        gpus_to_monitor = [idx for idx in allowed_gpus if idx < gpu_count]
                        logger.debug(f"Monitoring only allowed GPUs: {gpus_to_monitor} out of {gpu_count} total GPUs")

                    gpu_details = []

                    for i in gpus_to_monitor:
                        # Get GPU utilization if available
                        try:
                            # Try to get GPU utilization, but handle the case when pynvml is not installed
                            try:
                                utilization = torch.cuda.utilization(i)
                                memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # Convert to GB
                                memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)   # Convert to GB
                                gpu_details.append(f"GPU {i}: {utilization}% util, {memory_allocated:.2f}GB alloc, {memory_reserved:.2f}GB reserved")
                            except (AttributeError, RuntimeError, ModuleNotFoundError) as e:
                                if "pynvml does not seem to be installed" in str(e):
                                    # Fallback if pynvml is not installed
                                    memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # Convert to GB
                                    memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)   # Convert to GB
                                    gpu_details.append(f"GPU {i}: {memory_allocated:.2f}GB alloc, {memory_reserved:.2f}GB reserved (pynvml not installed)")
                                else:
                                    # Fallback if detailed stats not available
                                    memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # Convert to GB
                                    gpu_details.append(f"GPU {i}: {memory_allocated:.2f}GB allocated")
                        except Exception as e:
                            gpu_details.append(f"GPU {i}: error getting stats - {str(e)}")

                    gpu_info = ", ".join(gpu_details)
                    if not gpu_details:
                        gpu_info = "No monitored GPUs"
                except Exception as e:
                    gpu_info = f"GPU stats error: {str(e)}"

            # Log the resource usage
            if cls._gpu_in_use:
                logger.info(f"📊 Resource usage: GPU in use, {cls._active_threads} active CPU threads | {cpu_info} | {gpu_info}")
            else:
                logger.info(f"📊 Resource usage: GPU idle, {cls._active_threads} active CPU threads | {cpu_info} | {gpu_info}")

    def __init__(self, model_config, processing_config):
        """
        Initialize the sentiment analysis service

        Args:
            model_config: Model configuration
            processing_config: Processing configuration (batch or immediate)
        """
        self.model_config = model_config
        self.processing_config = processing_config
        self.pipe = None

        logger.info(f"{processing_config}")


        # Load prompts configuration once during initialization
        from app.core.config import Config
        self.config = Config()
        self.prompts = self.config.get_prompts()
        logger.info("Loaded prompts configuration during initialization")

        # Set up request queue with optional max size
        max_queue_size = processing_config.get('max_queue_size', 0)  # 0 means unlimited
        self.request_queue = queue.Queue(maxsize=max_queue_size)

        # Response storage and locks
        self.response_dict = {}
        self.response_dict_lock = threading.Lock()

        # Set to track requests that are being processed or have been processed
        self.processed_requests = set()
        self.processed_requests_lock = threading.Lock()

        # Request tracking
        self.total_requests_received = 0
        self.total_requests_processed = 0
        self.total_requests_failed = 0
        self.total_duplicate_requests = 0  # Track duplicate requests
        self.request_stats_lock = threading.Lock()

        # Processing mode
        self.processing_mode = processing_config.get('mode', 'batch')

        # Initialize thread pool and batch processor variables
        self.thread_pool = None
        self.batch_processor_thread = None

        # Flag to control background threads
        self.monitoring_active = True
        self.autosave_active = True

        # Start resource monitoring thread
        self.resource_monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.resource_monitor_thread.start()
        logger.info("Started resource monitoring thread")

        # Start auto-save thread if enabled
        if self.processing_config.get('autosave_enabled', True):
            self.autosave_thread = threading.Thread(target=self._autosave_data, daemon=True)
            self.autosave_thread.start()
            logger.info(f"Started auto-save thread with interval of {self.processing_config.get('autosave_interval_min', 5)} minutes")
        else:
            logger.info("Auto-save is disabled")
            self.autosave_thread = None

        # Initialize the model (this may change the processing mode based on hardware)
        self._initialize_model()

        # Initialize thread pool for immediate processing if needed
        # (This is now handled in _initialize_model to account for auto-detected processing mode)

        # The batch processor thread will be started in _initialize_model if needed
        # based on the final processing mode after auto-adjustment

        # Start the cleanup thread (needed in both modes)
        self.cleanup_thread = threading.Thread(target=self._cleanup_old_responses, daemon=True)
        self.cleanup_thread.start()

    def _initialize_model(self):
        """Initialize the model and configure processing based on available hardware"""
        with Timer("Model initialization") as timer:
            # Log the requested configuration
            logger.info(f"Requested configuration: device={self.model_config['device']}, processing_mode={self.processing_mode}, auto_adjust={self.processing_config.get('auto_adjust_mode', True)}")

            # Determine the best device to use
            device = self.model_config['device']

            # If processing mode is set to immediate and auto_adjust is disabled, prefer CPU
            if self.processing_mode == 'immediate' and not self.processing_config.get('auto_adjust_mode', True):
                logger.info("Immediate processing mode with auto_adjust disabled. Forcing CPU usage.")
                device = 'cpu'
            # Otherwise, if device is set to 'auto', detect the best available device
            elif device == 'auto':
                timer.checkpoint("Device detection start")
                if torch.cuda.is_available():
                    device = 'cuda'
                    logger.info(f"CUDA is available. Using GPU: {torch.cuda.get_device_name(0)}")
                else:
                    device = 'cpu'
                    logger.info("CUDA is not available. Using CPU.")
                timer.checkpoint("Device detection complete")

            # Log the device we're using
            logger.info(f"Loading model {self.model_config['name']} on {device}")

            # Initialize the model on the selected device
            timer.checkpoint("Model loading start")

            # Set PyTorch thread settings before loading the model
            if device == 'cpu':
                # Get the number of CPU cores
                import multiprocessing
                cpu_count = multiprocessing.cpu_count()

                # Configure PyTorch to use the specified number of CPU cores per request
                # Get the number of cores to allocate per request from config
                cores_per_request = self.processing_config.get('cpu_cores_per_request', 2)

                # If cores_per_request is 0, use all available CPU cores
                if cores_per_request == 0:
                    cores_per_request = cpu_count
                    logger.info(f"Using all available CPU cores ({cpu_count}) for maximum performance")
                else:
                    # Ensure the value is reasonable (between 1 and cpu_count)
                    cores_per_request = max(1, min(cores_per_request, cpu_count))

                # Get current thread count
                current_threads = torch.get_num_threads()
                logger.info(f"🧵 Current PyTorch threads before initialization: {current_threads}")

                # Set the number of threads for intra-op parallelism (within operations)
                torch.set_num_threads(cores_per_request)

                # Verify that the setting was applied correctly
                new_thread_count = torch.get_num_threads()
                if new_thread_count != cores_per_request:
                    logger.warning(f"⚠️ Failed to set PyTorch threads to {cores_per_request}, current value: {new_thread_count}")
                else:
                    logger.info(f"✅ Successfully set PyTorch threads to {cores_per_request}")

                # Set the number of threads for inter-op parallelism (between operations)
                if hasattr(torch, 'set_num_interop_threads'):
                    # For inter-op parallelism, use half the cores (minimum 1)
                    interop_threads = max(1, cores_per_request // 2)
                    torch.set_num_interop_threads(interop_threads)
                    logger.info(f"🧵 Set PyTorch inter-op threads to {interop_threads}")

                # Enable TensorFloat-32 (TF32) for faster computation on Ampere GPUs
                if hasattr(torch.backends.cuda, 'matmul') and hasattr(torch.backends.cudnn, 'allow_tf32'):
                    torch.backends.cuda.matmul.allow_tf32 = True
                    torch.backends.cudnn.allow_tf32 = True
                    logger.info("Enabled TensorFloat-32 for faster computation")

                # Enable cuDNN benchmark mode for faster training
                if hasattr(torch.backends, 'cudnn'):
                    torch.backends.cudnn.benchmark = True
                    logger.info("Enabled cuDNN benchmark mode for faster computation")

                logger.info(f"🧵 PyTorch configured to use {cores_per_request} CPU cores per request")

            # Check for GPU configuration and allowed GPUs
            elif device.startswith('cuda') or (device == 'auto' and torch.cuda.is_available()):
                # Get the list of allowed GPUs from config
                allowed_gpus = self.model_config.get('allowed_gpus', [])

                # Get total available GPUs
                total_gpu_count = torch.cuda.device_count()

                if allowed_gpus:
                    # Filter to only use allowed GPUs
                    valid_gpus = [idx for idx in allowed_gpus if idx < total_gpu_count]
                    if not valid_gpus:
                        logger.warning(f"None of the specified GPUs {allowed_gpus} are valid. Total available GPUs: {total_gpu_count}")
                        logger.warning("Falling back to CPU processing")
                        device = 'cpu'
                    else:
                        # Set CUDA_VISIBLE_DEVICES to restrict visible GPUs
                        import os
                        visible_gpus = ','.join(map(str, valid_gpus))
                        os.environ['CUDA_VISIBLE_DEVICES'] = visible_gpus
                        logger.info(f"🖥️ Restricting to allowed GPUs: {valid_gpus} (CUDA_VISIBLE_DEVICES={visible_gpus})")

                        # Refresh GPU count after setting CUDA_VISIBLE_DEVICES
                        # Note: This may not work as expected in all environments as CUDA context might already be initialized
                        # We'll handle this by explicitly tracking our allowed GPUs
                        gpu_count = len(valid_gpus)
                        device = 'cuda'  # Ensure we're using CUDA
                else:
                    # Use all available GPUs
                    valid_gpus = list(range(total_gpu_count))
                    gpu_count = total_gpu_count
                    logger.info(f"🖥️ No GPU restrictions specified. Using all {gpu_count} available GPUs")

                # Log GPU details for the GPUs we'll be using and initialize GPU status tracking
                for i in valid_gpus:
                    try:
                        gpu_name = torch.cuda.get_device_name(i)
                        gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                        logger.info(f"🖥️ GPU {i}: {gpu_name} with {gpu_memory:.2f} GB memory")

                        # Initialize GPU status as not in use
                        with SentimentAnalysisService._gpu_status_lock:
                            SentimentAnalysisService._gpu_status[i] = False
                    except Exception as e:
                        logger.warning(f"Could not get details for GPU {i}: {e}")

                # Set up device map for model loading across allowed GPUs
                if len(valid_gpus) > 1:
                    # For multiple GPUs, use device_map="auto" to distribute the model
                    device_map = "auto"
                    logger.info(f"🖥️ Using device_map='{device_map}' to distribute model across GPUs {valid_gpus}")
                else:
                    # For single GPU, specify the device directly
                    device_map = None
                    device = f"cuda:{valid_gpus[0]}" if valid_gpus else "cpu"
                    logger.info(f"🖥️ Using single GPU: {device}")
            else:
                # Single GPU or specific device
                device_map = None

            # Load the model with appropriate settings
            try:
                # Store the allowed GPUs for later reference
                self.allowed_gpus = self.model_config.get('allowed_gpus', [])

                # First try with device_map for multi-GPU support
                if device.startswith('cuda') and len(valid_gpus) > 1:
                    try:
                        logger.info(f"Attempting to load model with device_map for distribution across GPUs {valid_gpus}")
                        self.pipe = pipeline(
                            "text-generation",
                            model=self.model_config['name'],
                            device_map=device_map,  # Use auto device mapping for multiple GPUs
                            torch_dtype=self.model_config['torch_dtype']
                        )
                        logger.info(f"Successfully loaded model across GPUs {valid_gpus}")
                    except Exception as e:
                        logger.warning(f"Failed to load model with device_map: {e}")
                        logger.info("Falling back to single device loading")

                        # If multi-GPU loading fails, use the first allowed GPU
                        if valid_gpus:
                            single_device = f"cuda:{valid_gpus[0]}"
                            logger.info(f"Using single GPU: {single_device}")
                        else:
                            single_device = "cpu"
                            logger.warning("No valid GPUs available. Falling back to CPU.")

                        self.pipe = pipeline(
                            "text-generation",
                            model=self.model_config['name'],
                            device=single_device,
                            torch_dtype=self.model_config['torch_dtype']
                        )
                else:
                    # Standard loading for single device
                    logger.info(f"Loading model on device: {device}")
                    self.pipe = pipeline(
                        "text-generation",
                        model=self.model_config['name'],
                        device=device,
                        torch_dtype=self.model_config['torch_dtype']
                    )
            except Exception as e:
                logger.error(f"Error loading model: {e}")
                logger.error(traceback.format_exc())
                raise

            # Log model loading completion
            timer.checkpoint("Model loading complete")

            # Verify model device placement
            if hasattr(self.pipe.model, 'device'):
                logger.info(f"Model loaded on device: {self.pipe.model.device}")
            elif hasattr(self.pipe.model, 'hf_device_map'):
                logger.info(f"Model device map: {self.pipe.model.hf_device_map}")

        # Configure processing mode and infrastructure
        with Timer("Processing configuration") as timer:
            # Configure CPU threads regardless of auto-adjust setting
            if device == 'cpu':
                # On CPU, set up the thread count
                timer.checkpoint("CPU thread detection start")
                import multiprocessing
                cpu_count = multiprocessing.cpu_count()

                # Use the configured number of worker threads (default is 1)
                worker_threads = self.processing_config.get('cpu_threads', 1)
                cores_per_request = self.processing_config.get('cpu_cores_per_request', 2)

                # Verify that the worker_threads value is valid
                if worker_threads <= 0:
                    logger.warning(f"⚠️ Invalid cpu_threads value: {worker_threads}, setting to 1")
                    worker_threads = 1
                    # Update the config to avoid issues later
                    self.processing_config['cpu_threads'] = 1

                # If cores_per_request is 0, use all available CPU cores
                if cores_per_request == 0:
                    cores_per_request = cpu_count
                    # Verify that the setting was applied correctly
                    current_threads = torch.get_num_threads()
                    logger.info(f"🧵 Current PyTorch threads: {current_threads}, Target: {cores_per_request}")

                    if current_threads != cores_per_request:
                        logger.warning(f"⚠️ PyTorch thread count mismatch: current={current_threads}, target={cores_per_request}")
                        # Try to set it again
                        torch.set_num_threads(cores_per_request)
                        new_thread_count = torch.get_num_threads()
                        logger.info(f"🧵 After reset: PyTorch threads={new_thread_count}")

                    logger.info(f"Using {worker_threads} worker thread(s) for request processing")
                    logger.info(f"Each request will use ALL {cpu_count} CPU cores for maximum performance")
                else:
                    # Ensure the value is reasonable
                    cores_per_request = max(1, min(cores_per_request, cpu_count))

                    # Verify that the setting was applied correctly
                    current_threads = torch.get_num_threads()
                    logger.info(f"🧵 Current PyTorch threads: {current_threads}, Target: {cores_per_request}")

                    if current_threads != cores_per_request:
                        logger.warning(f"⚠️ PyTorch thread count mismatch: current={current_threads}, target={cores_per_request}")
                        # Try to set it again
                        torch.set_num_threads(cores_per_request)
                        new_thread_count = torch.get_num_threads()
                        logger.info(f"🧵 After reset: PyTorch threads={new_thread_count}")

                    logger.info(f"Using {worker_threads} worker thread(s) for request processing")
                    logger.info(f"Each request will use {cores_per_request} CPU cores for model inference (out of {cpu_count} available cores)")
                timer.checkpoint("CPU thread detection complete")

            # Only auto-adjust processing mode if enabled in config
            timer.checkpoint("Processing mode adjustment start")
            if self.processing_config.get('auto_adjust_mode', True):
                # If we're on CPU and in batch mode, switch to immediate for better performance
                if device == 'cpu' and self.processing_mode == 'batch':
                    logger.info("Running on CPU. Switching to immediate processing mode for better performance.")
                    self.processing_mode = 'immediate'

                # If we're on GPU and in immediate mode, switch to batch for better performance
                elif device.startswith('cuda') and self.processing_mode == 'immediate':
                    logger.info("Running on GPU. Switching to batch processing mode for better performance.")
                    self.processing_mode = 'batch'
            else:
                logger.info(f"Auto-adjust mode disabled. Keeping processing mode: {self.processing_mode}")
            timer.checkpoint("Processing mode adjustment complete")

            # Set up the appropriate processing infrastructure based on the final mode
            timer.checkpoint("Processing infrastructure setup start")
            if self.processing_mode == 'immediate' and device == 'cpu':
                # Initialize thread pool if not already done
                if self.thread_pool is None and self.processing_config.get('use_threadpool', True):
                    self.thread_pool = concurrent.futures.ThreadPoolExecutor(
                        max_workers=self.processing_config['cpu_threads'],
                        thread_name_prefix="sentiment-worker"
                    )
                    logger.info(f"Initialized thread pool with {self.processing_config['cpu_threads']} workers")

            elif self.processing_mode == 'batch' and self.batch_processor_thread is None:
                # Start the batch processor thread if not already running
                self.batch_processor_thread = threading.Thread(target=self._batch_processor, daemon=True)
                self.batch_processor_thread.start()
                logger.info("Started batch processor thread")
            timer.checkpoint("Processing infrastructure setup complete")

        # Log the final configuration and resource information
        logger.info(f"Model loaded successfully. Processing mode: {self.processing_mode}")

        # Log detailed resource configuration
        if device == 'cpu':
            import multiprocessing
            cpu_count = multiprocessing.cpu_count()
            worker_threads = self.processing_config.get('cpu_threads', 1)
            cores_per_request = self.processing_config.get('cpu_cores_per_request', 2)

            # If cores_per_request is 0, use all available CPU cores
            if cores_per_request == 0:
                cores_per_request = cpu_count
                logger.info(f"📊 Resource configuration: {worker_threads} worker thread(s), each request using ALL {cpu_count} CPU cores for maximum performance")
            else:
                # Ensure the value is reasonable
                cores_per_request = max(1, min(cores_per_request, cpu_count))
                logger.info(f"📊 Resource configuration: {worker_threads} worker thread(s), each request using {cores_per_request} CPU cores (out of {cpu_count} available cores)")
        elif device.startswith('cuda'):
            gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 0
            gpu_name = torch.cuda.get_device_name(0) if torch.cuda.is_available() else "Unknown"
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3) if torch.cuda.is_available() else 0
            logger.info(f"📊 Resource configuration: GPU {gpu_name} with {gpu_memory:.2f} GB memory")
            if gpu_count > 1:
                logger.info(f"📊 Multiple GPUs available: {gpu_count} devices")

        # Initialize resource tracking
        SentimentAnalysisService.log_resource_usage()

    def add_request(self, request_data):
        """
        Add a request for processing

        Args:
            request_data: Request data

        Returns:
            request_id: Unique request ID
        """
        with Timer("Request handling") as timer:
            # Generate a unique request ID
            request_id = f"{request_data['website_id']}_{request_data['session_id']}_{request_data['item_id']}_{int(time.time()*1000)}"
            timer.checkpoint("Request ID generation")

            # Prepare the request object
            request_obj = {
                'request_id': request_id,
                'item_id': request_data['item_id'],
                'session_id': request_data['session_id'],
                'website_id': request_data['website_id'],
                'timestamp': time.time(),
                'priority': request_data.get('priority', 0)  # Default priority is 0 (normal)
            }
            timer.checkpoint("Request object preparation")

            # Update request statistics
            with self.request_stats_lock:
                self.total_requests_received += 1
                current_queue_size = self.request_queue.qsize()

                # Log request statistics periodically (every 10 requests)
                if self.total_requests_received % 10 == 0:
                    logger.info(f"Request stats: {self.total_requests_received} received, "
                               f"{self.total_requests_processed} processed, "
                               f"{self.total_requests_failed} failed, "
                               f"{current_queue_size} in queue")

            # Check if we should apply rate limiting
            max_queue_size = self.processing_config.get('max_queue_size', 0)
            if max_queue_size > 0 and current_queue_size >= max_queue_size:
                # Queue is full, apply rate limiting strategy
                rate_limit_strategy = self.processing_config.get('rate_limit_strategy', 'reject')

                if rate_limit_strategy == 'reject':
                    # Reject the request
                    logger.warning(f"Request queue full ({current_queue_size}/{max_queue_size}). Rejecting request {request_id}")

                    # Store a rejection response
                    with self.response_dict_lock:
                        self.response_dict[request_id] = {
                            'result': {
                                "data": {
                                    "value": "queue_full"
                                }
                            },
                            'timestamp': time.time()
                        }
                    return request_id

                elif rate_limit_strategy == 'wait':
                    # Wait for space in the queue
                    wait_timeout = self.processing_config.get('queue_wait_timeout_sec', 5)
                    logger.warning(f"Request queue full. Waiting up to {wait_timeout}s for space to add request {request_id}")

                    try:
                        # Try to wait for space in the queue
                        space_available = False
                        wait_start = time.time()

                        while time.time() - wait_start < wait_timeout:
                            if self.request_queue.qsize() < max_queue_size:
                                space_available = True
                                break
                            time.sleep(0.1)  # Short sleep to avoid CPU spinning

                        if not space_available:
                            logger.warning(f"Timeout waiting for queue space. Rejecting request {request_id}")
                            # Store a timeout response
                            with self.response_dict_lock:
                                self.response_dict[request_id] = {
                                    'result': {
                                        "data": {
                                            "value": "queue_timeout"
                                        }
                                    },
                                    'timestamp': time.time()
                                }
                            return request_id
                    except Exception as e:
                        logger.error(f"Error while waiting for queue space: {e}")
                        # Continue with processing attempt

            # Check if we're using GPU and if there's an available GPU
            is_using_gpu = hasattr(self, 'pipe') and hasattr(self.pipe.model, 'device') and 'cuda' in str(self.pipe.model.device)
            available_gpu = None

            if is_using_gpu:
                # Get the allowed GPUs if available
                allowed_gpus = getattr(self, 'allowed_gpus', [])

                # Check for an available GPU among the allowed GPUs
                available_gpu = SentimentAnalysisService.get_available_gpu(allowed_gpus if allowed_gpus else None)

                # If we found an available GPU, process immediately regardless of mode
                if available_gpu is not None:
                    logger.info(f"🖥️ Found available GPU {available_gpu} from allowed GPUs {allowed_gpus}. Processing request {request_id} immediately.")

                    # Assign the GPU ID to the request object
                    request_obj['assigned_gpu_id'] = available_gpu

                    # Mark the GPU as in use
                    SentimentAnalysisService.set_gpu_in_use(True, available_gpu)

                    # Process in a thread pool if available, otherwise in the current thread
                    if self.thread_pool:
                        timer.checkpoint("Thread pool submission start (GPU available)")
                        self.thread_pool.submit(self._process_single_request, request_obj)
                        logger.info(f"Submitted request {request_id} to thread pool with assigned GPU {available_gpu}")
                        timer.checkpoint("Thread pool submission complete")
                        return request_id
                    else:
                        # Process in the current thread
                        timer.checkpoint("Direct processing start (GPU available)")
                        try:
                            self._process_single_request(request_obj)
                            timer.checkpoint("Direct processing complete")
                            return request_id
                        except Exception as e:
                            logger.error(f"Error processing request {request_id} immediately on GPU {available_gpu}: {e}")
                            timer.checkpoint("Direct processing failed")

                            # Update failed request count
                            with self.request_stats_lock:
                                self.total_requests_failed += 1

                            # Make sure to release the GPU
                            SentimentAnalysisService.set_gpu_in_use(False, available_gpu)
                            return request_id

            # If no GPU is available or we're not using GPU, proceed with normal processing logic
            # Process according to the configured mode
            if self.processing_mode == 'immediate':
                # Process immediately - do NOT add to queue
                logger.info(f"Processing request {request_id} immediately for {request_data['item_id']}")

                if self.thread_pool:
                    # Submit to thread pool for asynchronous processing
                    timer.checkpoint("Thread pool submission start")
                    self.thread_pool.submit(self._process_single_request, request_obj)
                    logger.info(f"Submitted request {request_id} to thread pool")
                    timer.checkpoint("Thread pool submission complete")
                else:
                    # Process in the current thread
                    timer.checkpoint("Direct processing start")
                    try:
                        self._process_single_request(request_obj)
                        timer.checkpoint("Direct processing complete")
                    except Exception as e:
                        logger.error(f"Error processing request {request_id} immediately: {e}")
                        timer.checkpoint("Direct processing failed")

                        # Update failed request count
                        with self.request_stats_lock:
                            self.total_requests_failed += 1
            else:
                # Add to queue for batch processing - do NOT process immediately
                timer.checkpoint("Queue addition start")
                try:
                    # Use non-blocking put with a timeout
                    put_timeout = self.processing_config.get('queue_put_timeout_sec', 2)
                    self.request_queue.put(request_obj, block=True, timeout=put_timeout)
                    logger.info(f"Added request {request_id} to queue for {request_data['item_id']}")
                    timer.checkpoint("Queue addition complete")
                except queue.Full:
                    logger.warning(f"Queue full, couldn't add request {request_id} within timeout")
                    # Store a queue full response
                    with self.response_dict_lock:
                        self.response_dict[request_id] = {
                            'result': {
                                "data": {
                                    "value": "queue_full"
                                }
                            },
                            'timestamp': time.time()
                        }
                    timer.checkpoint("Queue addition failed - queue full")

                    # Update failed request count
                    with self.request_stats_lock:
                        self.total_requests_failed += 1

            return request_id

    def get_result(self, request_id):
        """
        Get the result for a request

        Args:
            request_id: Request ID

        Returns:
            result: Result or None if not ready
        """
        # No logging or timing for this method to avoid excessive logs
        with self.response_dict_lock:
            if request_id in self.response_dict:
                result = self.response_dict.pop(request_id)['result']
                return result

        return None

    def _batch_processor(self):
        """Worker thread that processes sentiment analysis requests in batches"""
        logger.info("Starting batch processor thread")
        batch_interval = self.processing_config['batch_interval_sec']
        max_batch_size = self.processing_config.get('max_batch_size', 10)  # Default max batch size

        while True:
            try:
                # Wait for the batch processing interval
                time.sleep(batch_interval)

                # Collect all requests currently in the queue, up to max_batch_size
                batch = []
                batch_start_time = time.time()

                # Track queue size before and after batch collection
                queue_size_before = self.request_queue.qsize()

                # Collect requests up to max_batch_size
                while len(batch) < max_batch_size and not self.request_queue.empty():
                    try:
                        batch.append(self.request_queue.get_nowait())
                    except queue.Empty:
                        break

                if not batch:
                    # Log queue status periodically even when empty
                    if queue_size_before > 0:
                        logger.info(f"Batch processor cycle: queue had {queue_size_before} items but none could be retrieved")
                    continue  # Skip if no requests in the batch

                # Sort batch by priority (higher priority first) and then by timestamp (older first)
                batch.sort(key=lambda x: (-x.get('priority', 0), x.get('timestamp', 0)))

                # Calculate batch collection time
                batch_collection_time = time.time() - batch_start_time

                # Log batch size and resource usage before processing
                logger.info(f"Processing batch of {len(batch)} sentiment requests (from queue size {queue_size_before})")
                logger.info(f"Batch collection took {batch_collection_time:.4f} seconds")
                SentimentAnalysisService.log_resource_usage()

                # Set batch processing flag
                SentimentAnalysisService.increment_active_threads()  # Count the batch processor thread

                try:
                    # Process each request in the batch
                    batch_process_start = time.time()
                    successful_requests = 0
                    failed_requests = 0

                    for request_data in batch:
                        try:
                            request_id = request_data.get('request_id', 'unknown')
                            request_start_time = time.time()

                            # Process the request
                            self._process_single_request(request_data)

                            # Calculate and log processing time for this request
                            request_process_time = time.time() - request_start_time
                            logger.info(f"Batch request {request_id} processed in {request_process_time:.4f} seconds")

                            # Update statistics
                            with self.request_stats_lock:
                                self.total_requests_processed += 1
                                successful_requests += 1

                            # Mark the task as done
                            self.request_queue.task_done()
                        except Exception as e:
                            request_id = request_data.get('request_id', 'unknown')
                            logger.error(f"Error processing batch request {request_id}: {e}")
                            logger.error(traceback.format_exc())

                            # Update statistics
                            with self.request_stats_lock:
                                self.total_requests_failed += 1
                                failed_requests += 1

                            # Mark the task as done even if it failed
                            self.request_queue.task_done()

                    # Calculate total batch processing time
                    batch_process_time = time.time() - batch_process_start
                    avg_request_time = batch_process_time / len(batch) if batch else 0

                    # Log detailed batch processing statistics
                    logger.info(f"Completed batch processing of {len(batch)} requests in {batch_process_time:.4f} seconds")
                    logger.info(f"Batch results: {successful_requests} successful, {failed_requests} failed")
                    logger.info(f"Average request processing time: {avg_request_time:.4f} seconds")

                    # Log current queue status after processing
                    current_queue_size = self.request_queue.qsize()
                    logger.info(f"Current queue size after batch processing: {current_queue_size}")

                    # Log resource usage after batch processing
                    SentimentAnalysisService.log_resource_usage()
                finally:
                    # Always decrement the thread count
                    SentimentAnalysisService.decrement_active_threads()
            except Exception as e:
                logger.error(f"Error in batch processor: {e}")
                logger.error(traceback.format_exc())

    def _process_single_request(self, request_data):
        """
        Process a single sentiment analysis request immediately.
        This method is responsible for the high-level request handling:
        1. Extracting request data from the request object
        2. Retrieving conversation content from the database
        3. Preparing messages for the model with appropriate prompts
        4. Calling _generate_sentiment to perform the sentiment analysis
        5. Storing the result in the response dictionary for later retrieval
        6. Updating request statistics and logging

        This method does NOT directly interact with the model or process the model output.
        It delegates those responsibilities to _generate_sentiment.

        Args:
            request_data: Request data dictionary containing request_id, item_id, website_id, session_id
        """
        # Track active thread count
        SentimentAnalysisService.increment_active_threads()
        try:
            with Timer(f"Process request {request_data['request_id']}") as timer:
                try:
                    # Extract request data
                    request_id = request_data['request_id']
                    item_id = request_data['item_id']
                    website_id = request_data['website_id']
                    session_id = request_data['session_id']

                    # Check if this request has already been processed or is being processed
                    with self.processed_requests_lock:
                        if request_id in self.processed_requests:
                            # This request is already being processed or has been processed
                            logger.warning(f"Request {request_id} is already being processed or has been processed. Skipping duplicate processing.")

                            # Update duplicate request count
                            with self.request_stats_lock:
                                self.total_duplicate_requests += 1

                            return None

                        # Mark this request as being processed
                        self.processed_requests.add(request_id)

                    timer.checkpoint("Request data extraction")

                    # Get conversation content from database
                    from app import get_db
                    db = get_db()
                    timer.checkpoint("Database connection")

                    content = db.get_messages(website_id, session_id)
                    timer.checkpoint("Message retrieval")

                    # Check if there are any messages for this session
                    if not content:
                        logger.warning(f"No messages found for session {session_id} in website {website_id}")
                        # Return "No data is recorded" for all sentiment analysis types
                        result = {
                            "data": {
                                "value": "No data is recorded"
                            }
                        }

                        # Store the result with timestamp
                        with self.response_dict_lock:
                            self.response_dict[request_id] = {
                                'result': result,
                                'timestamp': time.time()
                            }

                        # Update processed request count
                        with self.request_stats_lock:
                            self.total_requests_processed += 1

                        logger.info(f"Processed request {request_id} for {item_id} with 'No data is recorded' response")
                        return result

                    # Prepare messages for the model using pre-loaded prompts
                    messages = [
                        {
                            "role": "system",
                            "content": "You are a helpful assistant that analyzes sentiment in conversations. The conversation is as follows:"
                        }
                    ]
                    messages += content
                    messages.append({
                        "role": "system",
                        "content": self.prompts["sentiment"][item_id]
                    })
                    timer.checkpoint("Message preparation")

                    # Log the messages being processed
                    logger.info(f"Processing request {request_id} with {len(content)} messages")

                    # Calculate and log conversation statistics
                    self._log_conversation_stats(content, request_id)

                    # Log a summary of the conversation content
                    if content:
                        # Log the first and last message if available
                        if len(content) >= 2:
                            first_msg = content[0]
                            last_msg = content[-1]
                            first_preview = first_msg.get('content', '')[:100] + '...' if len(first_msg.get('content', '')) > 100 else first_msg.get('content', '')
                            last_preview = last_msg.get('content', '')[:100] + '...' if len(last_msg.get('content', '')) > 100 else last_msg.get('content', '')

                            logger.info(f"First message ({first_msg.get('role', 'unknown')}): {first_preview}")
                            logger.info(f"Last message ({last_msg.get('role', 'unknown')}): {last_preview}")
                            logger.info(f"Analyzing for {item_id} with prompt: {self.prompts['sentiment'][item_id][:100]}...")
                        else:
                            # Only one message
                            msg = content[0]
                            preview = msg.get('content', '')[:100] + '...' if len(msg.get('content', '')) > 100 else msg.get('content', '')
                            logger.info(f"Single message ({msg.get('role', 'unknown')}): {preview}")
                            logger.info(f"Analyzing for {item_id} with prompt: {self.prompts['sentiment'][item_id][:100]}...")
                    else:
                        logger.warning(f"No conversation content found for request {request_id}")

                    # Generate sentiment analysis
                    start_time = time.time()
                    # Check if a specific GPU ID was assigned to this request
                    gpu_id = request_data.get('assigned_gpu_id', None)
                    result = self._generate_sentiment(messages, self.model_config['max_new_tokens'], item_id, gpu_id)
                    logger.info("-----------------------------------------------_generate_sentiment------------------------------------------------")
                    logger.info(result)
                    logger.info("-----------------------------------------------------------------------------------------------")
                    processing_time = time.time() - start_time
                    logger.info(f"--------------------------------------------{processing_time}---------------------------------------------------")

                    # Check if minimal logging is enabled
                    minimal_logging = self.processing_config.get('minimal_logging', False)

                    if not minimal_logging:
                        logger.info(f"Result analyzing for {item_id} with result: {result}...")
                        logger.info(f"Request {request_id} for {item_id} processed in {processing_time:.4f} seconds")
                    else:
                        # Even with minimal logging, we still want to log the result value
                        value = result.get('data', {}).get('value', 'unknown')
                        logger.info(f"Request {request_id} for {item_id} processed in {processing_time:.4f} seconds with result: {value}")

                    timer.checkpoint("Sentiment generation")

                    # Store the result with timestamp
                    with self.response_dict_lock:
                        self.response_dict[request_id] = {
                            'result': result,
                            'timestamp': time.time()
                        }
                    timer.checkpoint("Result storage")

                    # Log resource usage after processing only if minimal logging is disabled
                    if not minimal_logging:
                        SentimentAnalysisService.log_resource_usage()

                    # Update processed request count
                    with self.request_stats_lock:
                        self.total_requests_processed += 1

                    if not minimal_logging:
                        logger.info(f"Processed request {request_id} for {item_id} immediately")

                    # Return result and stop processing immediately
                    return result
                except Exception as e:
                    logger.error(f"Error in immediate processing: {e}")
                    logger.error(traceback.format_exc())

                    # Update failed request count
                    with self.request_stats_lock:
                        self.total_requests_failed += 1

                    # Store error result with more detailed error information
                    error_message = str(e)[:100] if str(e) else "Unknown error"  # Truncate long error messages
                    with self.response_dict_lock:
                        self.response_dict[request_id] = {
                            'result': {
                                "data": {
                                    "value": "error_processing",
                                    "error": error_message
                                }
                            },
                            'timestamp': time.time(),
                            'error': error_message
                        }
                    return None
        finally:
            # Always decrement the active thread count, even if an exception occurs
            SentimentAnalysisService.decrement_active_threads()

    def _monitor_resources(self):
        """Periodically monitor and log resource usage"""
        # Default monitoring interval is 30 seconds
        monitor_interval = self.processing_config.get('resource_monitor_interval_sec', 30)
        minimal_logging = self.processing_config.get('minimal_logging', False)

        if not minimal_logging:
            logger.info(f"Starting resource monitoring thread with interval of {monitor_interval} seconds")

        while self.monitoring_active:
            try:
                # Check if minimal logging is enabled
                minimal_logging = self.processing_config.get('minimal_logging', False)

                # Log current resource usage only if minimal logging is disabled
                if not minimal_logging:
                    SentimentAnalysisService.log_resource_usage()

                # Sleep for the monitoring interval
                time.sleep(monitor_interval)
            except Exception as e:
                logger.error(f"Error in resource monitoring thread: {e}")
                time.sleep(5)  # Sleep briefly before retrying

    def _log_conversation_stats(self, content, request_id):
        """
        Calculate and log statistics about the conversation

        Args:
            content: List of conversation messages
            request_id: The request ID for logging context
        """
        if not content:
            return

        try:
            # Count messages by role
            role_counts = {}
            total_chars = 0
            longest_message = 0
            shortest_message = float('inf')

            for msg in content:
                # Count by role
                role = msg.get('role', 'unknown')
                role_counts[role] = role_counts.get(role, 0) + 1

                # Calculate message length
                msg_content = msg.get('content', '')
                msg_length = len(msg_content)
                total_chars += msg_length

                # Track longest and shortest messages
                longest_message = max(longest_message, msg_length)
                shortest_message = min(shortest_message, msg_length)

            # Calculate averages
            avg_message_length = total_chars / len(content) if content else 0

            # Log the statistics
            stats_log = f"Conversation stats for {request_id}: "
            stats_log += f"{len(content)} messages total, "
            stats_log += ", ".join([f"{count} {role}" for role, count in role_counts.items()])
            stats_log += f", avg length: {avg_message_length:.1f} chars"
            stats_log += f", longest: {longest_message} chars"
            stats_log += f", shortest: {shortest_message} chars"

            logger.info(stats_log)
        except Exception as e:
            logger.warning(f"Error calculating conversation stats: {e}")

    def _autosave_data(self):
        """Periodically save data to persistent storage"""
        # Get auto-save interval in minutes and convert to seconds
        autosave_interval = self.processing_config.get('autosave_interval_min', 5) * 60
        logger.info(f"Starting auto-save thread with interval of {self.processing_config.get('autosave_interval_min', 5)} minutes")

        while self.autosave_active:
            try:
                # Sleep first to allow initial data collection
                time.sleep(autosave_interval)

                # Perform the auto-save
                with Timer("Auto-save data") as timer:
                    # Get the database instance
                    from app import get_db
                    db = get_db()
                    timer.checkpoint("Database connection")

                    # Save the database
                    if hasattr(db, 'save_to_disk'):
                        db.save_to_disk()
                        timer.checkpoint("Database save")
                        logger.info(f"Auto-saved data at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    else:
                        logger.warning("Database does not have a save_to_disk method. Auto-save skipped.")

                    # Save any pending responses
                    with self.response_dict_lock:
                        response_count = len(self.response_dict)
                        timer.checkpoint("Response count")

                    logger.info(f"Auto-save complete. Current pending responses: {response_count}")
            except Exception as e:
                logger.error(f"Error in auto-save thread: {e}")
                logger.error(traceback.format_exc())
                time.sleep(60)  # Sleep for a minute before retrying

    def _cleanup_old_responses(self):
        """Periodically clean up old responses that were never retrieved"""
        cleanup_interval_min = self.processing_config.get('cleanup_interval_min', 5)
        response_timeout_min = self.processing_config.get('response_timeout_min', 30)

        cleanup_interval = cleanup_interval_min * 60  # Convert minutes to seconds
        response_timeout = response_timeout_min * 60  # Convert minutes to seconds

        logger.info(f"Starting cleanup thread with interval of {cleanup_interval_min} minutes")
        logger.info(f"Responses older than {response_timeout_min} minutes will be removed")

        while self.monitoring_active:
            try:
                time.sleep(cleanup_interval)
                current_time = time.time()

                with self.response_dict_lock:
                    keys_to_remove = []
                    for request_id, response in self.response_dict.items():
                        # If a response is older than the configured timeout, remove it
                        if 'timestamp' in response and current_time - response['timestamp'] > response_timeout:
                            keys_to_remove.append(request_id)

                    for key in keys_to_remove:
                        self.response_dict.pop(key)

                    if keys_to_remove:
                        logger.info(f"Cleaned up {len(keys_to_remove)} old responses")
            except Exception as e:
                logger.error(f"Error in cleanup thread: {e}")
                time.sleep(60)  # Wait 1 minute before retrying after an error

    def get_queue_status(self):
        """
        Get the current status of the request queue and processing statistics

        Returns:
            dict: Queue status information
        """
        with self.request_stats_lock, SentimentAnalysisService._gpu_status_lock:
            # Get individual GPU status
            gpu_status = {}
            for gpu_id, in_use in SentimentAnalysisService._gpu_status.items():
                gpu_status[str(gpu_id)] = in_use

            status = {
                'queue_size': self.request_queue.qsize(),
                'total_requests_received': self.total_requests_received,
                'total_requests_processed': self.total_requests_processed,
                'total_requests_failed': self.total_requests_failed,
                'total_duplicate_requests': self.total_duplicate_requests,
                'pending_responses': len(self.response_dict),
                'tracked_requests': len(self.processed_requests),
                'processing_mode': self.processing_mode,
                'active_threads': SentimentAnalysisService._active_threads,
                'gpu_in_use': SentimentAnalysisService._gpu_in_use,
                'gpu_status': gpu_status,
                'timestamp': time.time()
            }

            # Calculate success rate
            if self.total_requests_received > 0:
                status['success_rate'] = (self.total_requests_processed / self.total_requests_received) * 100
            else:
                status['success_rate'] = 0

            return status

    def shutdown(self):
        """Properly shut down the service and save any pending data"""
        logger.info("Shutting down sentiment analysis service...")

        # Stop background threads
        self.monitoring_active = False
        self.autosave_active = False

        # Perform a final save
        try:
            logger.info("Performing final data save before shutdown...")
            from app import get_db
            db = get_db()

            if hasattr(db, 'save_to_disk'):
                db.save_to_disk()
                logger.info(f"Final data save completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                logger.warning("Database does not have a save_to_disk method. Final save skipped.")

            # Log any pending responses that weren't processed
            with self.response_dict_lock:
                response_count = len(self.response_dict)
                if response_count > 0:
                    logger.warning(f"Shutting down with {response_count} pending responses")
                else:
                    logger.info("No pending responses at shutdown")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
            logger.error(traceback.format_exc())

        logger.info("Sentiment analysis service shutdown complete")

    def _generate_response(self, messages, max_new_tokens, gpu_id=None):
        """
        Generate a raw response from the model using the provided messages.
        This method is responsible ONLY for calling the model and returning the raw response.
        It does not handle any post-processing or JSON extraction.

        Args:
            messages: Messages to generate a response for
            max_new_tokens: Maximum number of tokens to generate
            gpu_id: Specific GPU ID to use for inference. If None, uses the default device.

        Returns:
            result: Raw generated response from the model
        """
        # Check if we're using GPU
        is_gpu = hasattr(self, 'pipe') and hasattr(self.pipe.model, 'device') and 'cuda' in str(self.pipe.model.device)

        # Get the current device
        current_device = str(self.pipe.model.device) if hasattr(self.pipe.model, 'device') else "unknown"

        # Set GPU status if using GPU
        if is_gpu:
            # If a specific GPU ID is provided, use that for status tracking
            if gpu_id is not None:
                SentimentAnalysisService.set_gpu_in_use(True, gpu_id)
                logger.info(f"🖥️ Using specific GPU {gpu_id} for inference, device: {current_device}")
            else:
                # Try to extract GPU ID from the device string (e.g., "cuda:0" -> 0)
                try:
                    if ":" in current_device:
                        extracted_gpu_id = int(current_device.split(":")[-1])
                        SentimentAnalysisService.set_gpu_in_use(True, extracted_gpu_id)
                        logger.info(f"🖥️ Using GPU {extracted_gpu_id}, device: {current_device}")
                    else:
                        # Default to global GPU status if we can't extract a specific ID
                        SentimentAnalysisService.set_gpu_in_use(True)
                        logger.info(f"🖥️ GPU device: {current_device}")
                except (ValueError, IndexError):
                    # Fall back to global GPU status if parsing fails
                    SentimentAnalysisService.set_gpu_in_use(True)
                    logger.info(f"🖥️ GPU device: {current_device}")
        else:
            # For CPU, verify that we're using the configured number of cores
            cores_per_request = self.processing_config.get('cpu_cores_per_request', 2)
            current_threads = torch.get_num_threads()

            # If cores_per_request is 0, use all available CPU cores
            import multiprocessing
            cpu_count = multiprocessing.cpu_count()

            if cores_per_request == 0:
                cores_per_request = cpu_count

            # If the current thread count doesn't match our configuration, reset it
            if current_threads != cores_per_request:
                # Ensure the value is reasonable
                cores_per_request = max(1, min(cores_per_request, cpu_count))

                # Log the current and target thread count
                logger.info(f"🧵 Current PyTorch threads: {current_threads}, Target: {cores_per_request}")

                # Set the number of threads for intra-op parallelism (within operations)
                torch.set_num_threads(cores_per_request)

                # Verify that the setting was applied correctly
                new_thread_count = torch.get_num_threads()
                if new_thread_count != cores_per_request:
                    logger.warning(f"⚠️ Failed to set PyTorch threads to {cores_per_request}, current value: {new_thread_count}")

                # Set the number of threads for inter-op parallelism (between operations)
                if hasattr(torch, 'set_num_interop_threads'):
                    # For inter-op parallelism, use half the cores (minimum 1)
                    interop_threads = max(1, cores_per_request // 2)
                    torch.set_num_interop_threads(interop_threads)
                    logger.info(f"🧵 Set PyTorch inter-op threads to {interop_threads}")

                # Enable TensorFloat-32 (TF32) for faster computation on Ampere GPUs
                if hasattr(torch.backends.cuda, 'matmul') and hasattr(torch.backends.cudnn, 'allow_tf32'):
                    torch.backends.cuda.matmul.allow_tf32 = True
                    torch.backends.cudnn.allow_tf32 = True

                # Enable cuDNN benchmark mode for faster training
                if hasattr(torch.backends, 'cudnn'):
                    torch.backends.cudnn.benchmark = True

                if cores_per_request == cpu_count:
                    logger.info(f"🧵 Reset PyTorch to use ALL {cores_per_request} CPU cores for maximum performance")
                else:
                    logger.info(f"🧵 Reset PyTorch to use {cores_per_request} CPU cores for this request")

        try:
            with Timer("Model inference") as timer:
                logger.info("Generating sentiment analysis response")

                # Record the start time for precise measurement
                start_time = time.time()

                # Call the model pipeline with optimized settings
                # Prepare inference parameters with optimizations for faster inference
                inference_params = {
                    'max_new_tokens': max_new_tokens,
                    'do_sample': False,  # Use greedy decoding for deterministic results
                    'temperature': 0.1,  # Low temperature for more focused outputs
                    'return_full_text': False,  # Only return the generated text, not the prompt
                    'use_cache': True,  # Enable KV cache for faster generation
                    'pad_token_id': self.pipe.tokenizer.pad_token_id if hasattr(self.pipe, 'tokenizer') and hasattr(self.pipe.tokenizer, 'pad_token_id') else None
                }

                # Add performance optimization parameters
                if is_gpu:
                    # For GPU inference, use optimized settings
                    # Get the allowed GPUs if available
                    allowed_gpus = getattr(self, 'allowed_gpus', [])

                    if allowed_gpus:
                        # Only use the allowed GPUs
                        gpu_count = len(allowed_gpus)
                        total_gpu_count = torch.cuda.device_count()
                        gpus_to_monitor = [idx for idx in allowed_gpus if idx < total_gpu_count]
                        logger.info(f"🖥️ Using {len(gpus_to_monitor)} allowed GPU(s) for inference out of {gpu_count} configured: {gpus_to_monitor}")

                        # Make sure we're only tracking the allowed GPUs
                        with SentimentAnalysisService._gpu_status_lock:
                            # Remove any GPUs that are not in the allowed list
                            for gpu_id in list(SentimentAnalysisService._gpu_status.keys()):
                                if gpu_id not in allowed_gpus:
                                    del SentimentAnalysisService._gpu_status[gpu_id]
                                    logger.info(f"🖥️ Removed GPU {gpu_id} from tracking as it's not in the allowed list")
                    else:
                        # Use all available GPUs
                        gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 1
                        gpus_to_monitor = list(range(gpu_count))
                        logger.info(f"🖥️ Using all {gpu_count} available GPU(s) for inference: {gpus_to_monitor}")

                    # Log utilization of the GPUs we're using
                    for i in gpus_to_monitor:
                        if torch.cuda.is_available() and i < torch.cuda.device_count():
                            try:
                                # Try to get GPU utilization, but handle the case when pynvml is not installed
                                try:
                                    utilization = torch.cuda.utilization(i)
                                    logger.info(f"🖥️ GPU {i} utilization: {utilization}%")
                                except (AttributeError, RuntimeError, ModuleNotFoundError) as e:
                                    if "pynvml does not seem to be installed" in str(e):
                                        logger.warning(f"pynvml not installed, cannot get GPU utilization. Install with 'pip install pynvml'")
                                    else:
                                        logger.warning(f"Could not get utilization for GPU {i}: {e}")

                                # Memory info should still be available without pynvml
                                memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # Convert to GB
                                memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)  # Convert to GB
                                logger.info(f"🖥️ GPU {i} memory: allocated={memory_allocated:.2f}GB, reserved={memory_reserved:.2f}GB")
                            except Exception as e:
                                logger.warning(f"Could not get memory info for GPU {i}: {e}")
                else:
                    # For CPU inference, ensure we're using the configured number of threads
                    thread_count = torch.get_num_threads()
                    import multiprocessing
                    cpu_count = multiprocessing.cpu_count()

                    if thread_count == cpu_count:
                        logger.info(f"🧵 Using ALL {thread_count} CPU cores for maximum performance")
                    else:
                        logger.info(f"🧵 Using {thread_count} CPU threads for inference (out of {cpu_count} available)")

                start_times = time.time()
                # Execute the model inference
                response = self.pipe(messages, **inference_params)
                logger.info("-------------------------------------------response-----------------------------------------------")
                logger.info(f"-------------------------------------------{response}-----------------------------------------------")

                # Calculate and log the exact inference time
                inference_time = time.time() - start_time
                logger.info(f"-------------------------------------------{inference_time}-----------------------------------------------")
                logger.info(f"-------------------------------------------{time.time()-start_times}-----------------------------------------------")

                # Check if minimal logging is enabled
                minimal_logging = self.processing_config.get('minimal_logging', False)

                if not minimal_logging:
                    logger.info(f"⏱️ Model inference took exactly {inference_time:.4f} seconds")
                timer.checkpoint("Model pipeline execution")

                # Log a snippet of the response for debugging only if minimal logging is disabled
                if not minimal_logging:
                    try:
                        # Handle different response formats
                        if isinstance(response, list) and len(response) > 0:
                            if isinstance(response[-1], dict):
                                if 'generated_text' in response[-1]:
                                    generated_text = response[-1]['generated_text']

                                    # Check if generated_text is a list or a string
                                    if isinstance(generated_text, list) and len(generated_text) > 0:
                                        if isinstance(generated_text[-1], dict) and 'content' in generated_text[-1]:
                                            content_snippet = generated_text[-1]['content']
                                        else:
                                            content_snippet = str(generated_text[-1])
                                    else:
                                        content_snippet = str(generated_text)
                                else:
                                    content_snippet = str(response[-1])
                            else:
                                content_snippet = str(response[-1])
                        else:
                            content_snippet = str(response)

                        if len(content_snippet) > 100:
                            content_snippet = content_snippet[:100] + "..."
                        logger.info(f"Model response snippet: {content_snippet}")
                    except Exception as e:
                        logger.warning(f"Could not extract response snippet: {e}")

                timer.checkpoint("Response extraction")

                # Log resource usage after inference
                SentimentAnalysisService.log_resource_usage()

                return response
        finally:
            # Reset GPU status if using GPU
            if is_gpu:
                # If a specific GPU ID is provided, use that for status tracking
                if gpu_id is not None:
                    SentimentAnalysisService.set_gpu_in_use(False, gpu_id)
                else:
                    # Try to extract GPU ID from the device string (e.g., "cuda:0" -> 0)
                    try:
                        if ":" in current_device:
                            extracted_gpu_id = int(current_device.split(":")[-1])
                            SentimentAnalysisService.set_gpu_in_use(False, extracted_gpu_id)
                        else:
                            # Default to global GPU status if we can't extract a specific ID
                            SentimentAnalysisService.set_gpu_in_use(False)
                    except (ValueError, IndexError):
                        # Fall back to global GPU status if parsing fails
                        SentimentAnalysisService.set_gpu_in_use(False)

    def _extract_text_from_response(self, response):
        """
        Extract text content from the model response.
        This method handles different response formats and extracts the text content.

        Args:
            response: Raw response from the model

        Returns:
            str: Extracted text content
        """
        try:
            # Handle different response formats
            if isinstance(response, list) and len(response) > 0:
                if isinstance(response[-1], dict):
                    if 'generated_text' in response[-1]:
                        generated_text = response[-1]['generated_text']

                        # Check if generated_text is a list or a string
                        if isinstance(generated_text, list) and len(generated_text) > 0:
                            if isinstance(generated_text[-1], dict) and 'content' in generated_text[-1]:
                                return generated_text[-1]['content']
                            else:
                                return str(generated_text[-1])
                        else:
                            return str(generated_text)
                    else:
                        return str(response[-1])
                else:
                    return str(response[-1])
            else:
                return str(response)
        except Exception as e:
            logger.error(f"Error extracting response content: {e}")
            logger.error(f"Response structure: {type(response)}")
            if isinstance(response, list):
                logger.error(f"Response first element type: {type(response[0]) if len(response) > 0 else 'empty list'}")
            return str(response)

    def _extract_json(self, text, item_id):
        """
        Extract JSON from text using multiple patterns

        Args:
            text: Text to extract JSON from
            item_id: Type of sentiment analysis (used for direct value extraction)

        Returns:
            dict: Extracted JSON as a Python dictionary, or None if extraction failed
        """
        with Timer(f"JSON extraction for {item_id}") as timer:
            # Log the first 200 characters of the text for debugging
            log_text = text[:200] + "..." if len(text) > 200 else text
            logger.debug(f"Extracting JSON for {item_id} from: {log_text}")

            # Try multiple patterns to extract JSON
            json_part = None
            pattern_used = None

            # Try each pattern in order
            timer.checkpoint("Pattern matching start")
            for i, pattern in enumerate(self.JSON_PATTERNS):
                match = re.search(pattern, text, re.DOTALL)
                if match:
                    # Find the first non-None group
                    for group in match.groups() if match.groups() else [match.group(1)]:
                        if group is not None:
                            json_part = group
                            pattern_used = f"pattern_{i+1}"
                            logger.debug(f"Extracted JSON using {pattern_used}: {json_part[:50]}...")
                            break
                    if json_part:
                        break
            timer.checkpoint("Pattern matching complete")

            # If no JSON found, try to extract just the value
            if not json_part and item_id in self.FIELD_PATTERNS:
                timer.checkpoint("Direct value extraction start")
                match = re.search(self.FIELD_PATTERNS[item_id], text)
                if match:
                    value = match.group(1)
                    logger.debug(f"Extracted value directly: {value}")
                    # Construct a JSON object with the extracted value
                    json_part = f'{{"{item_id}": "{value}"}}'
                    pattern_used = "direct_value"
                timer.checkpoint("Direct value extraction complete")

            if not json_part:
                logger.error(f"Failed to extract JSON from text for {item_id}")
                return None

            # Clean up the JSON string
            timer.checkpoint("JSON cleanup start")
            json_part = json_part.strip()
            # Replace any escaped quotes
            json_part = json_part.replace('\\"', '"')
            # Ensure proper quotes for JSON
            json_part = json_part.replace("'", '"')
            timer.checkpoint("JSON cleanup complete")

            # Parse the JSON
            timer.checkpoint("JSON parsing start")
            try:
                result = json.loads(json_part)
                logger.info(f"Successfully parsed JSON using {pattern_used}")
                timer.checkpoint("JSON parsing successful")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON: {e}, JSON string: {json_part}")
                # Try one more time with a more aggressive cleanup
                try:
                    timer.checkpoint("Aggressive cleanup start")
                    # Remove any non-JSON characters
                    cleaned_json = re.sub(r'[^\{\}\[\]\,\:"\s\w\.\-]', '', json_part)
                    result = json.loads(cleaned_json)
                    logger.info(f"Successfully parsed JSON after aggressive cleanup")
                    timer.checkpoint("Aggressive cleanup successful")
                    return result
                except json.JSONDecodeError as e2:
                    logger.error(f"Failed to parse JSON after cleanup: {e2}")
                    timer.checkpoint("JSON parsing failed")
                    return None

    def _generate_sentiment(self, messages, max_new_tokens, item_id, gpu_id=None):
        """
        Generate sentiment analysis for the given messages and item_id.
        This method is responsible for the complete pipeline of sentiment analysis:
        1. Calling _generate_response to get the raw model response
        2. Calling _extract_text_from_response to extract the text content
        3. Calling _extract_json to parse the JSON from the response
        4. Formatting the final response based on the item_id

        This method serves as a bridge between the raw model output and the formatted response.

        Args:
            messages: Messages to analyze
            max_new_tokens: Maximum number of tokens to generate
            item_id: Type of sentiment analysis to perform (overall_emotion, sentiment_shift, latest_sentiment)
            gpu_id: Specific GPU ID to use for inference. If None, uses the default device.

        Returns:
            result: Formatted sentiment analysis result in the expected response format
        """
        # Check if detailed logging is enabled
        detailed_logging = self.processing_config.get('detailed_message_logging', False)

        # Log the full conversation if detailed logging is enabled
        if detailed_logging:
            logger.info(f"\n{'='*50}\nDETAILED MESSAGE LOG FOR {item_id}\n{'='*50}")
            for i, msg in enumerate(messages):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                logger.info(f"Message {i+1} ({role}):\n{content}\n{'-'*50}")
            logger.info(f"{'='*50}\nEND OF DETAILED MESSAGE LOG\n{'='*50}")

        with Timer(f"Generate sentiment for {item_id}") as timer:
            try:
                # Generate response from the model
                timer.checkpoint("Model inference start")
                result = self._generate_response(messages, max_new_tokens, gpu_id)
                timer.checkpoint("Model inference complete")

                # Extract text content from the response
                response_content = self._extract_text_from_response(result)

                # Extract JSON from the response using our helper method
                timer.checkpoint("JSON extraction start")
                final_result = self._extract_json(response_content, item_id)
                timer.checkpoint("JSON extraction complete")

                if not final_result:
                    logger.error(f"Failed to extract valid JSON for {item_id}")
                    return {
                        "data": {
                            "value": "error_parsing"
                        }
                    }

                logger.info(f"Sentiment analysis result for {item_id}: {final_result}")

                # Return the appropriate response based on item_id
                timer.checkpoint("Response formatting start")
                response_data = None

                if item_id == "overall_emotion":
                    if "overall_emotion" not in final_result:
                        logger.error("Missing 'overall_emotion' in result")
                        response_data = {
                            "data": {
                                "value": "error_missing_field"
                            }
                        }
                    else:
                        response_data = {
                            "data": {
                                "value": final_result["overall_emotion"]
                            }
                        }
                elif item_id == "sentiment_shift":
                    if "sentiment_shift" not in final_result:
                        logger.error("Missing 'sentiment_shift' in result")
                        response_data = {
                            "data": {
                                "value": "error_missing_field"
                            }
                        }
                    else:
                        response_data = {
                            "data": {
                                "value": final_result["sentiment_shift"]
                            }
                        }
                elif item_id == "latest_sentiment":
                    if "latest_sentiment" not in final_result:
                        logger.error("Missing 'latest_sentiment' in result")
                        response_data = {
                            "data": {
                                "value": "error_missing_field"
                            }
                        }
                    else:
                        response_data = {
                            "data": {
                                "value": final_result["latest_sentiment"]
                            }
                        }
                else:
                    logger.error(f"Unknown item_id: {item_id}")
                    response_data = {
                        "data": {
                            "value": "error_unknown_item"
                        }
                    }

                timer.checkpoint("Response formatting complete")
                return response_data
            except Exception as e:
                logger.error(f"Error in sentiment_generation: {e}")
                # Log the full traceback for debugging
                logger.error(traceback.format_exc())
                return {
                    "data": {
                        "value": "error_processing"
                    }
                }

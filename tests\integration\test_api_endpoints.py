"""
Integration tests for API endpoints
"""

import pytest
import requests
import time
import json
from typing import Dict, Any


class TestAPIEndpoints:
    """Test API endpoints integration"""
    
    @pytest.fixture(scope="class")
    def base_url(self) -> str:
        """Base URL for API testing"""
        return "http://localhost:8000"
    
    @pytest.fixture(scope="class")
    def test_data(self) -> Dict[str, Any]:
        """Test data for sentiment analysis"""
        return {
            "website_id": "test-website-integration",
            "session_id": "test-session-integration",
            "messages": [
                {"role": "user", "content": "Hello, I need help with my order"},
                {"role": "operator", "content": "Hi! I'd be happy to help you with your order."},
                {"role": "user", "content": "Thank you, that was very helpful!"}
            ]
        }
    
    def test_health_endpoint(self, base_url: str):
        """Test health endpoint"""
        response = requests.get(f"{base_url}/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
    
    def test_api_health_endpoint(self, base_url: str):
        """Test API health endpoint"""
        response = requests.get(f"{base_url}/api/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
    
    def test_sentiment_analysis_endpoint(self, base_url: str, test_data: Dict[str, Any]):
        """Test sentiment analysis endpoint"""
        response = requests.post(
            f"{base_url}/sentiment_widget/action",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        # Should return 200 (immediate) or 202 (async processing)
        assert response.status_code in [200, 202]
        
        data = response.json()
        
        if response.status_code == 200:
            # Immediate response
            assert "overall_emotion" in data
            assert "sentiment_shift" in data
            assert "latest_sentiment" in data
        else:
            # Async response
            assert "request_id" in data
            assert "status" in data
            
            # Test status endpoint
            request_id = data["request_id"]
            self._test_sentiment_status(base_url, request_id)
    
    def _test_sentiment_status(self, base_url: str, request_id: str):
        """Test sentiment status endpoint"""
        max_attempts = 10
        for attempt in range(max_attempts):
            response = requests.get(f"{base_url}/sentiment_status/{request_id}")
            assert response.status_code in [200, 202]
            
            data = response.json()
            
            if response.status_code == 200:
                # Processing completed
                assert "overall_emotion" in data
                assert "sentiment_shift" in data
                assert "latest_sentiment" in data
                break
            else:
                # Still processing
                assert "status" in data
                time.sleep(2)
        else:
            pytest.fail(f"Sentiment analysis did not complete after {max_attempts} attempts")
    
    def test_webhook_endpoint(self, base_url: str):
        """Test webhook endpoint"""
        webhook_data = {
            "website_id": "test-website-webhook",
            "session_id": "test-session-webhook",
            "event": "message:received",
            "data": {
                "message": {
                    "role": "user",
                    "content": "Test webhook message"
                }
            }
        }
        
        response = requests.post(
            f"{base_url}/webhook",
            json=webhook_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
    
    def test_sentiment_widget_callback(self, base_url: str):
        """Test sentiment widget callback endpoint"""
        response = requests.get(f"{base_url}/sentiment_widget/callback")
        assert response.status_code == 200
    
    def test_sentiment_widget_setting(self, base_url: str):
        """Test sentiment widget setting endpoint"""
        response = requests.get(f"{base_url}/sentiment_widget/setting")
        assert response.status_code == 200
    
    def test_invalid_endpoint(self, base_url: str):
        """Test invalid endpoint returns 404"""
        response = requests.get(f"{base_url}/invalid/endpoint")
        assert response.status_code == 404
    
    def test_rate_limiting(self, base_url: str):
        """Test rate limiting (if enabled)"""
        # Make multiple rapid requests
        responses = []
        for _ in range(20):
            response = requests.get(f"{base_url}/health")
            responses.append(response.status_code)
        
        # Should mostly succeed, but might have some rate limited
        success_count = sum(1 for status in responses if status == 200)
        assert success_count >= 15  # Allow some rate limiting
    
    def test_large_payload(self, base_url: str):
        """Test handling of large payloads"""
        large_data = {
            "website_id": "test-website-large",
            "session_id": "test-session-large",
            "messages": [
                {
                    "role": "user",
                    "content": "This is a very long message. " * 100
                },
                {
                    "role": "operator",
                    "content": "This is also a very long response. " * 100
                }
            ]
        }
        
        response = requests.post(
            f"{base_url}/sentiment_widget/action",
            json=large_data,
            headers={"Content-Type": "application/json"}
        )
        
        # Should handle large payloads gracefully
        assert response.status_code in [200, 202, 413]  # 413 = Payload Too Large
    
    def test_malformed_json(self, base_url: str):
        """Test handling of malformed JSON"""
        response = requests.post(
            f"{base_url}/sentiment_widget/action",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_missing_required_fields(self, base_url: str):
        """Test handling of missing required fields"""
        incomplete_data = {
            "website_id": "test-website-incomplete"
            # Missing session_id and messages
        }
        
        response = requests.post(
            f"{base_url}/sentiment_widget/action",
            json=incomplete_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_concurrent_requests(self, base_url: str, test_data: Dict[str, Any]):
        """Test handling of concurrent requests"""
        import concurrent.futures
        import threading
        
        def make_request():
            response = requests.post(
                f"{base_url}/sentiment_widget/action",
                json=test_data,
                headers={"Content-Type": "application/json"}
            )
            return response.status_code
        
        # Make 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        success_count = sum(1 for status in results if status in [200, 202])
        assert success_count >= 8  # Allow some failures due to load


class TestDatabaseIntegration:
    """Test database integration"""
    
    @pytest.fixture(scope="class")
    def base_url(self) -> str:
        return "http://localhost:8000"
    
    def test_database_connectivity(self, base_url: str):
        """Test that database is accessible"""
        # This would typically be tested through an admin endpoint
        # For now, we test indirectly through the health endpoint
        response = requests.get(f"{base_url}/health")
        assert response.status_code == 200
        
        data = response.json()
        # Assuming health endpoint includes database status
        if "database" in data:
            assert data["database"]["status"] == "healthy"
    
    def test_data_persistence(self, base_url: str):
        """Test that data is persisted correctly"""
        # Send a sentiment analysis request
        test_data = {
            "website_id": "test-persistence",
            "session_id": "test-session-persistence",
            "messages": [
                {"role": "user", "content": "Test message for persistence"},
                {"role": "operator", "content": "Test response"}
            ]
        }
        
        response = requests.post(
            f"{base_url}/sentiment_widget/action",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code in [200, 202]
        
        # If async, wait for completion
        if response.status_code == 202:
            request_id = response.json()["request_id"]
            time.sleep(5)  # Wait for processing
            
            status_response = requests.get(f"{base_url}/sentiment_status/{request_id}")
            assert status_response.status_code == 200


class TestPerformance:
    """Performance tests"""
    
    @pytest.fixture(scope="class")
    def base_url(self) -> str:
        return "http://localhost:8000"
    
    def test_response_time(self, base_url: str):
        """Test response time for health endpoint"""
        start_time = time.time()
        response = requests.get(f"{base_url}/health")
        end_time = time.time()
        
        assert response.status_code == 200
        
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        assert response_time < 1000  # Should respond within 1 second
    
    def test_sentiment_analysis_performance(self, base_url: str):
        """Test sentiment analysis performance"""
        test_data = {
            "website_id": "test-performance",
            "session_id": "test-session-performance",
            "messages": [
                {"role": "user", "content": "Performance test message"},
                {"role": "operator", "content": "Performance test response"}
            ]
        }
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/sentiment_widget/action",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        end_time = time.time()
        
        assert response.status_code in [200, 202]
        
        response_time = (end_time - start_time) * 1000
        
        if response.status_code == 200:
            # Immediate response should be fast
            assert response_time < 5000  # 5 seconds
        else:
            # Async response should be very fast
            assert response_time < 1000  # 1 second

"""
Daily Report Generator Service
-----------------------------
Service for generating simplified daily sentiment reports
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.models.schemas import DailySentimentReport, DailySentimentRecord

logger = logging.getLogger(__name__)


class DailyReportGenerator:
    """
    Service for generating daily sentiment analysis reports.
    This service:
    - Collects conversations processed throughout the day
    - Generates consolidated reports with sentiment analysis
    - Manages the daily report generation workflow
    """

    def __init__(self, config: Dict[str, Any], continuous_processor):
        """
        Initialize the daily report generator.

        Args:
            config: Periodic sentiment configuration
            continuous_processor: ContinuousSentimentProcessor instance
        """
        self.config = config
        self.continuous_processor = continuous_processor

        # Daily report settings
        self.daily_config = config.get('daily_report', {})
        self.start_time = self.daily_config.get('start_time', '00:00')
        self.end_time = self.daily_config.get('end_time', '23:59')
        self.timezone_str = self.daily_config.get('timezone', 'Asia/Ho_Chi_Minh')

        # Processing settings
        self.processing_config = config.get('processing', {})
        self.min_messages = self.processing_config.get('min_messages_for_analysis', 2)

        # Threading
        self._lock = threading.Lock()
        self._generating = False

        logger.info("NightlyReportGenerator initialized")

    def _parse_time_string(self, time_str: str) -> Tuple[int, int]:
        """
        Parse time string in HH:MM format.

        Args:
            time_str: Time string in HH:MM format

        Returns:
            Tuple of (hour, minute)
        """
        try:
            hour, minute = map(int, time_str.split(':'))
            return hour, minute
        except Exception as e:
            logger.error(f"Error parsing time string '{time_str}': {e}")
            return 18, 0  # Default to 6:00 PM

    def _get_nightly_time_range(self, target_date: datetime = None) -> Tuple[int, int]:
        """
        Get the timestamp range for nightly processing.

        Args:
            target_date: Target date for the report (defaults to today)

        Returns:
            Tuple of (start_timestamp, end_timestamp)
        """
        if target_date is None:
            target_date = datetime.now()

        # Get timezone
        try:
            tz = pytz.timezone(self.timezone_str)
        except Exception as e:
            logger.warning(f"Invalid timezone '{self.timezone_str}': {e}. Using UTC.")
            tz = pytz.UTC

        # Parse time strings
        start_hour, start_minute = self._parse_time_string(self.start_nightly_time)
        end_hour, end_minute = self._parse_time_string(self.end_nightly_time)

        # Create start time (previous day if end time is next day)
        if end_hour < start_hour:  # Crosses midnight
            start_date = target_date - timedelta(days=1)
        else:
            start_date = target_date

        start_time = tz.localize(datetime.combine(
            start_date.date(),
            datetime.min.time().replace(hour=start_hour, minute=start_minute)
        ))

        # Create end time
        if end_hour < start_hour:  # Crosses midnight
            end_date = target_date
        else:
            end_date = target_date

        end_time = tz.localize(datetime.combine(
            end_date.date(),
            datetime.min.time().replace(hour=end_hour, minute=end_minute)
        ))

        # Convert to timestamps
        start_timestamp = int(start_time.timestamp())
        end_timestamp = int(end_time.timestamp())

        return start_timestamp, end_timestamp

    def _collect_conversations_in_range(self, start_timestamp: int, end_timestamp: int) -> List[ConversationSentiment]:
        """
        Collect all conversations that were active in the specified time range.

        Args:
            start_timestamp: Start timestamp
            end_timestamp: End timestamp

        Returns:
            List of ConversationSentiment objects
        """
        conversations = []

        try:
            # Get all conversations from database
            all_data = self.database._data

            for website_id, sessions in all_data.items():
                for session_id, session_data in sessions.items():
                    try:
                        # Check if conversation has messages in the time range
                        messages_list = session_data.get('data', [])
                        if not messages_list or len(messages_list) < self.min_messages:
                            continue

                        # Check if any message is in the time range
                        has_messages_in_range = False
                        for msg in messages_list:
                            msg_timestamp = msg.get('timestamp', 0)
                            if start_timestamp <= msg_timestamp <= end_timestamp:
                                has_messages_in_range = True
                                break

                        if not has_messages_in_range:
                            continue

                        # Extract conversation data
                        conversation = self._extract_conversation_data(website_id, session_id)
                        if conversation:
                            conversations.append(conversation)

                    except Exception as e:
                        logger.error(f"Error processing conversation {website_id}_{session_id}: {e}")
                        continue

        except Exception as e:
            logger.error(f"Error collecting conversations: {e}")

        return conversations

    def _extract_conversation_data(self, website_id: str, session_id: str, session_data: Dict = None) -> Optional[ConversationSentiment]:
        """
        Extract conversation data from session data.

        Args:
            website_id: Website ID
            session_id: Session ID
            session_data: Session data from database

        Returns:
            ConversationSentiment object or None if extraction fails
        """
        try:
            # Get conversation data from database if not provided
            if session_data is None:
                session_data = self.database.get_conversation_data(website_id, session_id)
                if not session_data:
                    return None

            # Extract conversation metadata
            conversation_start = session_data.get('conversation_start', 0)
            user_info = session_data.get('user', {})
            messages_list = session_data.get('data', [])

            # Convert user info
            user = UserInfo(
                nickname=user_info.get('nickname', 'Unknown'),
                user_id=user_info.get('user_id', session_id)
            )

            # Extract agent information from messages
            agents = []
            agent_names = set()
            for msg in messages_list:
                if msg.get('role') == 'operator':
                    origin = msg.get('origin', '')
                    if origin and origin not in agent_names:
                        agent_names.add(origin)
                        agents.append(AgentInfo(name=origin, role='operator'))

            # If no specific agents found, add a generic one
            if not agents:
                agents.append(AgentInfo(name='Support Agent', role='operator'))

            # Convert messages
            messages = []
            for msg in messages_list:
                try:
                    message = MessageData(
                        type=msg.get('type', 'text'),
                        origin=msg.get('origin', 'chat'),
                        text=msg.get('text', ''),
                        timestamp=msg.get('timestamp', 0),
                        fingerprint=msg.get('fingerprint', 0),
                        role=msg.get('role', 'user')
                    )
                    messages.append(message)
                except Exception as e:
                    logger.warning(f"Failed to convert message: {e}")
                    continue

            # Determine if conversation is completed
            current_time = int(time.time())
            last_message_time = max(msg.timestamp for msg in messages) if messages else 0
            is_completed = (current_time - last_message_time) > (24 * 3600)  # 24 hours

            # Perform sentiment analysis for this conversation
            sentiment_analysis = self._perform_sentiment_analysis(website_id, session_id)

            # Update conversation tracker
            self.conversation_tracker.update_conversation(
                website_id=website_id,
                session_id=session_id,
                last_message_timestamp=last_message_time,
                message_count=len(messages),
                is_completed=is_completed
            )

            if sentiment_analysis.overall_emotion != "unknown":
                self.conversation_tracker.update_sentiment_analysis(
                    website_id=website_id,
                    session_id=session_id,
                    analysis_timestamp=int(time.time())
                )

            # Create conversation sentiment object
            conversation_sentiment = ConversationSentiment(
                session_id=session_id,
                website_id=website_id,
                user=user,
                agents=agents,
                conversation_start=conversation_start,
                conversation_end=last_message_time if is_completed else None,
                messages=messages,
                sentiment_analysis=sentiment_analysis,
                analysis_timestamp=int(time.time()),
                message_count=len(messages),
                is_completed=is_completed
            )

            return conversation_sentiment

        except Exception as e:
            logger.error(f"Error extracting conversation data for {website_id}_{session_id}: {e}")
            return None

    def _calculate_sentiment_distribution(self, conversations: List[ConversationSentiment]) -> Dict[str, int]:
        """
        Calculate sentiment distribution from conversations.

        Args:
            conversations: List of conversations

        Returns:
            Dictionary with sentiment counts
        """
        distribution = {
            "anger": 0,
            "frustration": 0,
            "anxiety": 0,
            "neutral": 0,
            "satisfaction": 0,
            "unknown": 0
        }

        for conv in conversations:
            emotion = conv.sentiment_analysis.overall_emotion
            if emotion in distribution:
                distribution[emotion] += 1
            else:
                distribution["unknown"] += 1

        return distribution

    def _perform_sentiment_analysis(self, website_id: str, session_id: str) -> SentimentAnalysisResult:
        """
        Perform sentiment analysis on a conversation.

        Args:
            website_id: Website ID
            session_id: Session ID

        Returns:
            SentimentAnalysisResult object
        """
        try:
            # Prepare requests for all three sentiment types
            requests = []
            for item_id in ['overall_emotion', 'sentiment_shift', 'latest_sentiment']:
                request_data = {
                    'item_id': item_id,
                    'session_id': session_id,
                    'website_id': website_id,
                    'priority': 1  # High priority for nightly processing
                }

                # Add request to sentiment service
                request_id = self.sentiment_service.add_request(request_data)
                requests.append((item_id, request_id))

            # Wait for results with timeout
            results = {}
            start_time = time.time()
            timeout = 30  # 30 seconds timeout

            while len(results) < 3 and (time.time() - start_time) < timeout:
                for item_id, request_id in requests:
                    if item_id not in results:
                        result = self.sentiment_service.get_result(request_id)
                        if result:
                            results[item_id] = result.get('data', {}).get('value', 'unknown')

                if len(results) < 3:
                    time.sleep(0.5)  # Wait before checking again

            # Create sentiment analysis result
            return SentimentAnalysisResult(
                overall_emotion=results.get('overall_emotion', 'unknown'),
                sentiment_shift=results.get('sentiment_shift', 'unknown'),
                latest_sentiment=results.get('latest_sentiment', 'unknown')
            )

        except Exception as e:
            logger.error(f"Error performing sentiment analysis for {website_id}_{session_id}: {e}")
            return SentimentAnalysisResult(
                overall_emotion="unknown",
                sentiment_shift="unknown",
                latest_sentiment="unknown"
            )

    def generate_daily_report(self, target_date: datetime = None) -> NightlyReport:
        """
        Generate a daily sentiment report for the specified date.

        Args:
            target_date: Target date for the report (defaults to today)

        Returns:
            NightlyReport object (reusing schema for compatibility)
        """
        with self._lock:
            if self._generating:
                raise RuntimeError("Daily report generation is already running")

            self._generating = True

        try:
            start_time = time.time()

            if target_date is None:
                target_date = datetime.now()

            report_date = target_date.strftime('%Y-%m-%d')
            report_id = f"daily_{report_date}_{int(start_time)}"

            logger.info(f"Starting daily report generation for {report_date} (ID: {report_id})")

            # Get conversations processed throughout the day
            conversations = self.continuous_processor.get_daily_conversations()

            logger.info(f"Found {len(conversations)} conversations for daily report")

            # Calculate statistics
            total_conversations = len(conversations)
            completed_conversations = sum(1 for conv in conversations if conv.is_completed)
            ongoing_conversations = total_conversations - completed_conversations
            total_messages = sum(conv.message_count for conv in conversations)

            # Calculate sentiment distribution
            sentiment_distribution = self._calculate_sentiment_distribution(conversations)

            # Create report
            processing_duration = time.time() - start_time
            report = NightlyReport(
                report_id=report_id,
                report_date=report_date,
                generation_timestamp=int(start_time),
                start_nightly_time=self.start_time,
                end_nightly_time=self.end_time,
                total_conversations=total_conversations,
                completed_conversations=completed_conversations,
                ongoing_conversations=ongoing_conversations,
                total_messages=total_messages,
                sentiment_distribution=sentiment_distribution,
                conversations=conversations,
                processing_duration_seconds=processing_duration,
                data_collection_period=f"Daily report for {report_date} ({self.start_time} to {self.end_time})"
            )

            logger.info(f"Daily report generated: {total_conversations} conversations, "
                       f"{total_messages} messages, {processing_duration:.2f}s duration")

            return report

        finally:
            with self._lock:
                self._generating = False

    def get_generation_status(self) -> Dict[str, Any]:
        """
        Get current generation status.

        Returns:
            Dictionary with generation status information
        """
        with self._lock:
            return {
                "is_generating": self._generating,
                "available_reports": len(self.list_available_reports())
            }

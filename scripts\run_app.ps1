# PowerShell script to run the Sentiment Analysis API System

# Check if Conda is installed
try {
    $condaVersion = conda --version
    Write-Host "Found Conda: $condaVersion"
}
catch {
    Write-Host "Conda is not installed or not in PATH. Please install Miniconda or Anaconda."
    Write-Host "Visit https://docs.conda.io/en/latest/miniconda.html for installation instructions."
    exit 1
}

# Check if the environment exists
$envExists = conda env list | Select-String "sentiment-api"
if (-not $envExists) {
    Write-Host "Conda environment 'sentiment-api' does not exist."
    Write-Host "Please run the installation script first: .\scripts\install_dependencies.ps1"
    exit 1
}

# Activate Conda environment
Write-Host "Activating Conda environment..."
conda activate sentiment-api
Write-Host "Conda environment activated."

# Create logs directory if it doesn't exist
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
    Write-Host "Created logs directory."
}

# Run the application
Write-Host "Starting the application..."

# Check if ngrok is needed
if ($args -contains "--ngrok") {
    Write-Host "Starting with ngrok support..."
    python run.py
}
else {
    # Run directly with uvicorn
    Write-Host "Starting with uvicorn..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
}

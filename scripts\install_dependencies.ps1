# PowerShell script to install dependencies for the Sentiment Analysis API System using Conda

# Check if Conda is installed
try {
    $condaVersion = conda --version
    Write-Host "Found Conda: $condaVersion"
}
catch {
    Write-Host "Conda is not installed or not in PATH. Please install Miniconda or Anaconda."
    Write-Host "Visit https://docs.conda.io/en/latest/miniconda.html for installation instructions."
    exit 1
}

# Create Conda environment
Write-Host "Creating Conda environment..."
conda env create -f environment.yml
Write-Host "Conda environment created."

# Activate Conda environment
Write-Host "To activate the Conda environment, run:"
Write-Host "conda activate sentiment-api"

# Create .env file from example if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Host "Creating .env file from .env.example..."
    Copy-Item ".env.example" ".env"
    Write-Host ".env file created. Please edit it with your configuration."
}
else {
    Write-Host ".env file already exists."
}

Write-Host "Setup complete. After activating the environment, you can run the application with:"
Write-Host "uvicorn app.main:app --host 0.0.0.0 --port 8000"

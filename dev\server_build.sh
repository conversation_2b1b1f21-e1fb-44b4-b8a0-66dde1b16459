#!/bin/bash

# ==== C<PERSON><PERSON> hình cần chỉnh trước khi chạy ====
username="your_user"                       # Thay tên user VPS của bạn
project_path="/home/<USER>/A"           # Đường dẫn đến project
domain="ai-tools.secomapp.com"             # Domain bạn đã trỏ về VPS
email="<EMAIL>"              # Email đăng ký SSL
branch="main"                              # Branch git bạn dùng (deploy)

# ==== Cập nhật hệ thống và cài đặt cần thiết ====
sudo apt update && sudo apt upgrade -y
sudo apt install -y nginx python3-pip python3-venv certbot python3-certbot-nginx git

# ==== Tạo virtualenv và cài dependencies ====
cd $project_path || { echo "Project path not found!"; exit 1; }
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
deactivate

# ==== Tạo systemd service cho FastAPI ====
sudo tee /etc/systemd/system/fastapi.service > /dev/null << EOF
[Unit]
Description=FastAPI Service
After=network.target

[Service]
User=$username
WorkingDirectory=$project_path
Environment="PATH=$project_path/venv/bin"
ExecStart=$project_path/venv/bin/uvicorn main:app --host 127.0.0.1 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable fastapi.service
sudo systemctl start fastapi.service

# ==== Cấu hình NGINX làm reverse proxy ====
sudo tee /etc/nginx/sites-available/$domain > /dev/null << EOF
server {
    listen 80;
    server_name $domain;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

sudo ln -sf /etc/nginx/sites-available/$domain /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx

# ==== Cài đặt HTTPS với Certbot ====
sudo certbot --nginx -d $domain --non-interactive --agree-tos -m $email

# ==== Thiết lập sudo không cần mật khẩu cho restart service ====
sudo bash -c "echo '$username ALL=(ALL) NOPASSWD: /bin/systemctl restart fastapi.service' >> /etc/sudoers.d/fastapi_restart"
sudo chmod 440 /etc/sudoers.d/fastapi_restart

# ==== Tạo script deploy.sh cho CI/CD ====
cat > $project_path/deploy.sh << 'EOF'
#!/bin/bash
echo "Pull latest code from Git..."
git pull origin main

echo "Activate virtual env and install dependencies..."
source ./venv/bin/activate
pip install -r requirements.txt
deactivate

echo "Restart FastAPI service..."
sudo systemctl restart fastapi.service

echo "Deployment finished!"
EOF

chmod +x $project_path/deploy.sh

echo "===== Setup completed! ====="
echo "FastAPI is running as systemd service."
echo "NGINX is configured with SSL for domain $domain."
echo "You can deploy new code by running: $project_path/deploy.sh"

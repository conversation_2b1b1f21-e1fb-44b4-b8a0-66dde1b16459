name: CD Pipeline

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_run:
    workflows: ["CI Pipeline"]
    types:
      - completed
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Job 1: Build and Push Docker Image
  build-and-push:
    runs-on: ubuntu-latest
    name: Build & Push Docker Image
    if: github.event.workflow_run.conclusion == 'success' || github.event_name == 'push'
    
    permissions:
      contents: read
      packages: write
      
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # Job 2: Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    name: Deploy to Staging
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USER }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        port: ${{ secrets.STAGING_PORT }}
        script: |
          cd ${{ secrets.STAGING_PATH }}
          
          # Pull latest code
          git pull origin main
          
          # Update Docker image
          docker pull ${{ needs.build-and-push.outputs.image-tag }}
          
          # Run deployment script
          ./scripts/deploy-staging.sh ${{ needs.build-and-push.outputs.image-tag }}
          
    - name: Run staging health check
      run: |
        sleep 30
        curl -f ${{ secrets.STAGING_URL }}/health || exit 1
        
    - name: Run staging smoke tests
      run: |
        curl -f ${{ secrets.STAGING_URL }}/api/health || exit 1
        # Add more smoke tests here

  # Job 3: Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    name: Deploy to Production
    needs: [build-and-push, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v')
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        port: ${{ secrets.PRODUCTION_PORT }}
        script: |
          cd ${{ secrets.PRODUCTION_PATH }}
          
          # Pull latest code
          git pull origin main
          
          # Update Docker image
          docker pull ${{ needs.build-and-push.outputs.image-tag }}
          
          # Run blue-green deployment
          ./scripts/deploy-production.sh ${{ needs.build-and-push.outputs.image-tag }}
          
    - name: Run production health check
      run: |
        sleep 60
        curl -f ${{ secrets.PRODUCTION_URL }}/health || exit 1
        
    - name: Run production smoke tests
      run: |
        curl -f ${{ secrets.PRODUCTION_URL }}/api/health || exit 1
        # Add more comprehensive tests here
        
    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      if: success()
      with:
        status: success
        text: "🚀 Production deployment successful! Version: ${{ github.ref_name }}"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
    - name: Notify deployment failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: failure
        text: "❌ Production deployment failed! Version: ${{ github.ref_name }}"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Job 4: Security Scan
  security-scan:
    runs-on: ubuntu-latest
    name: Container Security Scan
    needs: build-and-push
    
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build-and-push.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Job 5: Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    name: Performance Tests
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Install k6
      run: |
        sudo gpg -k
        sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
        
    - name: Run performance tests
      run: |
        k6 run tests/performance/load-test.js --env BASE_URL=${{ secrets.STAGING_URL }}
        
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: |
          performance-results.json

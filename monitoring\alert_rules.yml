groups:
  - name: sentiment_analysis_alerts
    rules:
      # High error rate alert
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # High response time alert
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      # High memory usage alert
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanizePercentage }}"

      # High CPU usage alert
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}%"

      # Low disk space alert
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.2
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} available"

      # Service down alert
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} service is down"

      # Database connection issues
      - alert: DatabaseConnectionIssues
        expr: postgresql_up == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection issues"
          description: "Cannot connect to PostgreSQL database"

      # Redis connection issues
      - alert: RedisConnectionIssues
        expr: redis_up == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection issues"
          description: "Cannot connect to Redis"

      # High database connections
      - alert: HighDatabaseConnections
        expr: postgresql_stat_database_numbackends > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High number of database connections"
          description: "Database has {{ $value }} active connections"

      # Sentiment analysis queue backup
      - alert: SentimentQueueBackup
        expr: sentiment_analysis_queue_size > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Sentiment analysis queue backup"
          description: "Queue has {{ $value }} pending requests"

      # Container restart alert
      - alert: ContainerRestarted
        expr: increase(container_start_time_seconds[1h]) > 0
        labels:
          severity: warning
        annotations:
          summary: "Container restarted"
          description: "Container {{ $labels.name }} has restarted"

      # SSL certificate expiry
      - alert: SSLCertificateExpiry
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate expires in {{ $value | humanizeDuration }}"

  - name: business_metrics_alerts
    rules:
      # Low sentiment satisfaction rate
      - alert: LowSatisfactionRate
        expr: (sentiment_analysis_satisfaction_total / sentiment_analysis_total) < 0.6
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low customer satisfaction rate"
          description: "Satisfaction rate is {{ $value | humanizePercentage }} over the last 10 minutes"

      # High anger/frustration rate
      - alert: HighNegativeSentiment
        expr: (sentiment_analysis_anger_total + sentiment_analysis_frustration_total) / sentiment_analysis_total > 0.3
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High negative sentiment detected"
          description: "Negative sentiment rate is {{ $value | humanizePercentage }}"

      # Processing time too long
      - alert: LongProcessingTime
        expr: sentiment_analysis_processing_duration_seconds > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Sentiment analysis taking too long"
          description: "Processing time is {{ $value }}s"

  - name: infrastructure_alerts
    rules:
      # Docker daemon issues
      - alert: DockerDaemonDown
        expr: engine_daemon_engine_info == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Docker daemon is down"
          description: "Docker daemon is not responding"

      # Network connectivity issues
      - alert: NetworkConnectivityIssues
        expr: probe_success == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Network connectivity issues"
          description: "Cannot reach {{ $labels.instance }}"

      # Log errors
      - alert: HighLogErrors
        expr: rate(log_entries_total{level="error"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High number of log errors"
          description: "{{ $value }} errors per second in logs"

      # Backup failures
      - alert: BackupFailure
        expr: backup_last_success_timestamp < time() - 86400 * 2
        for: 1h
        labels:
          severity: critical
        annotations:
          summary: "Backup failure"
          description: "Last successful backup was {{ $value | humanizeTimestamp }}"

  - name: deployment_alerts
    rules:
      # Deployment in progress
      - alert: DeploymentInProgress
        expr: deployment_status == 1
        labels:
          severity: info
        annotations:
          summary: "Deployment in progress"
          description: "Deployment to {{ $labels.environment }} is in progress"

      # Deployment failed
      - alert: DeploymentFailed
        expr: deployment_status == 2
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Deployment failed"
          description: "Deployment to {{ $labels.environment }} has failed"

      # Rollback required
      - alert: RollbackRequired
        expr: deployment_health_check_failures > 3
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Rollback required"
          description: "Health checks failing after deployment, rollback may be required"

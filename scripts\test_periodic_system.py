#!/usr/bin/env python3
"""
Test script for periodic sentiment analysis system
"""

import os
import sys
import json
import time
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import Config
from app.services.conversation_tracker import ConversationTracker
from app.services.periodic_sentiment_processor import PeriodicSentimentProcessor
from app.services.nightly_report_generator import NightlyReportGenerator


def test_conversation_tracker():
    """Test conversation tracker functionality"""
    print("🔍 Testing Conversation Tracker...")

    # Create a temporary tracker
    tracker = ConversationTracker("data/test_conversation_tracking.json")

    # Test data
    current_time = int(time.time())

    # Add some test conversations
    tracker.update_conversation("website1", "session1", current_time, 5)
    tracker.update_conversation("website1", "session2", current_time - 3600, 3)
    tracker.update_conversation("website2", "session3", current_time - 7200, 8)

    # Update sentiment analysis for one conversation
    tracker.update_sentiment_analysis("website1", "session1", current_time)

    # Get statistics
    stats = tracker.get_statistics()
    print(f"   📊 Statistics: {stats}")

    # Get conversations for periodic analysis
    conversations = tracker.get_conversations_for_periodic_analysis()
    print(f"   📝 Conversations needing analysis: {len(conversations)}")

    # Mark one as completed
    tracker.mark_conversation_completed("website1", "session2")

    print("   ✅ Conversation Tracker test completed")
    return True


def test_config_loading():
    """Test configuration loading"""
    print("⚙️  Testing Configuration Loading...")

    try:
        config = Config()
        daily_config = config.get_daily_sentiment_config()

        print(f"   📋 Daily sentiment enabled: {daily_config.get('enabled', False)}")
        print(f"   ⏰ Processing intervals: {daily_config.get('processing_intervals', [])}")
        print(f"   🌅 Daily report time: {daily_config.get('daily_report', {}).get('start_time', 'N/A')}")

        print("   ✅ Configuration loading test completed")
        return True
    except Exception as e:
        print(f"   ❌ Configuration loading failed: {e}")
        return False


def test_file_structure():
    """Test that required directories and files can be created"""
    print("📁 Testing File Structure...")

    try:
        # Test directories
        test_dirs = [
            "data",
            "data/nightly_reports",
            "logs"
        ]

        for dir_path in test_dirs:
            os.makedirs(dir_path, exist_ok=True)
            print(f"   📂 Directory created/verified: {dir_path}")

        # Test file creation
        test_files = [
            "data/test_periodic_results.json",
            "data/test_conversation_tracking.json"
        ]

        for file_path in test_files:
            with open(file_path, 'w') as f:
                json.dump({"test": "data", "timestamp": int(time.time())}, f)
            print(f"   📄 Test file created: {file_path}")

        print("   ✅ File structure test completed")
        return True
    except Exception as e:
        print(f"   ❌ File structure test failed: {e}")
        return False


def test_mock_periodic_processing():
    """Test periodic processing with mock data"""
    print("🔄 Testing Mock Periodic Processing...")

    try:
        # Load config
        config = Config()
        daily_config = config.get_daily_sentiment_config()

        # Create mock sentiment service and database
        class MockSentimentService:
            def add_request(self, request_data):
                return f"mock_request_{int(time.time())}"

            def get_result(self, request_id):
                return {
                    "data": {
                        "value": "satisfaction"
                    }
                }

        class MockDatabase:
            def get_conversation_data(self, website_id, session_id):
                return {
                    "conversation_start": int(time.time()) - 3600,
                    "user": {
                        "nickname": "Test User",
                        "user_id": "test_user_123"
                    },
                    "data": [
                        {
                            "type": "text",
                            "origin": "chat",
                            "text": "Hello, I need help",
                            "timestamp": int(time.time()) - 3600,
                            "fingerprint": 123456,
                            "role": "user"
                        },
                        {
                            "type": "text",
                            "origin": "chat",
                            "text": "Hi! How can I help you?",
                            "timestamp": int(time.time()) - 3500,
                            "fingerprint": 123457,
                            "role": "operator"
                        }
                    ]
                }

        # Create processor with mock services
        mock_sentiment = MockSentimentService()
        mock_db = MockDatabase()

        processor = PeriodicSentimentProcessor(daily_config, mock_sentiment, mock_db)

        # Add some test conversations to tracker
        processor.conversation_tracker.update_conversation("test_website", "test_session", int(time.time()), 2)

        print(f"   📊 Processor initialized with {len(processor.conversation_tracker._tracking_data)} conversations")
        print(f"   ⚙️  Processing config: min_messages={processor.min_messages}, batch_size={processor.batch_size}")

        print("   ✅ Mock periodic processing test completed")
        return True
    except Exception as e:
        print(f"   ❌ Mock periodic processing test failed: {e}")
        return False


def test_nightly_report_structure():
    """Test nightly report generation structure"""
    print("🌙 Testing Nightly Report Structure...")

    try:
        # Load config
        config = Config()
        daily_config = config.get_daily_sentiment_config()

        # Create mock processor and database
        class MockProcessor:
            def __init__(self):
                self.conversation_tracker = ConversationTracker("data/test_conversation_tracking.json")

            def get_latest_results(self, limit=50):
                return []

        class MockDatabase:
            def _data(self):
                return {}

        mock_processor = MockProcessor()
        mock_db = MockDatabase()

        generator = NightlyReportGenerator(daily_config, mock_processor, mock_db)

        print(f"   🕐 Start time: {generator.start_nightly_time}")
        print(f"   🕕 End time: {generator.end_nightly_time}")
        print(f"   🌍 Timezone: {generator.timezone_str}")
        print(f"   📁 Reports directory: {generator.reports_dir}")

        # Test time parsing
        start_hour, start_minute = generator._parse_time_string(generator.start_nightly_time)
        print(f"   ⏰ Parsed start time: {start_hour:02d}:{start_minute:02d}")

        print("   ✅ Nightly report structure test completed")
        return True
    except Exception as e:
        print(f"   ❌ Nightly report structure test failed: {e}")
        return False


def cleanup_test_files():
    """Clean up test files"""
    print("🧹 Cleaning up test files...")

    test_files = [
        "data/test_periodic_results.json",
        "data/test_conversation_tracking.json"
    ]

    for file_path in test_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"   🗑️  Removed: {file_path}")
        except Exception as e:
            print(f"   ⚠️  Could not remove {file_path}: {e}")


def main():
    """Run all tests"""
    print("🚀 Starting Periodic Sentiment Analysis System Tests")
    print("=" * 60)

    tests = [
        ("Configuration Loading", test_config_loading),
        ("File Structure", test_file_structure),
        ("Conversation Tracker", test_conversation_tracker),
        ("Mock Periodic Processing", test_mock_periodic_processing),
        ("Nightly Report Structure", test_nightly_report_structure),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The periodic sentiment system is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")

    # Cleanup
    cleanup_test_files()

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

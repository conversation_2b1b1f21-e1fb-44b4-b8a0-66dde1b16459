"""
Asynchronous API Routes
----------------------
This module provides asynchronous API routes for sentiment analysis.
"""

from fastapi import APIRouter, Request, Response, status, Query
from fastapi.responses import JSONResponse
import json
import logging
import async<PERSON>
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# Create a router for the async API routes
router = APIRouter(tags=["async_api"], prefix="/async")

@router.post("/sentiment_widget/action")
@router.get("/sentiment_widget/action")
async def async_sentiment_widget_action(request: Request):
    """Asynchronous action endpoint for sentiment widget"""
    try:
        # Log the incoming request
        body_bytes = await request.body()
        body_str = body_bytes.decode('utf-8')
        logger.info(f"Received async sentiment action request: {body_str}")

        # Parse the request body
        body = json.loads(body_str)
        item_id = body.get('widget', {}).get('item_id')
        session_id = body.get('origin', {}).get('session_id')
        website_id = body.get('origin', {}).get('website_id')

        # Validate required fields
        if not all([item_id, session_id, website_id]):
            logger.error("Missing required fields in request")
            return JSONResponse(
                content={
                    "data": {
                        "value": "error_missing_fields"
                    }
                },
                status_code=200
            )

        # Validate website ID against whitelist
        from app import get_website_validator
        website_validator = get_website_validator()
        if not website_validator.validate_website_id(website_id):
            logger.warning(f"Rejected async sentiment request from unauthorized website_id: {website_id}")
            return JSONResponse(
                content={
                    "data": {
                        "value": "error_unauthorized_website"
                    }
                },
                status_code=200
            )

        # Add the request to the async sentiment analyzer
        from app import get_async_sentiment_analyzer
        async_sentiment_analyzer = get_async_sentiment_analyzer()
        logger.info(f"Adding async request for item_id: {item_id}, session_id: {session_id}, website_id: {website_id}")
        request_id = await async_sentiment_analyzer.add_request({
            'item_id': item_id,
            'session_id': session_id,
            'website_id': website_id
        })

        # Return immediately with the request ID
        logger.info(f"Async request {request_id} submitted for processing")
        return JSONResponse(
            content={
                "data": {
                    "value": "processing",
                    "request_id": request_id
                }
            },
            status_code=202  # 202 Accepted indicates the request is being processed
        )
    except Exception as e:
        logger.error(f"Error in async_sentiment_widget_action: {e}")
        return JSONResponse(
            content={
                "data": {
                    "value": "error",
                    "message": str(e)
                }
            },
            status_code=200  # Still return 200 to avoid webhook failures
        )

@router.get("/sentiment_status/{request_id}")
async def async_sentiment_status(request_id: str, timeout: float = Query(0.0, ge=0.0, le=10.0)):
    """
    Check the status of a sentiment analysis request asynchronously

    If timeout is 0, returns immediately with the current status.
    If timeout > 0, waits up to that many seconds for a result.
    """
    try:
        from app import get_async_sentiment_analyzer
        async_sentiment_analyzer = get_async_sentiment_analyzer()

        if timeout > 0:
            # Wait for the result with the specified timeout
            result = await async_sentiment_analyzer.wait_for_result(request_id, timeout)
            if result:
                logger.info(f"Got async result for request {request_id} within {timeout} seconds")
                return result

            # If we've waited the maximum time and still no result, return processing status
            logger.info(f"Timeout reached for async status check of request {request_id}")
            return JSONResponse(
                content={
                    "status": "processing",
                    "request_id": request_id,
                    "message": f"Still processing after {timeout} seconds"
                },
                status_code=202  # 202 Accepted indicates the request is being processed
            )
        else:
            # Check immediately without waiting
            result = await async_sentiment_analyzer.get_result(request_id)
            if result:
                logger.info(f"Got async result for request {request_id} immediately")
                return result

            # No result yet
            return JSONResponse(
                content={
                    "status": "processing",
                    "request_id": request_id,
                    "message": "Still processing"
                },
                status_code=202  # 202 Accepted indicates the request is being processed
            )
    except Exception as e:
        logger.error(f"Error in async_sentiment_status: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": str(e)
            },
            status_code=500
        )

@router.get("/sentiment_queue_status")
async def async_sentiment_queue_status():
    """Get the current status of the sentiment analysis queue asynchronously"""
    try:
        from app import get_async_sentiment_analyzer
        async_sentiment_analyzer = get_async_sentiment_analyzer()

        # Get the queue status asynchronously
        status = await async_sentiment_analyzer.get_queue_status()

        # Add some human-readable timestamps
        import time
        status['timestamp_readable'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(status['timestamp']))

        # Calculate additional metrics
        if status['total_requests_received'] > 0:
            status['failure_rate'] = (status['total_requests_failed'] / status['total_requests_received']) * 100
            status['completion_rate'] = ((status['total_requests_processed'] + status['total_requests_failed']) /
                                       status['total_requests_received']) * 100
        else:
            status['failure_rate'] = 0
            status['completion_rate'] = 0

        # Format rates as percentages with 2 decimal places
        status['success_rate'] = f"{status['success_rate']:.2f}%"
        status['failure_rate'] = f"{status['failure_rate']:.2f}%"
        status['completion_rate'] = f"{status['completion_rate']:.2f}%"

        return {
            "status": "ok",
            "queue": status
        }
    except Exception as e:
        logger.error(f"Error in async_sentiment_queue_status: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": str(e)
            },
            status_code=500
        )

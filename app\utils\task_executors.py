"""
Task executors for Airflow integration
Contains implementations for different types of tasks that can be executed by Airflow
"""

import asyncio
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
import json

from app.core.config import Config
from app.services.sentiment_service import SentimentService
from app.services.postgresql_service import PostgreSQLService
from app.services.daily_sentiment_service import DailySentimentService

logger = logging.getLogger(__name__)

async def execute_sentiment_analysis(parameters: Dict[str, Any], task_info: Dict[str, Any]) -> Dict[str, Any]:
    """Execute sentiment analysis for a single conversation"""
    try:
        config = Config()
        sentiment_service = SentimentService(config)
        
        # Extract parameters
        website_id = parameters.get("website_id")
        session_id = parameters.get("session_id")
        messages = parameters.get("messages", [])
        
        if not all([website_id, session_id, messages]):
            raise ValueError("Missing required parameters: website_id, session_id, messages")
        
        # Update progress
        task_info["progress"] = 10.0
        task_info["message"] = "Starting sentiment analysis"
        
        # Perform sentiment analysis
        result = await sentiment_service.analyze_conversation_sentiment(
            website_id=website_id,
            session_id=session_id,
            messages=messages
        )
        
        task_info["progress"] = 90.0
        task_info["message"] = "Sentiment analysis completed"
        
        return {
            "website_id": website_id,
            "session_id": session_id,
            "overall_emotion": result.get("overall_emotion"),
            "sentiment_shift": result.get("sentiment_shift"),
            "latest_sentiment": result.get("latest_sentiment"),
            "analysis_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        raise

async def execute_batch_sentiment_analysis(parameters: Dict[str, Any], task_info: Dict[str, Any]) -> Dict[str, Any]:
    """Execute batch sentiment analysis for multiple conversations"""
    try:
        config = Config()
        sentiment_service = SentimentService(config)
        
        # Extract parameters
        website_ids = parameters.get("website_ids", [])
        date_range = parameters.get("date_range", {})
        batch_size = parameters.get("batch_size", 100)
        processing_type = parameters.get("processing_type", "daily_sentiment")
        
        start_date = datetime.fromisoformat(date_range.get("start_date"))
        end_date = datetime.fromisoformat(date_range.get("end_date"))
        
        task_info["progress"] = 5.0
        task_info["message"] = "Initializing batch processing"
        
        results = []
        total_processed = 0
        total_failed = 0
        
        # Process each website
        for i, website_id in enumerate(website_ids):
            try:
                task_info["progress"] = 10.0 + (i / len(website_ids)) * 80.0
                task_info["message"] = f"Processing website {website_id} ({i+1}/{len(website_ids)})"
                
                # Get conversations for this website in date range
                conversations = await get_conversations_for_processing(
                    website_id, start_date, end_date, batch_size
                )
                
                # Process conversations in batches
                for batch_start in range(0, len(conversations), batch_size):
                    batch = conversations[batch_start:batch_start + batch_size]
                    
                    batch_results = await process_conversation_batch(
                        sentiment_service, batch, task_info
                    )
                    
                    results.extend(batch_results)
                    total_processed += len([r for r in batch_results if r.get("success")])
                    total_failed += len([r for r in batch_results if not r.get("success")])
                    
                    # Small delay to prevent overwhelming the system
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Failed to process website {website_id}: {e}")
                total_failed += 1
        
        task_info["progress"] = 95.0
        task_info["message"] = "Finalizing batch processing"
        
        return {
            "total_processed": total_processed,
            "total_failed": total_failed,
            "success_rate": total_processed / (total_processed + total_failed) if (total_processed + total_failed) > 0 else 0,
            "processing_time": (datetime.now() - task_info["started_at"]).total_seconds(),
            "results_summary": {
                "websites_processed": len(website_ids),
                "date_range": date_range,
                "batch_size": batch_size
            }
        }
        
    except Exception as e:
        logger.error(f"Batch sentiment analysis failed: {e}")
        raise

async def execute_daily_report_generation(parameters: Dict[str, Any], task_info: Dict[str, Any]) -> Dict[str, Any]:
    """Execute daily report generation"""
    try:
        config = Config()
        daily_service = DailySentimentService(config)
        
        # Extract parameters
        target_date = parameters.get("target_date")
        if target_date:
            target_date = datetime.fromisoformat(target_date).date()
        else:
            target_date = datetime.now().date() - timedelta(days=1)  # Previous day
        
        website_ids = parameters.get("website_ids", [])
        
        task_info["progress"] = 10.0
        task_info["message"] = f"Generating daily report for {target_date}"
        
        # Generate reports
        if website_ids:
            # Generate for specific websites
            reports = []
            for i, website_id in enumerate(website_ids):
                task_info["progress"] = 20.0 + (i / len(website_ids)) * 60.0
                task_info["message"] = f"Generating report for {website_id}"
                
                report = await daily_service.generate_daily_report(website_id, target_date)
                reports.append(report)
        else:
            # Generate for all websites
            task_info["progress"] = 30.0
            task_info["message"] = "Generating reports for all websites"
            reports = await daily_service.generate_all_daily_reports(target_date)
        
        task_info["progress"] = 85.0
        task_info["message"] = "Uploading reports to database"
        
        # Upload to PostgreSQL if enabled
        postgresql_config = config.get_postgresql_config()
        if postgresql_config.get("enabled"):
            postgresql_service = PostgreSQLService(postgresql_config)
            upload_results = await postgresql_service.upload_daily_reports(reports)
        else:
            upload_results = {"status": "skipped", "reason": "PostgreSQL not enabled"}
        
        task_info["progress"] = 95.0
        task_info["message"] = "Report generation completed"
        
        return {
            "target_date": target_date.isoformat(),
            "reports_generated": len(reports),
            "upload_results": upload_results,
            "generation_time": (datetime.now() - task_info["started_at"]).total_seconds()
        }
        
    except Exception as e:
        logger.error(f"Daily report generation failed: {e}")
        raise

async def execute_data_cleanup(parameters: Dict[str, Any], task_info: Dict[str, Any]) -> Dict[str, Any]:
    """Execute data cleanup tasks"""
    try:
        config = Config()
        
        # Extract parameters
        cleanup_type = parameters.get("cleanup_type", "old_logs")
        retention_days = parameters.get("retention_days", 30)
        dry_run = parameters.get("dry_run", False)
        
        task_info["progress"] = 10.0
        task_info["message"] = f"Starting {cleanup_type} cleanup"
        
        results = {}
        
        if cleanup_type == "old_logs":
            results = await cleanup_old_logs(retention_days, dry_run, task_info)
        elif cleanup_type == "temp_files":
            results = await cleanup_temp_files(retention_days, dry_run, task_info)
        elif cleanup_type == "old_conversations":
            results = await cleanup_old_conversations(retention_days, dry_run, task_info)
        elif cleanup_type == "all":
            task_info["message"] = "Running comprehensive cleanup"
            results["logs"] = await cleanup_old_logs(retention_days, dry_run, task_info)
            results["temp_files"] = await cleanup_temp_files(retention_days, dry_run, task_info)
            results["conversations"] = await cleanup_old_conversations(retention_days, dry_run, task_info)
        else:
            raise ValueError(f"Unknown cleanup type: {cleanup_type}")
        
        task_info["progress"] = 95.0
        task_info["message"] = "Cleanup completed"
        
        return {
            "cleanup_type": cleanup_type,
            "retention_days": retention_days,
            "dry_run": dry_run,
            "results": results,
            "cleanup_time": (datetime.now() - task_info["started_at"]).total_seconds()
        }
        
    except Exception as e:
        logger.error(f"Data cleanup failed: {e}")
        raise

# Helper functions
async def get_conversations_for_processing(website_id: str, start_date: datetime, end_date: datetime, limit: int) -> List[Dict[str, Any]]:
    """Get conversations that need processing"""
    # This would typically query your data storage
    # For now, return mock data
    conversations = []
    
    # Mock implementation - replace with actual data retrieval
    for i in range(min(limit, 50)):  # Limit for demo
        conversations.append({
            "website_id": website_id,
            "session_id": f"session_{i}",
            "messages": [
                {"role": "user", "content": f"Test message {i}"},
                {"role": "operator", "content": f"Test response {i}"}
            ],
            "timestamp": start_date + timedelta(hours=i)
        })
    
    return conversations

async def process_conversation_batch(sentiment_service: SentimentService, conversations: List[Dict[str, Any]], task_info: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Process a batch of conversations"""
    results = []
    
    for conversation in conversations:
        try:
            result = await sentiment_service.analyze_conversation_sentiment(
                website_id=conversation["website_id"],
                session_id=conversation["session_id"],
                messages=conversation["messages"]
            )
            
            results.append({
                "website_id": conversation["website_id"],
                "session_id": conversation["session_id"],
                "success": True,
                "result": result
            })
            
        except Exception as e:
            logger.error(f"Failed to process conversation {conversation['session_id']}: {e}")
            results.append({
                "website_id": conversation["website_id"],
                "session_id": conversation["session_id"],
                "success": False,
                "error": str(e)
            })
    
    return results

async def cleanup_old_logs(retention_days: int, dry_run: bool, task_info: Dict[str, Any]) -> Dict[str, Any]:
    """Cleanup old log files"""
    import os
    import glob
    from pathlib import Path
    
    task_info["progress"] = 30.0
    task_info["message"] = "Scanning log files"
    
    log_dir = Path("logs")
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    files_to_delete = []
    total_size = 0
    
    if log_dir.exists():
        for log_file in log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_date.timestamp():
                files_to_delete.append(log_file)
                total_size += log_file.stat().st_size
    
    task_info["progress"] = 60.0
    task_info["message"] = f"Found {len(files_to_delete)} old log files"
    
    if not dry_run:
        deleted_count = 0
        for log_file in files_to_delete:
            try:
                log_file.unlink()
                deleted_count += 1
            except Exception as e:
                logger.error(f"Failed to delete {log_file}: {e}")
        
        return {
            "files_deleted": deleted_count,
            "space_freed": total_size,
            "retention_days": retention_days
        }
    else:
        return {
            "files_to_delete": len(files_to_delete),
            "space_to_free": total_size,
            "retention_days": retention_days,
            "dry_run": True
        }

async def cleanup_temp_files(retention_days: int, dry_run: bool, task_info: Dict[str, Any]) -> Dict[str, Any]:
    """Cleanup temporary files"""
    import tempfile
    from pathlib import Path
    
    task_info["progress"] = 40.0
    task_info["message"] = "Scanning temporary files"
    
    temp_dir = Path(tempfile.gettempdir())
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    files_to_delete = []
    total_size = 0
    
    # Look for sentiment analysis temp files
    for temp_file in temp_dir.glob("sentiment_*"):
        if temp_file.stat().st_mtime < cutoff_date.timestamp():
            files_to_delete.append(temp_file)
            total_size += temp_file.stat().st_size
    
    if not dry_run:
        deleted_count = 0
        for temp_file in files_to_delete:
            try:
                temp_file.unlink()
                deleted_count += 1
            except Exception as e:
                logger.error(f"Failed to delete {temp_file}: {e}")
        
        return {
            "files_deleted": deleted_count,
            "space_freed": total_size
        }
    else:
        return {
            "files_to_delete": len(files_to_delete),
            "space_to_free": total_size,
            "dry_run": True
        }

async def cleanup_old_conversations(retention_days: int, dry_run: bool, task_info: Dict[str, Any]) -> Dict[str, Any]:
    """Cleanup old conversation data"""
    task_info["progress"] = 50.0
    task_info["message"] = "Scanning old conversation data"
    
    # This would typically clean up old conversation data from storage
    # For now, return mock results
    
    return {
        "conversations_cleaned": 0 if dry_run else 100,
        "retention_days": retention_days,
        "dry_run": dry_run
    }

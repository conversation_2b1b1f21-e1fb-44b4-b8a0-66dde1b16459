#!/bin/bash

# Rollback Script for Sentiment Analysis System
# Usage: ./rollback.sh [staging|production] [version]

set -euo pipefail

# Configuration
ENVIRONMENT=${1:-}
TARGET_VERSION=${2:-}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_DIR/logs/rollback.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Show usage
show_usage() {
    echo "Usage: $0 [staging|production] [version]"
    echo ""
    echo "Examples:"
    echo "  $0 staging                    # Rollback staging to previous version"
    echo "  $0 production v1.2.0         # Rollback production to specific version"
    echo "  $0 production previous       # Rollback production to previous version"
    echo ""
    echo "Available versions:"
    if [ -f "$PROJECT_DIR/.deployment-history" ]; then
        echo "Recent deployments:"
        tail -10 "$PROJECT_DIR/.deployment-history" | while read line; do
            echo "  $line"
        done
    else
        echo "  No deployment history found"
    fi
}

# Validate inputs
if [ -z "$ENVIRONMENT" ]; then
    show_usage
    exit 1
fi

if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    error "Invalid environment. Use 'staging' or 'production'"
fi

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

log "Starting rollback for $ENVIRONMENT environment"

# Determine current version and target version
CURRENT_VERSION_FILE="$PROJECT_DIR/.current-${ENVIRONMENT}-version"
DEPLOYMENT_HISTORY_FILE="$PROJECT_DIR/.deployment-history"

if [ ! -f "$CURRENT_VERSION_FILE" ]; then
    error "Current version file not found: $CURRENT_VERSION_FILE"
fi

CURRENT_VERSION=$(cat "$CURRENT_VERSION_FILE")
log "Current version: $CURRENT_VERSION"

# Determine target version
if [ -z "$TARGET_VERSION" ] || [ "$TARGET_VERSION" = "previous" ]; then
    # Get previous version from deployment history
    if [ ! -f "$DEPLOYMENT_HISTORY_FILE" ]; then
        error "Deployment history file not found. Cannot determine previous version."
    fi
    
    # Get the second most recent deployment (excluding current)
    TARGET_VERSION=$(grep "$ENVIRONMENT" "$DEPLOYMENT_HISTORY_FILE" | grep -v "$CURRENT_VERSION" | tail -1 | awk '{print $3}')
    
    if [ -z "$TARGET_VERSION" ]; then
        error "Cannot determine previous version from deployment history"
    fi
    
    log "Determined previous version: $TARGET_VERSION"
else
    log "Target version specified: $TARGET_VERSION"
fi

# Confirm rollback
echo
warning "You are about to rollback $ENVIRONMENT from $CURRENT_VERSION to $TARGET_VERSION"
read -p "Are you sure you want to continue? (yes/no): " -r
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    log "Rollback cancelled by user"
    exit 0
fi

# Pre-rollback backup
log "Creating pre-rollback backup..."
BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$PROJECT_DIR/backups/rollback_$BACKUP_TIMESTAMP"
mkdir -p "$BACKUP_DIR"

# Backup current state
if [ "$ENVIRONMENT" = "staging" ]; then
    COMPOSE_FILE="docker-compose.staging.yml"
elif [ "$ENVIRONMENT" = "production" ]; then
    # Determine current production environment
    if [ -f "$PROJECT_DIR/.current-production-env" ]; then
        CURRENT_ENV=$(cat "$PROJECT_DIR/.current-production-env")
        COMPOSE_FILE="docker-compose.production.$CURRENT_ENV.yml"
    else
        COMPOSE_FILE="docker-compose.production.blue.yml"
    fi
fi

# Backup database
log "Backing up database..."
if docker-compose -f "$PROJECT_DIR/$COMPOSE_FILE" ps postgres | grep -q "Up"; then
    docker-compose -f "$PROJECT_DIR/$COMPOSE_FILE" exec -T postgres pg_dump -U postgres sentiment_analysis > "$BACKUP_DIR/database_backup.sql" || warning "Database backup failed"
fi

# Backup application data
log "Backing up application data..."
cp -r "$PROJECT_DIR/data" "$BACKUP_DIR/" 2>/dev/null || warning "Data backup failed"

# Backup configuration
cp "$PROJECT_DIR/config/"* "$BACKUP_DIR/" 2>/dev/null || warning "Config backup failed"
cp "$PROJECT_DIR/postgresql_config"*.yaml "$BACKUP_DIR/" 2>/dev/null || warning "PostgreSQL config backup failed"

success "Pre-rollback backup completed: $BACKUP_DIR"

# Perform rollback based on environment
if [ "$ENVIRONMENT" = "staging" ]; then
    rollback_staging
elif [ "$ENVIRONMENT" = "production" ]; then
    rollback_production
fi

# Rollback staging environment
rollback_staging() {
    log "Rolling back staging environment..."
    
    # Pull target image
    log "Pulling target image: $TARGET_VERSION"
    docker pull "$TARGET_VERSION" || error "Failed to pull target image"
    
    # Update compose file
    log "Updating compose file with target image..."
    sed -i.bak "s|image: .*sentiment-analysis.*|image: $TARGET_VERSION|g" "$PROJECT_DIR/docker-compose.staging.yml"
    
    # Stop current services
    log "Stopping current services..."
    docker-compose -f "$PROJECT_DIR/docker-compose.staging.yml" down
    
    # Start with target version
    log "Starting services with target version..."
    docker-compose -f "$PROJECT_DIR/docker-compose.staging.yml" up -d
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 45
    
    # Health check
    log "Running health check..."
    if curl -f http://localhost:8080/health &>/dev/null; then
        success "Staging rollback completed successfully"
    else
        error "Health check failed after rollback"
    fi
}

# Rollback production environment (blue-green)
rollback_production() {
    log "Rolling back production environment..."
    
    # Determine current and target environments
    CURRENT_ENV_FILE="$PROJECT_DIR/.current-production-env"
    if [ -f "$CURRENT_ENV_FILE" ]; then
        CURRENT_ENV=$(cat "$CURRENT_ENV_FILE")
    else
        CURRENT_ENV="blue"
    fi
    
    if [ "$CURRENT_ENV" = "blue" ]; then
        TARGET_ENV="green"
        CURRENT_COMPOSE_FILE="docker-compose.production.blue.yml"
        TARGET_COMPOSE_FILE="docker-compose.production.green.yml"
        TARGET_PORT="8002"
    else
        TARGET_ENV="blue"
        CURRENT_COMPOSE_FILE="docker-compose.production.green.yml"
        TARGET_COMPOSE_FILE="docker-compose.production.blue.yml"
        TARGET_PORT="8001"
    fi
    
    log "Current environment: $CURRENT_ENV"
    log "Target environment: $TARGET_ENV"
    
    # Pull target image
    log "Pulling target image: $TARGET_VERSION"
    docker pull "$TARGET_VERSION" || error "Failed to pull target image"
    
    # Update target compose file
    log "Updating target compose file with target image..."
    sed -i.bak "s|image: .*sentiment-analysis.*|image: $TARGET_VERSION|g" "$PROJECT_DIR/$TARGET_COMPOSE_FILE"
    
    # Stop target environment if running
    docker-compose -f "$PROJECT_DIR/$TARGET_COMPOSE_FILE" down || warning "Failed to stop target environment"
    
    # Start target environment
    log "Starting target environment with rollback version..."
    docker-compose -f "$PROJECT_DIR/$TARGET_COMPOSE_FILE" up -d || error "Failed to start target environment"
    
    # Wait for target environment to be ready
    log "Waiting for target environment to be ready..."
    sleep 60
    
    # Health check target environment
    log "Running health check on target environment..."
    for i in {1..10}; do
        if curl -f "http://localhost:$TARGET_PORT/health" &>/dev/null; then
            success "Target environment health check passed"
            break
        fi
        if [ $i -eq 10 ]; then
            error "Target environment health check failed"
        fi
        log "Health check attempt $i/10 failed, retrying in 10 seconds..."
        sleep 10
    done
    
    # Switch traffic to target environment
    log "Switching traffic to target environment..."
    
    # Update nginx configuration
    NGINX_CONFIG_DIR="$PROJECT_DIR/nginx"
    if [ -f "$NGINX_CONFIG_DIR/nginx.conf" ]; then
        # Create backup of current nginx config
        cp "$NGINX_CONFIG_DIR/nginx.conf" "$BACKUP_DIR/nginx.conf.bak"
        
        # Update upstream configuration
        if [ "$CURRENT_ENV" = "blue" ]; then
            sed -i "s/server sentiment-api:8001/server sentiment-api:8002/g" "$NGINX_CONFIG_DIR/nginx.conf"
        else
            sed -i "s/server sentiment-api:8002/server sentiment-api:8001/g" "$NGINX_CONFIG_DIR/nginx.conf"
        fi
        
        # Reload nginx
        docker-compose -f "$PROJECT_DIR/$TARGET_COMPOSE_FILE" exec nginx nginx -s reload || error "Failed to reload nginx"
        
        log "Nginx configuration updated and reloaded"
    fi
    
    # Wait for traffic switch
    log "Waiting for traffic switch to complete..."
    sleep 30
    
    # Verify production traffic
    log "Verifying production traffic..."
    for i in {1..5}; do
        if curl -f "http://localhost/health" &>/dev/null; then
            success "Production traffic verification passed"
            break
        fi
        if [ $i -eq 5 ]; then
            error "Production traffic verification failed"
        fi
        log "Traffic verification attempt $i/5 failed, retrying in 10 seconds..."
        sleep 10
    done
    
    # Stop old environment
    log "Stopping old environment ($CURRENT_ENV)..."
    docker-compose -f "$PROJECT_DIR/$CURRENT_COMPOSE_FILE" down || warning "Failed to stop old environment"
    
    # Update environment marker
    echo "$TARGET_ENV" > "$CURRENT_ENV_FILE"
    
    success "Production rollback completed successfully"
}

# Update version tracking
echo "$TARGET_VERSION" > "$CURRENT_VERSION_FILE"
echo "$(date) $ENVIRONMENT rollback $CURRENT_VERSION -> $TARGET_VERSION" >> "$DEPLOYMENT_HISTORY_FILE"

# Cleanup old images
log "Cleaning up old Docker images..."
docker image prune -f || warning "Failed to cleanup old images"

# Final verification
log "Running final verification..."
if [ "$ENVIRONMENT" = "staging" ]; then
    HEALTH_URL="http://localhost:8080/health"
else
    HEALTH_URL="http://localhost/health"
fi

if curl -f "$HEALTH_URL" &>/dev/null; then
    success "Final health check passed"
else
    error "Final health check failed"
fi

# Send notification
if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🔄 Rollback completed! Environment: $ENVIRONMENT, Version: $CURRENT_VERSION -> $TARGET_VERSION\"}" \
        "$SLACK_WEBHOOK_URL" || warning "Failed to send Slack notification"
fi

success "Rollback completed successfully!"
log "Rolled back from: $CURRENT_VERSION"
log "Rolled back to: $TARGET_VERSION"
log "Environment: $ENVIRONMENT"
log "Backup location: $BACKUP_DIR"
log "Rollback time: $(date)"

echo
echo "========================================"
echo "  Rollback Summary"
echo "========================================"
echo "Environment: $ENVIRONMENT"
echo "Previous version: $CURRENT_VERSION"
echo "Current version: $TARGET_VERSION"
echo "Backup location: $BACKUP_DIR"
echo "========================================"

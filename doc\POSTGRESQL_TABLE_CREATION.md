# PostgreSQL Table Creation Guide

## <PERSON><PERSON><PERSON> lệnh SQL để tạo bảng Daily Sentiment Reports

### Tạo bảng ch<PERSON>h

```sql
CREATE TABLE daily_sentiment_reports (
    -- Thông tin cơ bản
    id SERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    website_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    
    -- Thông tin người dùng
    user_nickname VARCHAR(255),
    user_id VARCHAR(255),
    
    -- Thông tin nhân viên
    agent_name VARCHAR(255),
    agent_role VARCHAR(50) DEFAULT 'operator',
    
    -- Th<PERSON><PERSON> gian hội thoại
    conversation_start BIGINT,
    conversation_end BIGINT,
    is_completed BOOLEAN DEFAULT FALSE,
    
    -- K<PERSON>t quả phân tích
    overall_emotion VARCHAR(50),
    sentiment_shift TEXT,
    latest_sentiment VARCHAR(50),
    
    -- Metadata
    message_count INTEGER DEFAULT 0,
    analysis_timestamp BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tạo các index để tối ưu hiệu suất

```sql
-- Index cho report_date (truy vấn theo ngày)
CREATE INDEX idx_daily_sentiment_reports_date ON daily_sentiment_reports (report_date);

-- Index cho website_id (truy vấn theo website)
CREATE INDEX idx_daily_sentiment_reports_website ON daily_sentiment_reports (website_id);

-- Index cho session_id (truy vấn theo session)
CREATE INDEX idx_daily_sentiment_reports_session ON daily_sentiment_reports (session_id);

-- Index cho agent_name (truy vấn theo agent)
CREATE INDEX idx_daily_sentiment_reports_agent ON daily_sentiment_reports (agent_name);

-- Index composite cho truy vấn phức tạp
CREATE INDEX idx_daily_sentiment_reports_composite ON daily_sentiment_reports (report_date, website_id, session_id);
```

### Tạo trigger để tự động cập nhật updated_at

```sql
-- Tạo function để cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Tạo trigger
CREATE TRIGGER update_daily_sentiment_reports_updated_at 
    BEFORE UPDATE ON daily_sentiment_reports 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

## Mô tả các trường dữ liệu

### Thông tin cơ bản
- **id**: Primary key tự động tăng
- **report_date**: Ngày báo cáo (DATE)
- **website_id**: ID của website (VARCHAR)
- **session_id**: ID của phiên hội thoại (VARCHAR)

### Thông tin người dùng
- **user_nickname**: Tên hiển thị của người dùng (VARCHAR)
- **user_id**: ID của người dùng (VARCHAR)

### Thông tin nhân viên
- **agent_name**: Tên của nhân viên hỗ trợ (VARCHAR)
- **agent_role**: Vai trò của nhân viên (VARCHAR, mặc định 'operator')

### Thời gian hội thoại
- **conversation_start**: Thời gian bắt đầu hội thoại (BIGINT - Unix timestamp)
- **conversation_end**: Thời gian kết thúc hội thoại (BIGINT - Unix timestamp)
- **is_completed**: Trạng thái hoàn thành hội thoại (BOOLEAN)

### Kết quả phân tích
- **overall_emotion**: Cảm xúc tổng thể (VARCHAR)
- **sentiment_shift**: Sự thay đổi cảm xúc (TEXT)
- **latest_sentiment**: Cảm xúc mới nhất (VARCHAR)

### Metadata
- **message_count**: Số lượng tin nhắn trong hội thoại (INTEGER)
- **analysis_timestamp**: Thời gian phân tích (BIGINT - Unix timestamp)
- **created_at**: Thời gian tạo bản ghi (TIMESTAMP)
- **updated_at**: Thời gian cập nhật bản ghi (TIMESTAMP)

## Lưu ý quan trọng

1. **Append-only**: Dữ liệu được thêm vào chứ không xóa hay thay đổi dữ liệu cũ
2. **Timestamps**: Sử dụng Unix timestamp (BIGINT) cho conversation_start, conversation_end, analysis_timestamp
3. **Indexing**: Các index được tạo để tối ưu hiệu suất truy vấn
4. **Auto-update**: Trigger tự động cập nhật updated_at khi có thay đổi

## Câu lệnh truy vấn mẫu

### Lấy dữ liệu theo ngày
```sql
SELECT * FROM daily_sentiment_reports 
WHERE report_date = '2024-01-15'
ORDER BY created_at DESC;
```

### Lấy dữ liệu theo website và ngày
```sql
SELECT * FROM daily_sentiment_reports 
WHERE report_date = '2024-01-15' 
AND website_id = 'website123'
ORDER BY session_id, agent_name;
```

### Thống kê theo cảm xúc
```sql
SELECT overall_emotion, COUNT(*) as count
FROM daily_sentiment_reports 
WHERE report_date = '2024-01-15'
GROUP BY overall_emotion
ORDER BY count DESC;
```

### Thống kê theo agent
```sql
SELECT agent_name, agent_role, COUNT(*) as conversations
FROM daily_sentiment_reports 
WHERE report_date = '2024-01-15'
GROUP BY agent_name, agent_role
ORDER BY conversations DESC;
```

"""
Website ID Validator
-------------------
This module provides functionality to validate website IDs against a whitelist.
It also tracks and logs unauthorized website IDs for reporting purposes.
"""

import os
import json
import logging
import threading
import time
from datetime import datetime
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class WebsiteValidator:
    """
    Validator for website IDs using a whitelist loaded from a file.

    This class provides functionality to:
    1. Load a whitelist of allowed website IDs from a file
    2. Validate website IDs against the whitelist
    3. Reload the whitelist when needed
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the website validator with configuration.

        Args:
            config: Configuration dictionary containing security settings
        """
        self.config = config.get('security', {}).get('website_id_whitelist', {})
        self.enabled = self.config.get('enabled', False)
        self.whitelist_file = self.config.get('whitelist_file', 'config/allowed_websites.json')
        self.reload_after_save = self.config.get('reload_after_save', True)

        # File to store unauthorized website IDs
        self.unauthorized_file = self.config.get('unauthorized_file', 'logs/unauthorized_websites.json')

        # Initialize whitelist
        self.allowed_websites = []
        self.last_loaded = 0
        self.last_file_modified = 0
        self._lock = threading.RLock()

        # Track unauthorized website IDs
        self.unauthorized_websites = {}  # Dictionary to track unauthorized website IDs and their access counts
        self.unauthorized_lock = threading.RLock()

        # Load the initial whitelist
        self.reload_whitelist()

        # Load existing unauthorized websites if the file exists
        self._load_unauthorized_websites()

        logger.info(f"Website ID validation {'enabled' if self.enabled else 'disabled'}")
        if self.enabled:
            logger.info(f"Loaded {len(self.allowed_websites)} allowed website IDs from {self.whitelist_file}")
            logger.info(f"Unauthorized website IDs will be logged to {self.unauthorized_file}")

    def reload_whitelist(self) -> bool:
        """
        Reload the whitelist from the file.

        Returns:
            bool: True if the whitelist was reloaded, False otherwise
        """
        if not self.enabled:
            return False

        with self._lock:
            try:
                # Check if the file exists
                if not os.path.exists(self.whitelist_file):
                    logger.warning(f"Whitelist file not found: {self.whitelist_file}")
                    return False

                # Check if the file has been modified since last load
                current_mtime = os.path.getmtime(self.whitelist_file)
                if current_mtime <= self.last_file_modified and self.last_loaded > 0:
                    # File hasn't changed, no need to reload
                    return False

                # Load the whitelist
                with open(self.whitelist_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Extract the allowed websites
                websites = data.get('allowed_websites', [])
                if not isinstance(websites, list):
                    logger.error(f"Invalid whitelist format in {self.whitelist_file}: 'allowed_websites' must be a list")
                    return False

                # Update the whitelist
                self.allowed_websites = websites
                self.last_loaded = time.time()
                self.last_file_modified = current_mtime

                # Log the reload
                logger.info(f"Reloaded whitelist with {len(self.allowed_websites)} website IDs from {self.whitelist_file}")
                logger.debug(f"Allowed websites: {', '.join(self.allowed_websites)}")

                return True
            except Exception as e:
                logger.error(f"Error reloading whitelist: {e}")
                return False

    def is_website_allowed(self, website_id: str) -> bool:
        """
        Check if a website ID is allowed.

        Args:
            website_id: The website ID to check

        Returns:
            bool: True if the website ID is allowed, False otherwise
        """
        if not self.enabled:
            # If validation is disabled, all websites are allowed
            return True

        with self._lock:
            return website_id in self.allowed_websites

    def _load_unauthorized_websites(self):
        """
        Load unauthorized websites from the file if it exists.
        """
        if not os.path.exists(self.unauthorized_file):
            logger.debug(f"Unauthorized websites file not found: {self.unauthorized_file}")
            return

        try:
            with open(self.unauthorized_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            with self.unauthorized_lock:
                self.unauthorized_websites = data.get('unauthorized_websites', {})

            logger.info(f"Loaded {len(self.unauthorized_websites)} unauthorized website IDs from {self.unauthorized_file}")
        except Exception as e:
            logger.error(f"Error loading unauthorized websites: {e}")

    def _save_unauthorized_websites(self):
        """
        Save unauthorized websites to the file.
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.unauthorized_file), exist_ok=True)

            with self.unauthorized_lock:
                data = {
                    'unauthorized_websites': self.unauthorized_websites,
                    'last_updated': datetime.now().isoformat()
                }

                with open(self.unauthorized_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(self.unauthorized_websites)} unauthorized website IDs to {self.unauthorized_file}")
        except Exception as e:
            logger.error(f"Error saving unauthorized websites: {e}")

    def track_unauthorized_website(self, website_id: str):
        """
        Track an unauthorized website ID.

        Args:
            website_id: The unauthorized website ID to track
        """
        with self.unauthorized_lock:
            # Get current timestamp
            current_time = datetime.now().isoformat()

            # Update or add the website ID in the tracking dictionary
            if website_id in self.unauthorized_websites:
                # Update existing entry
                entry = self.unauthorized_websites[website_id]
                entry['access_count'] = entry.get('access_count', 0) + 1
                entry['last_access'] = current_time
            else:
                # Add new entry
                self.unauthorized_websites[website_id] = {
                    'first_access': current_time,
                    'last_access': current_time,
                    'access_count': 1
                }

            # Save the updated tracking data
            self._save_unauthorized_websites()

    def validate_website_id(self, website_id: str) -> bool:
        """
        Validate a website ID against the whitelist.

        Args:
            website_id: The website ID to validate

        Returns:
            bool: True if the website ID is valid, False otherwise
        """
        if not website_id:
            logger.warning("Empty website ID provided for validation")
            return False

        is_allowed = self.is_website_allowed(website_id)

        if not is_allowed:
            logger.warning(f"Website ID '{website_id}' is not in the allowed list")
            # Track the unauthorized website ID
            self.track_unauthorized_website(website_id)

        return is_allowed

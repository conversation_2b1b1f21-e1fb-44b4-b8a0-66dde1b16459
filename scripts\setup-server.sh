#!/bin/bash

# Server Setup Script for Sentiment Analysis CI/CD
# Usage: ./setup-server.sh [staging|production]

set -euo pipefail

# Configuration
ENVIRONMENT=${1:-staging}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    error "Please do not run this script as root. Use a regular user with sudo privileges."
fi

# Check if environment is valid
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    error "Invalid environment. Use 'staging' or 'production'"
fi

log "Setting up $ENVIRONMENT server for Sentiment Analysis CI/CD"

# Update system packages
log "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential packages
log "Installing essential packages..."
sudo apt install -y \
    curl \
    wget \
    git \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    htop \
    vim \
    ufw \
    fail2ban \
    logrotate

# Install Docker
log "Installing Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    rm get-docker.sh
    success "Docker installed successfully"
else
    log "Docker is already installed"
fi

# Install Docker Compose
log "Installing Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_VERSION="v2.20.0"
    sudo curl -L "https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    success "Docker Compose installed successfully"
else
    log "Docker Compose is already installed"
fi

# Install Node.js (for k6 and other tools)
log "Installing Node.js..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    success "Node.js installed successfully"
else
    log "Node.js is already installed"
fi

# Install k6 for performance testing
log "Installing k6..."
if ! command -v k6 &> /dev/null; then
    sudo gpg -k
    sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
    echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
    sudo apt-get update
    sudo apt-get install k6
    success "k6 installed successfully"
else
    log "k6 is already installed"
fi

# Setup firewall
log "Configuring firewall..."
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Environment-specific ports
if [ "$ENVIRONMENT" = "staging" ]; then
    sudo ufw allow 8080/tcp  # Staging nginx
    sudo ufw allow 3001/tcp  # Staging Grafana
    sudo ufw allow 9091/tcp  # Staging Prometheus
elif [ "$ENVIRONMENT" = "production" ]; then
    sudo ufw allow 3000/tcp  # Production Grafana
    sudo ufw allow 9090/tcp  # Production Prometheus
fi

# Enable firewall
sudo ufw --force enable
success "Firewall configured successfully"

# Setup fail2ban
log "Configuring fail2ban..."
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
success "fail2ban configured successfully"

# Create application directory
APP_DIR="/opt/sentiment-analysis"
log "Creating application directory: $APP_DIR"
sudo mkdir -p "$APP_DIR"
sudo chown $USER:$USER "$APP_DIR"

# Create necessary subdirectories
mkdir -p "$APP_DIR"/{data,logs,backups,nginx/ssl,monitoring}
mkdir -p "$APP_DIR/backups"/{postgres,redis,configs}

# Setup log rotation
log "Setting up log rotation..."
sudo tee /etc/logrotate.d/sentiment-analysis > /dev/null << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        docker-compose -f $APP_DIR/docker-compose.$ENVIRONMENT.yml restart nginx || true
    endscript
}
EOF

# Setup backup script
log "Creating backup script..."
tee "$APP_DIR/backup.sh" > /dev/null << 'EOF'
#!/bin/bash

# Backup script for Sentiment Analysis
BACKUP_DIR="/opt/sentiment-analysis/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create backup directories
mkdir -p "$BACKUP_DIR/postgres" "$BACKUP_DIR/redis" "$BACKUP_DIR/configs"

# Backup PostgreSQL
echo "Backing up PostgreSQL..."
docker-compose exec -T postgres pg_dump -U postgres sentiment_analysis > "$BACKUP_DIR/postgres/backup_$DATE.sql"

# Backup Redis
echo "Backing up Redis..."
docker-compose exec -T redis redis-cli --rdb - > "$BACKUP_DIR/redis/backup_$DATE.rdb"

# Backup configurations
echo "Backing up configurations..."
tar -czf "$BACKUP_DIR/configs/config_backup_$DATE.tar.gz" \
    config/ \
    postgresql_config*.yaml \
    docker-compose*.yml \
    nginx/ \
    monitoring/

# Cleanup old backups
echo "Cleaning up old backups..."
find "$BACKUP_DIR" -name "backup_*" -mtime +$RETENTION_DAYS -delete

echo "Backup completed: $DATE"
EOF

chmod +x "$APP_DIR/backup.sh"

# Setup cron job for backups
log "Setting up backup cron job..."
(crontab -l 2>/dev/null; echo "0 2 * * * $APP_DIR/backup.sh >> $APP_DIR/logs/backup.log 2>&1") | crontab -

# Setup monitoring script
log "Creating monitoring script..."
tee "$APP_DIR/monitor.sh" > /dev/null << 'EOF'
#!/bin/bash

# Simple monitoring script
APP_DIR="/opt/sentiment-analysis"
LOG_FILE="$APP_DIR/logs/monitor.log"

# Function to log with timestamp
log_message() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Check if containers are running
check_containers() {
    local compose_file="$1"
    local failed_services=""
    
    while IFS= read -r service; do
        if ! docker-compose -f "$compose_file" ps "$service" | grep -q "Up"; then
            failed_services="$failed_services $service"
        fi
    done < <(docker-compose -f "$compose_file" config --services)
    
    if [ -n "$failed_services" ]; then
        log_message "ERROR: Failed services:$failed_services"
        return 1
    else
        log_message "INFO: All services are running"
        return 0
    fi
}

# Check disk space
check_disk_space() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$usage" -gt 80 ]; then
        log_message "WARNING: Disk usage is ${usage}%"
        return 1
    else
        log_message "INFO: Disk usage is ${usage}%"
        return 0
    fi
}

# Check memory usage
check_memory() {
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$usage" -gt 80 ]; then
        log_message "WARNING: Memory usage is ${usage}%"
        return 1
    else
        log_message "INFO: Memory usage is ${usage}%"
        return 0
    fi
}

# Main monitoring
log_message "Starting health check"

# Determine compose file based on environment
if [ -f "$APP_DIR/docker-compose.staging.yml" ] && docker-compose -f "$APP_DIR/docker-compose.staging.yml" ps | grep -q "Up"; then
    COMPOSE_FILE="$APP_DIR/docker-compose.staging.yml"
elif [ -f "$APP_DIR/docker-compose.production.blue.yml" ] && docker-compose -f "$APP_DIR/docker-compose.production.blue.yml" ps | grep -q "Up"; then
    COMPOSE_FILE="$APP_DIR/docker-compose.production.blue.yml"
elif [ -f "$APP_DIR/docker-compose.production.green.yml" ] && docker-compose -f "$APP_DIR/docker-compose.production.green.yml" ps | grep -q "Up"; then
    COMPOSE_FILE="$APP_DIR/docker-compose.production.green.yml"
else
    log_message "ERROR: No active compose file found"
    exit 1
fi

# Run checks
check_containers "$COMPOSE_FILE"
check_disk_space
check_memory

log_message "Health check completed"
EOF

chmod +x "$APP_DIR/monitor.sh"

# Setup monitoring cron job
log "Setting up monitoring cron job..."
(crontab -l 2>/dev/null; echo "*/5 * * * * $APP_DIR/monitor.sh") | crontab -

# Create environment-specific configuration
log "Creating environment configuration..."
if [ "$ENVIRONMENT" = "staging" ]; then
    # Staging-specific setup
    tee "$APP_DIR/.env.staging" > /dev/null << 'EOF'
# Staging Environment Configuration
ENVIRONMENT=staging
POSTGRES_PASSWORD=staging_secure_password
REDIS_PASSWORD=staging_redis_password
GRAFANA_ADMIN_PASSWORD=staging_admin_password

# Staging URLs
STAGING_URL=https://staging.your-domain.com
EOF
else
    # Production-specific setup
    tee "$APP_DIR/.env.production" > /dev/null << 'EOF'
# Production Environment Configuration
ENVIRONMENT=production
POSTGRES_PASSWORD=production_secure_password
REDIS_PASSWORD=production_redis_password
GRAFANA_ADMIN_PASSWORD=production_admin_password

# Production URLs
PRODUCTION_URL=https://your-domain.com
EOF
fi

# Setup SSH key for deployment (if not exists)
if [ ! -f ~/.ssh/id_rsa ]; then
    log "Generating SSH key for deployment..."
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
    success "SSH key generated. Add the public key to your Git repository deploy keys:"
    cat ~/.ssh/id_rsa.pub
fi

# Setup systemd service for monitoring
log "Creating systemd service for monitoring..."
sudo tee /etc/systemd/system/sentiment-monitor.service > /dev/null << EOF
[Unit]
Description=Sentiment Analysis Monitoring Service
After=docker.service

[Service]
Type=oneshot
User=$USER
ExecStart=$APP_DIR/monitor.sh
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

sudo tee /etc/systemd/system/sentiment-monitor.timer > /dev/null << EOF
[Unit]
Description=Run Sentiment Analysis Monitoring every 5 minutes
Requires=sentiment-monitor.service

[Timer]
OnCalendar=*:0/5
Persistent=true

[Install]
WantedBy=timers.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable sentiment-monitor.timer
sudo systemctl start sentiment-monitor.timer

success "Server setup completed successfully!"

log "Next steps:"
log "1. Clone your repository to $APP_DIR"
log "2. Configure environment variables in $APP_DIR/.env.$ENVIRONMENT"
log "3. Setup SSL certificates (for production)"
log "4. Configure your CI/CD pipeline with the server details"
log "5. Test the deployment process"

log "Server information:"
log "- Application directory: $APP_DIR"
log "- Environment: $ENVIRONMENT"
log "- Backup script: $APP_DIR/backup.sh (runs daily at 2 AM)"
log "- Monitoring script: $APP_DIR/monitor.sh (runs every 5 minutes)"
log "- Log files: $APP_DIR/logs/"

if [ "$ENVIRONMENT" = "production" ]; then
    warning "IMPORTANT: This is a production server. Make sure to:"
    warning "1. Change all default passwords"
    warning "2. Setup proper SSL certificates"
    warning "3. Configure proper backup strategy"
    warning "4. Setup monitoring and alerting"
fi

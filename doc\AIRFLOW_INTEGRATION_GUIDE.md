# Apache Airflow Integration Guide

## Tổng quan

Hướng dẫn này mô tả cách tích hợp Apache Airflow với hệ thống Sentiment Analysis để quản lý và giám sát các workflows tự động.

## Kiến trúc

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Airflow       │    │   Sentiment     │    │   PostgreSQL    │
│   Server        │───▶│   Analysis      │───▶│   Database      │
│   (Scheduler)   │    │   Server        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DAG           │    │   Task          │    │   Monitoring    │
│   Management    │    │   Execution     │    │   & Alerting    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Tính năng chính

### 1. **Task Management**
- Tạo và quản lý tasks từ Airflow
- Theo dõi tiến trình thực thi
- Retry và error handling tự động

### 2. **Monitoring & Alerting**
- Health checks liên tục
- Performance monitoring
- Email và Slack notifications

### 3. **Scheduled Workflows**
- Daily sentiment analysis
- Batch processing
- Data cleanup
- Weekly reports

## Setup Instructions

### 1. <PERSON>ài đặt trên Sentiment Analysis Server

#### Bước 1: Cập nhật dependencies
```bash
# Thêm vào requirements.txt
pip install psutil  # For system monitoring
```

#### Bước 2: Cấu hình environment variables
```bash
# Thêm vào .env
AIRFLOW_API_TOKEN=your-secure-token-here
AIRFLOW_ENABLED=true
```

#### Bước 3: Khởi động server với Airflow endpoints
```bash
# Server sẽ tự động include Airflow endpoints
python run.py
```

### 2. Cài đặt trên Airflow Server

#### Bước 1: Copy DAG files
```bash
# Copy DAG files to Airflow DAGs directory
cp airflow/dags/* $AIRFLOW_HOME/dags/
```

#### Bước 2: Setup connections
```bash
# Run connection setup script
python airflow/config/airflow_connections.py
```

#### Bước 3: Setup variables
```bash
# Run variables setup script
python airflow/config/airflow_variables.py
```

#### Bước 4: Configure Airflow settings
```bash
# Update airflow.cfg
[webserver]
expose_config = True

[api]
auth_backend = airflow.api.auth.backend.basic_auth

[email]
email_backend = airflow.providers.sendgrid.utils.emailer.send_email
```

## API Endpoints

### Authentication
Tất cả endpoints yêu cầu Bearer token authentication:
```
Authorization: Bearer your-airflow-api-token
```

### Health Check
```http
GET /airflow/health
```

Response:
```json
{
  "status": "healthy",
  "services": {
    "postgresql": "healthy",
    "sentiment_analysis": "healthy"
  },
  "metrics": {
    "active_tasks": 2,
    "pending_tasks": 0,
    "cpu_usage": 45.2,
    "memory_usage": 67.8
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Create Task
```http
POST /airflow/tasks
Content-Type: application/json

{
  "task_type": "sentiment_analysis",
  "parameters": {
    "website_id": "example-site",
    "session_id": "session-123",
    "messages": [
      {"role": "user", "content": "Hello"},
      {"role": "operator", "content": "Hi there!"}
    ]
  },
  "priority": 1,
  "timeout": 3600
}
```

### Get Task Status
```http
GET /airflow/tasks/{task_id}
```

### List Tasks
```http
GET /airflow/tasks?status=running&limit=50
```

### Batch Processing
```http
POST /airflow/batch-process
Content-Type: application/json

{
  "website_ids": ["site1", "site2"],
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-02"
  },
  "processing_type": "daily_sentiment",
  "batch_size": 200
}
```

### Get Metrics
```http
GET /airflow/metrics
```

## DAG Configurations

### 1. Daily Sentiment Analysis DAG

**File**: `sentiment_analysis_dag.py`
**Schedule**: Daily at 2:00 AM
**Tasks**:
- Health check
- Daily report generation
- Batch processing (high-volume sites)
- Data cleanup
- Summary notification

### 2. Monitoring DAG

**File**: `sentiment_monitoring_dag.py`
**Schedule**: Every 5 minutes
**Tasks**:
- System metrics check
- Task performance analysis
- Alert generation
- Email notifications

### 3. Weekly Report DAG

**Schedule**: Every Monday at 9:00 AM
**Tasks**:
- Weekly performance analysis
- Report generation
- Email distribution

## Task Types

### 1. Sentiment Analysis
```python
{
  "task_type": "sentiment_analysis",
  "parameters": {
    "website_id": "string",
    "session_id": "string", 
    "messages": [{"role": "user|operator", "content": "string"}]
  }
}
```

### 2. Batch Sentiment Analysis
```python
{
  "task_type": "batch_sentiment_analysis",
  "parameters": {
    "website_ids": ["string"],
    "date_range": {"start_date": "YYYY-MM-DD", "end_date": "YYYY-MM-DD"},
    "batch_size": 200
  }
}
```

### 3. Daily Report Generation
```python
{
  "task_type": "daily_report_generation",
  "parameters": {
    "target_date": "YYYY-MM-DD",
    "website_ids": ["string"] // optional, empty = all websites
  }
}
```

### 4. Data Cleanup
```python
{
  "task_type": "data_cleanup",
  "parameters": {
    "cleanup_type": "old_logs|temp_files|old_conversations|all",
    "retention_days": 30,
    "dry_run": false
  }
}
```

## Monitoring & Alerting

### Health Checks
- **System Status**: Overall health của sentiment analysis system
- **Service Status**: PostgreSQL, Redis, API services
- **Resource Usage**: CPU, Memory, Disk usage
- **Task Metrics**: Active, pending, failed tasks

### Alert Thresholds
- CPU usage > 80%
- Memory usage > 85%
- Disk usage > 90%
- Failed tasks > 5
- Long-running tasks > 1 hour

### Notification Channels
- **Email**: Critical alerts và weekly reports
- **Slack**: Real-time notifications (optional)
- **Airflow UI**: Dashboard monitoring

## Configuration Variables

### Required Variables
```bash
SENTIMENT_API_BASE_URL=http://your-sentiment-server:8000
AIRFLOW_API_TOKEN=your-secure-token
ALERT_EMAIL=<EMAIL>
```

### Optional Variables
```bash
HIGH_VOLUME_WEBSITES=site1,site2,site3
BATCH_SIZE=200
SLACK_WEBHOOK_URL=https://hooks.slack.com/...
ENVIRONMENT=production
```

## Security Considerations

### 1. Authentication
- Sử dụng strong API tokens
- Rotate tokens định kỳ
- Restrict network access

### 2. Network Security
- HTTPS cho production
- Firewall rules
- VPN access nếu cần

### 3. Data Protection
- Encrypt sensitive data
- Secure database connections
- Audit logging

## Troubleshooting

### Common Issues

#### 1. Connection Refused
```
Error: Connection refused to sentiment analysis server
```
**Solution**:
- Check server is running
- Verify network connectivity
- Check firewall rules

#### 2. Authentication Failed
```
Error: Invalid authentication token
```
**Solution**:
- Verify AIRFLOW_API_TOKEN
- Check token expiry
- Update Airflow variables

#### 3. Task Timeout
```
Error: Task exceeded timeout limit
```
**Solution**:
- Increase timeout value
- Check system resources
- Optimize task parameters

#### 4. High Memory Usage
```
Warning: Memory usage > 85%
```
**Solution**:
- Reduce batch size
- Limit concurrent tasks
- Add more memory

### Debugging

#### Check Airflow Logs
```bash
# Airflow task logs
tail -f $AIRFLOW_HOME/logs/dag_id/task_id/execution_date/1.log

# Airflow scheduler logs
tail -f $AIRFLOW_HOME/logs/scheduler/latest/scheduler.log
```

#### Check Sentiment Analysis Logs
```bash
# Application logs
tail -f logs/app.log

# Airflow integration logs
grep "airflow" logs/app.log
```

#### Test API Connectivity
```bash
# Test health endpoint
curl -H "Authorization: Bearer your-token" \
     http://your-sentiment-server:8000/airflow/health

# Test task creation
curl -X POST \
     -H "Authorization: Bearer your-token" \
     -H "Content-Type: application/json" \
     -d '{"task_type":"sentiment_analysis","parameters":{...}}' \
     http://your-sentiment-server:8000/airflow/tasks
```

## Performance Optimization

### 1. Task Optimization
- Adjust batch sizes based on system capacity
- Use appropriate timeouts
- Implement proper retry logic

### 2. Resource Management
- Monitor system resources
- Scale horizontally if needed
- Optimize database queries

### 3. Scheduling Optimization
- Avoid peak hours for heavy tasks
- Distribute load across time
- Use appropriate DAG intervals

## Best Practices

### 1. DAG Design
- Keep DAGs simple và focused
- Use appropriate dependencies
- Implement proper error handling

### 2. Monitoring
- Set up comprehensive alerts
- Monitor key metrics
- Regular health checks

### 3. Maintenance
- Regular cleanup of old data
- Update dependencies
- Monitor performance trends

## Support

### Documentation
- [Airflow Documentation](https://airflow.apache.org/docs/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Project API Documentation](http://your-server:8000/docs)

### Contact
- Technical Support: <EMAIL>
- DevOps Team: <EMAIL>
- On-call: +1-xxx-xxx-xxxx

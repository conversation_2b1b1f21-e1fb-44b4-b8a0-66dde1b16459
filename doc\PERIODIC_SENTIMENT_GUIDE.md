# Hướng dẫn sử dụng hệ thống Daily Sentiment Analysis & MongoDB Upload

## Tổng quan

Hệ thống Daily Sentiment Analysis tự động thực hiện phân tích cảm xúc hàng ngày và upload kết quả lên MongoDB. Hệ thống được đơn giản hóa để chỉ tập trung vào việc tạo báo cáo hàng ngày và đồng bộ dữ liệu lên MongoDB.

## Cấu hình

### File cấu hình: `config/config.yaml`

```yaml
# Periodic Sentiment Analysis Configuration
periodic_sentiment:
  # Bật/tắt xử lý hàng ngày
  enabled: true

  # Xử lý hàng ngày (đơn giản hóa)
  periodic_statistics:
    - 1440  # Mỗi 24 giờ (hàng ngày)

  # Cấu hình báo cáo hàng đêm
  nightly_report:
    start_nightly_time: "18:00"  # 6:00 PM - Thời gian b<PERSON>t đầu thu thập dữ liệu
    end_nightly_time: "06:00"    # 6:00 AM - Thời gian kết thúc thu thập dữ liệu
    timezone: "Asia/Ho_Chi_Minh" # Múi giờ

  # Cấu hình MongoDB
  mongodb:
    enabled: true
    connection_string: "mongodb://localhost:27017/"
    database_name: "sentiment_analysis"
    collection_name: "daily_reports"
    upload_schedule: "daily"
    upload_time: "19:00"  # Upload sau khi tạo báo cáo
    retry_attempts: 3
    retry_delay_seconds: 60

  # Đường dẫn lưu trữ local (backup)
  storage:
    conversation_tracking_path: "data/conversation_tracking.json"
    backup_reports_path: "data/backup_reports/"

  # Cài đặt xử lý
  processing:
    min_messages_for_analysis: 2      # Số tin nhắn tối thiểu để phân tích
    max_conversation_age_hours: 168   # Tuổi tối đa của cuộc hội thoại (7 ngày)
    batch_size: 50                    # Số cuộc hội thoại xử lý cùng lúc
    conversation_timeout_sec: 30      # Timeout cho mỗi cuộc hội thoại
    detailed_logging: false           # Bật logging chi tiết
```

## Cách thức hoạt động

### 1. Báo cáo hàng đêm (Nightly Reports)

- **Mục đích**: Tổng hợp và phân tích cảm xúc cho tất cả cuộc hội thoại trong khoảng thời gian từ `start_nightly_time` đến `end_nightly_time`
- **Tần suất**: Hàng ngày vào lúc `start_nightly_time` (mặc định 18:00)
- **Nội dung báo cáo**:
  - Cuộc hội thoại cũ chưa kết thúc tại `start_nightly_time`
  - Cuộc hội thoại mới trong khoảng `start_nightly_time` đến `end_nightly_time`
  - Thông tin đầy đủ: nội dung, session_id, website_id, user info, agent info
  - Ba loại phân tích cảm xúc: overall_emotion, sentiment_shift, latest_sentiment
- **Xử lý**: Phân tích cảm xúc real-time cho từng cuộc hội thoại trong báo cáo

### 2. Upload MongoDB

- **Mục đích**: Tự động upload báo cáo hàng ngày lên MongoDB
- **Tần suất**: Hàng ngày vào lúc `upload_time` (mặc định 19:00, sau khi tạo báo cáo)
- **Tính năng**:
  - Retry mechanism với configurable attempts
  - Backup local trước khi upload
  - Upsert để tránh duplicate
  - Connection management và error handling

### 3. Theo dõi cuộc hội thoại (Conversation Tracking)

- **Mục đích**: Theo dõi trạng thái và lịch sử phân tích của từng cuộc hội thoại
- **Thông tin lưu trữ**:
  - Timestamp tin nhắn cuối cùng
  - Timestamp phân tích cảm xúc cuối cùng
  - Số lượng tin nhắn
  - Trạng thái hoàn thành
- **Kết quả**: Lưu vào `data/conversation_tracking.json`

## Cấu trúc dữ liệu

### 1. MongoDB Collection Structure

**Database**: `sentiment_analysis`
**Collection**: `daily_reports`

```json
[
  {
    "processing_id": "uuid-string",
    "processing_timestamp": 1747214400,
    "processing_interval_minutes": 60,
    "conversations_processed": 25,
    "conversations_analyzed": 15,
    "conversations_skipped": 10,
    "processing_duration_seconds": 45.2,
    "results": [
      {
        "session_id": "session_123",
        "website_id": "website_456",
        "user": {
          "nickname": "John Doe",
          "user_id": "user_789"
        },
        "agents": [
          {
            "name": "Support Agent",
            "role": "operator"
          }
        ],
        "conversation_start": 1747210000,
        "conversation_end": null,
        "messages": [...],
        "sentiment_analysis": {
          "overall_emotion": "satisfaction",
          "sentiment_shift": "neutral > satisfaction",
          "latest_sentiment": "satisfaction"
        },
        "analysis_timestamp": 1747214400,
        "message_count": 8,
        "is_completed": false
      }
    ]
  }
]
```

### 2. File báo cáo hàng đêm

**Đường dẫn**: `data/nightly_reports/nightly_report_2025-01-15.json`

```json
{
  "report_id": "nightly_2025-01-15_1747214400",
  "report_date": "2025-01-15",
  "generation_timestamp": 1747214400,
  "start_nightly_time": "18:00",
  "end_nightly_time": "06:00",
  "total_conversations": 50,
  "completed_conversations": 30,
  "ongoing_conversations": 20,
  "total_messages": 500,
  "sentiment_distribution": {
    "anger": 5,
    "frustration": 8,
    "anxiety": 3,
    "neutral": 20,
    "satisfaction": 12,
    "unknown": 2
  },
  "conversations": [...],
  "processing_duration_seconds": 120.5,
  "data_collection_period": "2025-01-14 18:00 to 2025-01-15 06:00"
}
```

### 3. File theo dõi cuộc hội thoại

**Đường dẫn**: `data/conversation_tracking.json`

```json
{
  "website_456_session_123": {
    "session_id": "session_123",
    "website_id": "website_456",
    "last_message_timestamp": 1747214000,
    "last_sentiment_analysis_timestamp": 1747214400,
    "message_count": 8,
    "is_completed": false,
    "completion_timestamp": null
  }
}
```

## Cách truy cập kết quả

### 1. Xem kết quả xử lý định kỳ mới nhất

```bash
# Xem file JSON trực tiếp
cat data/periodic_sentiment_results.json | jq '.[-1]'  # Kết quả mới nhất
```

### 2. Xem báo cáo hàng đêm

```bash
# Liệt kê tất cả báo cáo
ls data/nightly_reports/

# Xem báo cáo ngày hôm nay
cat data/nightly_reports/nightly_report_$(date +%Y-%m-%d).json | jq '.'

# Xem báo cáo ngày cụ thể
cat data/nightly_reports/nightly_report_2025-01-15.json | jq '.'
```

### 3. Xem thống kê tổng quan

```bash
# Xem số lượng cuộc hội thoại theo cảm xúc từ báo cáo mới nhất
cat data/nightly_reports/nightly_report_$(date +%Y-%m-%d).json | jq '.sentiment_distribution'

# Xem tổng số cuộc hội thoại và tin nhắn
cat data/nightly_reports/nightly_report_$(date +%Y-%m-%d).json | jq '{total_conversations, total_messages, completed_conversations, ongoing_conversations}'
```

## Logs và Monitoring

### Xem logs hệ thống

```bash
# Xem logs chung
tail -f logs/app.log

# Lọc logs liên quan đến periodic processing
tail -f logs/app.log | grep -i "periodic\|nightly\|scheduler"
```

### Kiểm tra trạng thái hoạt động

- Hệ thống sẽ log thông tin khi bắt đầu và kết thúc mỗi lần xử lý
- Kiểm tra file `data/conversation_tracking.json` để xem cuộc hội thoại nào đã được phân tích
- Kiểm tra thư mục `data/nightly_reports/` để xem báo cáo hàng ngày

## Tùy chỉnh cấu hình

### Thay đổi tần suất xử lý

Chỉnh sửa `periodic_statistics` trong `config/config.yaml`:

```yaml
periodic_statistics:
  - 15    # Mỗi 15 phút
  - 30    # Mỗi 30 phút
  - 120   # Mỗi 2 giờ
```

### Thay đổi thời gian báo cáo hàng đêm

```yaml
nightly_report:
  start_nightly_time: "20:00"  # Bắt đầu lúc 8:00 PM
  end_nightly_time: "08:00"    # Kết thúc lúc 8:00 AM
```

### Tắt hệ thống periodic

```yaml
periodic_sentiment:
  enabled: false
```

## Troubleshooting

### Hệ thống không tạo báo cáo

1. Kiểm tra `enabled: true` trong config
2. Kiểm tra logs có lỗi không
3. Kiểm tra quyền ghi file trong thư mục `data/`

### Báo cáo thiếu dữ liệu

1. Kiểm tra `min_messages_for_analysis` - có thể quá cao
2. Kiểm tra `max_conversation_age_hours` - có thể quá thấp
3. Kiểm tra thời gian `start_nightly_time` và `end_nightly_time`

### Performance issues

1. Giảm `batch_size` nếu xử lý chậm
2. Tăng `conversation_timeout_sec` nếu bị timeout
3. Tăng khoảng thời gian trong `periodic_statistics` để giảm tần suất xử lý
